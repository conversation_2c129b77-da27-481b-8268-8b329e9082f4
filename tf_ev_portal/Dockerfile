FROM node:16-alpine as build
COPY frontend /tmp/src
RUN cd /tmp/src \
    && yarn install --prod --silent --no-progress --non-interactive \
    && yarn build

FROM alpine:latest
COPY nginx /opt/nginx
COPY --from=build /tmp/src/build /opt/nginx/content/default
RUN apk add --no-cache --no-progress --quiet curl gettext nginx nginx-mod-http-headers-more nginx-mod-http-js \
    && apk add --no-cache --no-progress --quiet --virtual .build shadow coreutils \
    && find / -user nginx -exec chown -h 1000 {} \; \
    && find / -group nginx -exec chgrp -h 1000 {} \; \
    && groupmod -g 1000 nginx \
    && usermod -u 1000 nginx \
    && apk del --no-progress --quiet .build \
    && mkdir -p /opt/nginx/logs /opt/nginx/content/aral /opt/nginx/content/bp \
    && find /opt/nginx/content/default -maxdepth 1 \( -type f -o -name static \) -exec cp -r {} /opt/nginx/content/aral/ + -exec cp -r {} /opt/nginx/content/bp/ + \
    && mv -f /opt/nginx/content/default/aral/* /opt/nginx/content/aral/ \
    && mv -f /opt/nginx/content/default/bp/* /opt/nginx/content/bp/ \
    && rm -rf /opt/nginx/content/default/aral /opt/nginx/content/default/bp \
    && chown -R nginx:nginx /opt/nginx /tmp \
    && chmod 500 /opt/nginx/entrypoint.sh

USER nginx

HEALTHCHECK --interval=10s CMD test `curl --user-agent Healthcheck --silent --write-out '%{http_code}' --output /dev/null http://localhost:8080/healthcheck` -eq 200
ENTRYPOINT [ "/opt/nginx/entrypoint.sh" ]
