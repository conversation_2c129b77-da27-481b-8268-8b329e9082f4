const app = require('express')();
const cors = require('cors');
const axios = require('axios');
const bodyParser = require('body-parser');
require('dotenv').config();

app.use(cors('*'));
app.use(bodyParser.json());

app.use(`/idp`, async (req, res) => {
  try {
    const data = req.body;
    const response = await axios({
      method: req.method,
      url: `${process.env.IDP_URL}${req.url}`,
      data,
      headers: {
        ...req.headers,
        host: 'localhost:16060',
      },
    });
    res.status(response.status).send(response.data);
  } catch (err) {
    console.error(err.message);
    if (err.response) {
      res.status(err.response.status);
      res.send(err.response.data);
    } else {
      res.status(500);
      res.send(err.message);
    }
  }
});
app.use(async (req, res) => {
  try {
    const data = req.body;
    const { role, user_id } = req.headers;
    const response = await axios({
      method: req.method,
      url: `${process.env.ESB_URL}${req.originalUrl}`,
      data,
      headers: {
        role,
        user_id,
      },
    });
    res.status(response.status).send(response.data);
  } catch (err) {
    console.error(err.message);
    if (err.response) {
      res.status(err.response.status);
      res.send(err.response.data);
    } else {
      res.status(500);
      res.send(err.message);
    }
  }
});

app.listen(process.env.PORT, () =>
  console.log(`Development proxy server startet on port ${process.env.PORT}🚀`),
);
