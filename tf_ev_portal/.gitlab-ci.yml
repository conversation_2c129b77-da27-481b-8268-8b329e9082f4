stages:
  - Test & Security
  - Clean
  - Build
  - Demo
  - Deploy
  - Push

include:
  - project: xintegrate/intern/gitlab
    file: /includes/basic-ci.yml
  - project: xintegrate/intern/gitlab
    file: /includes/nodejs-ci.yml

variables:
  COMPOSE_FILE: /opt/reimbursement/docker-compose.yml
  ENV: $CI_COMMIT_BRANCH
  HOST: trafineo-portal-$CI_COMMIT_BRANCH

workflow:
  rules:
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "qa"'
    - if: '$CI_COMMIT_BRANCH == "staging"'
    - if: '$CI_COMMIT_BRANCH == "master"'
    - if: '$CI_COMMIT_BRANCH == "demo"'
    - when: never

.staging1: &staging1
  variables:
    HOST: trafineo-portal-staging1
  rules:
    - if: '$CI_COMMIT_BRANCH == "staging"'

.staging2: &staging2
  variables:
    HOST: trafineo-portal-staging2
  rules:
    - if: '$CI_COMMIT_BRANCH == "staging"'

.prod1: &prod1
  variables:
    ENV: prod
    HOST: trafineo-portal-prod1
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

.prod2: &prod2
  variables:
    ENV: prod
    HOST: trafineo-portal-prod2
  rules:
    - if: '$CI_COMMIT_BRANCH == "master"'

Security Preview:
  extends: .nodejs-audit
  variables:
    CODE_DIR: frontend
  allow_failure: true

Linting:
  extends: .nodejs-lint
  variables:
    CODE_DIR: frontend

Test:
  extends: .nodejs
  image: cypress/base:16.14.2
  script:
    - cd frontend
    - yarn install --silent --no-progress --frozen-lockfile --cache-folder .yarn-cache --prefer-offline
    - yarn test
  artifacts:
     reports:
       junit: frontend/junit.xml
     expire_in: 1 week
  allow_failure: true

Clean:
  extends: .docker-cleanup
  needs: []
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "qa"'
    - if: '$CI_COMMIT_BRANCH == "demo"'

Clean Staging1:
  extends: .docker-cleanup
  needs: []
  <<: *staging1

Clean Staging2:
  extends: .docker-cleanup
  needs: []
  <<: *staging2

Clean Prod1:
  extends: .docker-cleanup
  needs: []
  <<: *prod1

Clean Prod2:
  extends: .docker-cleanup
  needs: []
  <<: *prod2

Build:
  extends: .build
  variables:
    SERVICE: frontend
    DOCKER_BUILD_LOCATION: "."
  needs: []

Deploy:
  extends: .deploy
  script:
    - envsubst '${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}' < docker-compose.template.yml > docker-compose.yml
    - scp -F ${SSH_CONFIG} docker-compose.yml ${HOST}:${COMPOSE_FILE}
    - scp -F ${SSH_CONFIG} ${ENV}.env ${HOST}:/opt/reimbursement/.env
    - ssh -F ${SSH_CONFIG} ${HOST} 'mkdir -p /opt/reimbursement/nginx/conf.d/maintenance'
  rules:
    - if: '$CI_COMMIT_BRANCH == "develop"'
    - if: '$CI_COMMIT_BRANCH == "qa"'

Deploy Staging1:
  extends: Deploy
  <<: *staging1

Deploy Staging2:
  extends: Deploy
  needs: ["Deploy Staging1"]
  <<: *staging2

Deploy to Prod1:
  extends: Deploy
  <<: *prod1

Deploy to Prod2:
  extends: Deploy
  needs: ["Deploy to Prod1"]
  <<: *prod2

Push to Bitbucket:
  extends: .push external git
  variables:
    EXTERNAL_GIT_URL: ${BITBUCKET_URL}/portal.git

# Demo

Build demo:
  extends: .build
  variables:
    SERVICE: demo
    DOCKER_BUILD_LOCATION: "."
    DOCKER_BUILD_ARGS: -f demo/Dockerfile
  needs: []
  rules:
    - if: '$CI_COMMIT_BRANCH == "demo"'

Deploy demo:
  stage: Demo
  extends: .basic
  variables:
    ENV: develop
    HOST: trafineo-portal-develop
  script:
    - envsubst '${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}' < demo/docker-compose.template.yml > demo/docker-compose.yml
    - scp -F ${SSH_CONFIG} demo/docker-compose.yml ${HOST}:/opt/reimbursement/demo-compose.yml
    - scp -F ${SSH_CONFIG} demo/demo-reimbursement.trafineo.com ${HOST}:/opt/reimbursement/nginx/conf.d/demo-reimbursement.trafineo.com
    - |
      ssh -F ${SSH_CONFIG} ${HOST} <<END
        docker-compose -p demo -f /opt/reimbursement/demo-compose.yml up --detach
      END
  rules:
    - if: '$CI_COMMIT_BRANCH == "demo"'
