version: "2.4"

x-service-template:
  &default-config
  read_only: true
  init: true
  mem_limit: 100m
  user: "1000:1000"
  env_file: /opt/reimbursement/.env
  restart: unless-stopped

services:
  frontend:
    << : *default-config
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "1"
    image: ${EXTERNAL_REGISTRY}/trafineo-portal/frontend:${CI_COMMIT_SHORT_SHA}
    volumes:
      - type: bind
        source: /opt/reimbursement/certificates
        target: /opt/nginx/certs
      - type: bind
        source: /opt/reimbursement/nginx/conf.d
        target: /opt/nginx/conf.d
      - type: tmpfs
        target: /tmp
      - type: tmpfs
        target: /var/log/nginx
      - type: tmpfs
        target: /var/lib/nginx/logs
    ports:
      - "443:8443"
      - "80:8080"
