#!/bin/bash -e

current=$(git branch --show-current)

git fetch >> /dev/null 2>&1

for epic in develop qa release #staging
do
    echo "Merging master into $epic"
    {
        git checkout "$epic"
        git pull
        git merge origin/master --no-edit -X theirs
        git push
     } >> /dev/null 2>&1
done

git checkout "$current"

if [ -n "$1" ]
then
    echo "Tagging as $1"
    git tag "$1"
    git push --tags
fi
