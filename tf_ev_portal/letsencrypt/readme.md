# Requesting certificates
## Dev
certbot certonly --standalone --http-01-port 8080 --preferred-challenges http --agree-tos --email <EMAIL> -n -d reimbursement-dev.trafineo.com -d aral-reimbursement-dev.trafineo.com -d bp-reimbursement-dev.trafineo.com -d demo-reimbursement.trafineo.com
## Qa
certbot certonly --standalone --http-01-port 8080 --preferred-challenges http --agree-tos --email <EMAIL> -n -d reimbursement-qa.trafineo.com -d aral-reimbursement-qa.trafineo.com -d bp-reimbursement-qa.trafineo.com
## Prod
certbot certonly --standalone --http-01-port 8080 --preferred-challenges http --agree-tos --email <EMAIL> -n -d reimbursement.trafineo.com -d aral-reimbursement.trafineo.com -d bp-reimbursement.trafineo.com

# Set up deploy hook
Copy certbot_deploy_hook.sh to /etc/letsencrypt/renewal-hooks/deploy/