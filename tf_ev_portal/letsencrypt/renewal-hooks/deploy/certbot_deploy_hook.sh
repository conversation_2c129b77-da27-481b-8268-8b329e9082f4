#!/bin/bash -e
# /etc/letsencrypt/renewal-hooks/deploy/certbot_deploy_hook.sh

username="$(hostname)admin"

# Copy files
cat /etc/letsencrypt/live/reimbursement*.trafineo.com/fullchain.pem      > /opt/reimbursement/certificates/fullchain.pem
cat /etc/letsencrypt/live/reimbursement*.trafineo.com/privkey.pem        > /opt/reimbursement/certificates/privkey.pem

chown -R "$username:users" /opt/reimbursement/certificates/*

# Reload nginx config
/usr/local/bin/docker-compose -f /opt/reimbursement/docker-compose.yml exec -T frontend nginx -s reload -c /tmp/nginx.conf

# If production, repeat for second server
if [ "$(hostname)" != "trafazlx32" ] ; then
    exit 0
fi

ssh -i "/home/<USER>/.ssh/id_rsa" trafazlx33admin@trafazlx33 <<END
    echo "$(cat /etc/letsencrypt/live/reimbursement*.trafineo.com/fullchain.pem)"      > /opt/reimbursement/certificates/fullchain.pem
    echo "$(cat /etc/letsencrypt/live/reimbursement*.trafineo.com/privkey.pem)"        > /opt/reimbursement/certificates/privkey.pem
    /usr/local/bin/docker-compose -f /opt/reimbursement/docker-compose.yml exec -T frontend nginx -s reload -c /tmp/nginx.conf
END