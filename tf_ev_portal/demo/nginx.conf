load_module modules/ngx_http_headers_more_filter_module.so;

worker_processes 1;
error_log        /opt/nginx/logs/error.log warn;
pid              /tmp/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;

    client_body_temp_path /tmp/client_temp 1 2;
    proxy_temp_path       /tmp/proxy       1 2;
    fastcgi_temp_path     /tmp/fastcgi     1 2;
    scgi_temp_path        /tmp/scgi        1 2;
    uwsgi_temp_path       /tmp/uwsgi       1 2;
    client_max_body_size  5M;
    default_type          application/octet-stream;
    sendfile              on;
    tcp_nopush            on;
    keepalive_timeout     65;
    gzip                  on;

    map $http_user_agent $loggable {
        "Healthcheck"  0;
        "Load Balancer Agent"  0;
        default 1;
    }

    log_format main '[$time_local] $remote_addr $status $body_bytes_sent "$request" "$http_user_agent"';
    access_log      /dev/stdout  main if=$loggable;
    access_log      /opt/nginx/logs/access.log  main if=$loggable;

    # Expires map
    map $sent_http_content_type $expires {
        default                    off;
        text/html                  epoch;
        text/css                   max;
        application/javascript     max;
        ~image/                    max;
    }

    server {
        listen      8000;
        listen [::]:8000;

        root /opt/nginx/content;

        location / {
            location = /healthcheck {
                auth_basic off;
                return 200;
            }
            
            auth_basic           "Demo Portal";
            auth_basic_user_file /opt/nginx/htpasswd;

            index     index.html;
            try_files $uri $uri/ /index.html;
        }
    }
}