version: "2.4"

x-service-template:
  &default-config
  read_only: true
  init: true
  mem_limit: 100m
  user: "1000:1000"
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "1"
  env_file: /opt/reimbursement/.env
  restart: unless-stopped

services:
  demo:
    << : *default-config
    image: ${EXTERNAL_REGISTRY}/trafineo-portal/demo:${CI_COMMIT_SHORT_SHA}
    volumes:
      - type: bind
        source: /opt/reimbursement/logs/nginx
        target: /opt/nginx/logs
      - type: tmpfs
        target: /tmp
      - type: tmpfs
        target: /var/log/nginx
    ports:
      - "8000:8000"
