server {
    server_name               demo-reimbursement.trafineo.com;
    ssl_certificate           /opt/nginx/certs/fullchain.pem;
    ssl_certificate_key       /opt/nginx/certs/privkey.pem;

    listen                    8443 ssl http2;
    listen                    [::]:8443 ssl http2;

    ssl_dhparam               /opt/nginx/dhparam.pem;
    ssl_session_cache         shared:SSL:20m;
    ssl_session_timeout       60m;
    ssl_protocols             TLSv1.2 TLSv1.3;
    ssl_prefer_server_ciphers on;
    ssl_ciphers               "EECDH+ECDSA+AESGCM EECDH+aRSA+AESGCM EECDH+ECDSA+SHA384 EECDH+ECDSA+SHA256 EECDH+aRSA+SHA384 EECDH+aRSA+SHA256 EECDH+aRSA+RC4 EECDH EDH+aRSA HIGH !RC4 !aNULL !eNULL !LOW !3DES !MD5 !EXP !PSK !SRP !DSS";
    expires $expires;

    add_header  Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header  X-Frame-Options "sameorigin" always;

    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Host $http_host;
    proxy_ssl_verify off;

    location / {
        proxy_pass http://*************:8000/;
    }
}
