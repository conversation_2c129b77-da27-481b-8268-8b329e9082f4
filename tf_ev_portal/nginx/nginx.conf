load_module modules/ngx_http_headers_more_filter_module.so;
load_module modules/ngx_http_js_module.so;

worker_processes 1;
error_log        /dev/stdout warn;
error_log        syslog:server=trafazlx52.trafineo.com:5140,nohostname,tag=Reimbursement info;
pid              /tmp/nginx.pid;

events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    js_import   /opt/nginx/auth.js;

    upstream esb {
        server ${ESB};
    }

    upstream services {
        server ${SERVICES};
    }

    upstream keycloak {
        server ${KEYCLOAK};
    }

    client_body_temp_path /tmp/client_temp 1 2;
    proxy_temp_path       /tmp/proxy       1 2;
    fastcgi_temp_path     /tmp/fastcgi     1 2;
    scgi_temp_path        /tmp/scgi        1 2;
    uwsgi_temp_path       /tmp/uwsgi       1 2;
    client_max_body_size  6M;
    default_type          application/octet-stream;
    sendfile              on;
    tcp_nopush            on;
    keepalive_timeout     65;
    gzip                  on;


    # Custom access logging
    map $http_user_agent $loggable {
        "Healthcheck"  0;
        "Load Balancer Agent"  0;
        "Edge Health Probe"  0;
        default 1;
    }
    log_format main '[$time_local] $remote_addr $status $body_bytes_sent $request_time "$request" "$http_user_agent"';
    access_log      /dev/stdout main if=$loggable;
    
    log_format monitoring   '###|level=info|stack=Reimbursement|'
                            'http_client=$remote_addr|'
                            'http_status=$status|'
                            'http_bytes_sent=$body_bytes_sent|'
                            'http_response_time=$request_time|'
                            'http_method=$request_method|'
                            'http_request=$request_uri|'
                            'http_version=$server_protocol|'
                            'http_user_agent=$http_user_agent|'
                            'http_server_name=$server_name|'
                            'http_upstream_addr=$upstream_addr|'
                            'http_x_forwarded_for=$http_x_forwarded_for|'
                            'http_referrer=$http_referer'
    ;
    access_log      syslog:server=trafazlx52.trafineo.com:5140,nohostname,tag=Reimbursement monitoring if=$loggable;


    # Expires map
    map $sent_http_content_type $expires {
        default                    off;
        text/html                  epoch;
        text/css                   max;
        application/javascript     max;
        ~image/                    max;
    }
    expires $expires;


    # Additional headers / SSL settings / proxy settings
    server_tokens off; # hides version on 404 or 500 pages
    more_clear_headers 'Server'; # removes Server header from response headers

    add_header  Content-Security-Policy "default-src 'self' 'unsafe-inline' 'unsafe-eval' https://appservices.trafineo.com https://www.googletagmanager.com https://region1.google-analytics.com https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/nl.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/gb.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/de.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/at.svg; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://www.googletagmanager.com; frame-src 'self' https://www.google.com https://www.googletagmanager.com; style-src 'self' 'unsafe-inline' *.googleapis.com https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/css/flag-icon.min.css; img-src 'self' 'unsafe-inline' www.googletagmanager.com https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/de.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/gb.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/nl.svg https://cdnjs.cloudflare.com/ajax/libs/flag-icon-css/3.5.0/flags/4x3/at.svg blob:" always;
    add_header  Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header  Strict-Transport-Security "max-age=63072000; includeSubDomains" always;
    add_header  X-Content-Type-Options "nosniff" always;
    add_header  X-Frame-Options "sameorigin" always;
    add_header X-Robots-Tag "noindex" always;
    add_header X-Robots-Tag "nofollow" always;
    add_header X-Robots-Tag "nosnippet" always;
    add_header X-Robots-Tag "noarchive" always;

    ssl_session_cache         shared:SSL:20m;
    ssl_session_timeout       60m;
    ssl_protocols             TLSv1.2 TLSv1.3;
    ssl_buffer_size           4K;
    ssl_ecdh_curve            secp521r1:secp384r1;
    ssl_prefer_server_ciphers on;
    ssl_ciphers               'EECDH+AESGCM:EECDH+AES256:!ECDHE-RSA-AES256-SHA384:!ECDHE-RSA-AES256-SHA';
    ssl_dhparam               /opt/nginx/dhparam.pem;
    ssl_certificate           /opt/nginx/certs/fullchain.pem;
    ssl_certificate_key       /opt/nginx/certs/privkey.pem;
    
    proxy_set_header Host $http_host;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header Forwarded "for=$remote_addr;proto=$scheme";

    proxy_ssl_verify off;
    proxy_pass_request_headers on;
    #proxy_pass_header Server; #Disable server header version
    underscores_in_headers on;  # TODO remove when switching from User_id to User-Id

    # HTTP redirect to HTTPS and ACME forwarding
    server {
        listen      8080;
        listen [::]:8080;


        location /.well-known/ {
            proxy_pass http://${CERTBOT_HOST}/.well-known/;
        }

        location / {
            location = /healthcheck {
                return 200;
            }
            return 301 https://$host$request_uri;
        }

    }

    # Trafineo
    server {
        server_name             ${DNS_NAME_DEFAULT};
        listen                  8443 ssl http2 default_server;
        listen                  [::]:8443 ssl http2 default_server;

        root        /opt/nginx/content/default;

        set $idp_client_id ${CLIENT_ID};
        set $idp_client_secret ${CLIENT_SECRET};

        include     /opt/nginx/server.conf;
    }
    # Aral
    server {
        server_name             ${DNS_NAME_ARAL};
        listen                  8443 ssl http2;
        listen                  [::]:8443 ssl http2;

        root        /opt/nginx/content/aral;
        
        set $idp_client_id ${CLIENT_ID};
        set $idp_client_secret ${CLIENT_SECRET};

        include     /opt/nginx/server.conf;
    }
    # BP
    server {
        server_name             ${DNS_NAME_BP};
        listen                  8443 ssl http2;
        listen                  [::]:8443 ssl http2;

        root        /opt/nginx/content/bp;

        set $idp_client_id ${CLIENT_ID};
        set $idp_client_secret ${CLIENT_SECRET};

        include     /opt/nginx/server.conf;
    }

    include /opt/nginx/conf.d/*.conf;
    include /opt/nginx/conf.d/*.trafineo.com;
}
