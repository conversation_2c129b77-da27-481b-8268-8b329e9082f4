import qs from "querystring";

function checkRole(r) {
  if (!r.headersIn.authorization) {
    // r.warn('Header Authorization missing');
    r.return(401, "Header Authorization missing");
  } else {
    let requiredRolesArr = qs.parse(r.uri.replace(/.*\//, "")).role || [];
    if (typeof requiredRolesArr === "string")
      requiredRolesArr = [requiredRolesArr];
    const requiredRoles = requiredRolesArr.map((role) =>
      role ? role.toLowerCase() : role
    );
    r.subrequest("/validate-token", (reply) => {
      if (reply.status !== 200) {
        // r.warn(reply.status + ' : ' + reply.body);
        // r.warn('Token invalid');
        r.return(401, "Token invalid");
      } else {
        const data = JSON.parse(reply.responseText);
        if (
          requiredRoles.includes(
            data.role ? data.role.toLowerCase() : data.role
          )
        ) {
          // r.warn('Success');
          r.headersOut["User_id"] = data.sub;
          r.headersOut["Role"] = data.role;
          r.headersOut["Provider"] = data.provider;
          r.headersOut["Function"] = data.function;
          r.headersOut["Language"] = data.locale;
          r.status = 200;
          r.sendHeader();
          r.finish();
        } else {
          // r.warn('Unauthorized');
          r.return(401, "Unauthorized");
        }
      }
    });
  }
}

export default { checkRole };
