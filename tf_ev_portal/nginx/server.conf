

##############################
# Internal locations
##############################

location /auth {
    internal;
    client_max_body_size 0;
    js_content auth.checkRole;
}

location = /validate-token {
    internal;
    client_max_body_size 0;
    proxy_method GET;
    proxy_set_header Content-Type "";
    proxy_set_header Content-Length "";
    proxy_pass_request_body off;
    proxy_pass https://keycloak/realms/trafineo/protocol/openid-connect/userinfo;
}

location = /leads-public {
    internal;
    proxy_set_header Role $http_role;
    proxy_pass   https://esb/services/sales/leads;
}

location = /onboarding-registration-public {
    internal;
    proxy_set_header Role $http_role;
    proxy_pass   https://esb/services/onboarding/registration;
}

location = /driver_management-public {
    internal;
    proxy_set_header Role $http_role;
    proxy_pass   https://esb/services/driver_management;
}

##############################
# Healthcheck
##############################

location = /healthcheck {
    return 200;
}

##############################
# Maintenance
##############################

error_page 503 @maintenance;
location @maintenance {
    try_files /maintenance.html /maintenance.html;
}

##############################
# External locations
##############################

location / {

    if (-f /opt/nginx/conf.d/maintenance_active) {
        return 503;
    }

    ##############################
    # API locations
    ##############################

    location /api/ {
        add_header Cache-control no-store;
        add_header Pragma        no-cache;

        # Ironbeetle-18
        location ~* '[\'\"\<\>]' {
            return 404;
        }


    ##############################
    # API locations IDP
    ##############################
 
        location /api/idp/ {

            location = /api/idp/login {
                proxy_method     POST;
                proxy_set_header Content-Type "application/x-www-form-urlencoded";
                proxy_set_body   "$request_body&client_id=$idp_client_id&client_secret=$idp_client_secret";
                proxy_pass       https://keycloak/realms/trafineo/protocol/openid-connect/token;
                add_header       Set-Cookie "redirect=https://$host/bankData?redirect=true;Domain=trafineo.com;Path=/";
            }
            location = /api/idp/logout {
                proxy_method     POST;
                proxy_set_header Content-Type "application/x-www-form-urlencoded";
                proxy_set_body   "$request_body&client_id=$idp_client_id&client_secret=$idp_client_secret";
                proxy_pass       https://keycloak/api/idp/auth/realms/trafineo/protocol/openid-connect/logout;
            }
            location /api/idp/login-actions/ {
                proxy_pass https://keycloak/realms/trafineo/login-actions/;
            }
            location = /api/idp/changeEmail {
                proxy_pass https://keycloak/realms/trafineo/ext/changeEmail;
            }
            location = /api/idp/changePassword {
                proxy_pass https://keycloak/realms/trafineo/ext/changePassword;
            }
        }

        ##############################
        # API locations ESB
        ##############################

        location /api/esb/ {
            auth_request_set $user $sent_http_user_id;
            auth_request_set $role $sent_http_role;
            auth_request_set $provider $sent_http_provider;
            auth_request_set $function $sent_http_function;
            auth_request_set $language $sent_http_language;
            proxy_set_header User_id $user;
            proxy_set_header Role $role;
            proxy_set_header Provider $provider;
            proxy_set_header Function $function;
            proxy_set_header Language $language;

            location /api/esb/authorities/ {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=Superuser&role=Support;
                proxy_pass   https://esb/services/authorities/;
            }
            location /api/esb/Additional_Information/ {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager;
                proxy_pass   https://esb/services/Additional_Information/;
            }
            location /api/esb/Ev_Driver_Data_Approval/ {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect;
                proxy_pass   https://esb/services/Ev_Driver_Data_Approval/;
            }
            location /api/esb/Ev_Driver_Data/ {
                auth_request /auth/role=Driver&role=AppDriver;
                proxy_pass   https://esb/services/Ev_Driver_Data/;
            }
            location /api/esb/Information_Overview/ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/Information_Overview/;
            }
            location /api/esb/chargingsessions/ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/chargingsessions/;
            }
            location /api/esb/stats/ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/stats/;
            }
            location /api/esb/trainings/ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass https://esb/services/trainings/;
            }
            location /api/esb/Policies/ {
                location /api/esb/Policies/Get_Policies/ {
                    return 404;
                }
                location /api/esb/Policies/Get_Policies {
                    #if ($args ~ "^language=\w+&type=\w+$") {
                        proxy_set_header Role $http_role;
                        proxy_pass   https://esb/services/Policies/Get_Policies;
                    #}
                }
                location /api/esb/Policies/Post_PoliciesAccepted {
                    auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Sales&role=Support;
                    proxy_pass   https://esb/services/Policies/Post_PoliciesAccepted;
                }
            }
            location = /api/esb/sales/leads {
                if ($request_method = PUT ) {
                    rewrite ^/api/esb/sales/leads(.*)$ /leads-public$1 last;
                }
                auth_request /auth/role=Superuser&role=Support;
                proxy_pass   https://esb/services/sales/leads;
            }
            location /api/esb/Rapyd_User_Management/ {
                auth_request /auth/role=Driver&role=AppDriver;
                proxy_pass   https://esb/services/Rapyd_User_Management/;
            }
            location /api/esb/Reimbursement/ {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/Reimbursement/;
            }
            location /api/esb/User_Management/ {
                auth_request /auth/role=Sales&role=Superuser&role=Support;
                proxy_pass   https://esb/services/User_Management/;
            }
            location /api/esb/fm_management {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Sales&role=Support;
                proxy_pass   https://esb/services/fm_management;
            }
            location /api/esb/driver_management {
                if ($request_method = PUT ) {
                    rewrite ^/api/esb/driver_management(.*)$ /driver_management-public$1 last;
                }
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager;
                proxy_pass   https://esb/services/driver_management;
            }
            location /api/esb/get_fm_data/ {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Sales&role=Support;
                proxy_pass   https://esb/services/get_fm_data/;
            }
            location /api/esb/status_history/ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/status_history/;
            }
            location /api/esb/FileUpload {
                client_max_body_size 21549051;
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/FileUpload;
            }
            location /api/esb/SendSupportRequest {
                client_max_body_size 21549051;
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/SendSupportRequest;
            }
            location /api/esb/reminder {
                client_max_body_size 21549051;
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/reminder;
            }
            location /api/esb/vehicle_list {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/ordered_vehicle_list/vehicle_list;
            }
            location /api/esb/ev_driver {
                auth_request /auth/role=FleetManager_direct&role=FleetManager_indirect;
                proxy_pass   https://esb/services/ev_driver;
            }
            location /api/esb/onboarding/approval {
                auth_request /auth/role=Superuser&role=Sales&role=Support;
                proxy_pass   https://esb/services/onboarding/approval;
            }
            location = /api/esb/onboarding/registration {
                if ($request_method = POST ) {
                    rewrite ^/api/esb/onboarding/registration(.*)$ /onboarding-registration-public$1 last;
                }
                auth_request /auth/role=Superuser&role=Sales&role=Support;
                proxy_pass   https://esb/services/onboarding/registration;
            }
            location /api/esb/payout_reports {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass   https://esb/services/payout_reports;
            }
            location ~ ^/api/esb/cards/(?<id>[^/]+)/availability {
                proxy_pass https://esb/services/cards/$id/availability$is_args$args;
            }
            #RE-3494 FE_GR_AP_UserManagement
            location /api/esb/User_Management {
                auth_request /auth/role=Superuser&role=FleetManager_direct&role=FleetManager_indirect&role=Sales&role=Support;
                proxy_pass   https://esb/services/User_Management;
            }
            
            # location /api/esb/cards/ {
            #     auth_request /auth/***;
            #     proxy_pass   https://esb/services/cards/;
            # }
        }

        ##############################
        # API locations Services
        ##############################

        location /api/service/ {
            auth_request_set $user $sent_http_user_id;
            auth_request_set $role $sent_http_role;
            auth_request_set $provider $sent_http_provider;
            auth_request_set $function $sent_http_function;
            auth_request_set $language $sent_http_language;
            proxy_set_header User_id $user;
            proxy_set_header Role $role;
            proxy_set_header Provider $provider;
            proxy_set_header Function $function;
            proxy_set_header Language $language;

            # Add basic auth for services
            proxy_set_header Authorization "Basic dGVzYjp0ZXNi";

            location ~ ^/api/service/approvals/(?<version>v\d+)/(?<route>.*)$ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass https://services/api/service/approvals/$version/$route$is_args$args;
            }

            location ~ ^/api/service/chargingsessions/(?<version>v\d+)/(?<route>.*)$ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass https://services/api/service/chargingsessions/$version/$route$is_args$args;
            }

            location ~ ^/api/service/users/(?<version>v\d+)/(?<route>.*)$ {
                auth_request /auth/role=Support&role=Sales;
                proxy_pass https://services/api/service/users/$version/$route$is_args$args;
            }
            # TODO check if roles are correct
            location ~ ^/api/service/rejections/(?<version>v\d+)/(?<route>.*)$ {
                auth_request /auth/role=Driver&role=AppDriver&role=AppUser&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Sales&role=Support;
                proxy_pass https://services/api/service/rejections/$version/$route$is_args$args;
            }

            location ~ ^/api/service/io/(?<version>v\d+)/io_cards/(?<route>.*)$ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass https://services/api/service/io/$version/io_cards/$route$is_args$args;
            }

            location ~ ^/api/service/io/(?<version>v\d+)/io_users/(?<route>.*)$ {
                auth_request /auth/role=Driver&role=AppDriver&role=FleetManager_direct&role=FleetManager_indirect&role=LeasingManager&role=Superuser&role=Support;
                proxy_pass https://services/api/service/io/$version/io_users/$route$is_args$args;
            }

        }
    }

    ##############################
    # Default location
    ##############################

    index     index.html;
    try_files $uri $uri/ /index.html;
}
