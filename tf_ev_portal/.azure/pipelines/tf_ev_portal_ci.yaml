pool: Trafineo Pool

trigger:
  branches:
    include:
    - develop
    - qa
    - staging
    - master

parameters:
  - name: demo
    type: boolean
    default: true
  - name: environments
    type: object
    default:
      - name: develop
        hosts:
          - trafineo-portal-develop
        branch: refs/heads/develop
      - name: qa
        hosts:
          - trafineo-portal-qa
        branch: refs/heads/qa
      - name: staging
        hosts:
          - trafineo-portal-staging1
          - trafineo-portal-staging2
        branch: refs/heads/staging
      - name: prod
        hosts:
          - trafineo-portal-prod1
          - trafineo-portal-prod2
        branch: refs/heads/master

variables: 
  - group: Trafineo EV Common
  - name: CI_COMMIT_REF_NAME
    value: ${{ replace(replace(variables['Build.SourceBranch'], 'refs/heads/', ''), 'refs/pull/', 'PR-') }}
  - name: CI_COMMIT_SHORT_SHA
    value: ${{ substring(variables['Build.SourceVersion'], 0, 7) }}
  - name: CI_PROJECT_NAME
    value: trafineo-portal

resources:
  repositories:
    - repository: TrafineoInfra
      type: git
      name: CM_Global_Fleet_Services/tf_ev_infrastructure

stages:
- stage: TestAndSecurity
  displayName: 'Test & Linting'
  jobs:
  - template: templates/linting.yaml@TrafineoInfra
    parameters:
      codeDir: frontend
  - template: templates/test.yaml@TrafineoInfra
    parameters:
      codeDir: frontend

- ${{ if in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/qa', 'refs/heads/staging', 'refs/heads/master') }}:
  - stage: Clean
    displayName: 'Clean'
    dependsOn: []
    jobs:
      - ${{ each environment in parameters.environments }}:
        - ${{ if eq(environment.branch, variables['Build.SourceBranch']) }}:
          - ${{ each hostName in environment.hosts }}:
            - template: templates/host-cleanup.yaml@TrafineoInfra
              parameters:
                hostName: ${{ hostName }}
  
- stage: Build
  displayName: 'Build'
  dependsOn: []
  jobs:
  - template: templates/build.yaml@TrafineoInfra
    parameters:
      service: frontend
      buildContext: "."

- ${{ if in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/qa', 'refs/heads/staging', 'refs/heads/master') }}:
  - stage: Deploy
    displayName: 'Deploy'
    dependsOn:
      - Build
    jobs:
      - ${{ each environment in parameters.environments }}:
        - ${{ if eq(environment.branch, variables['Build.SourceBranch']) }}:
          - ${{ each hostName in environment.hosts }}:
            - template: templates/deploy.yaml@TrafineoInfra
              parameters:
                hostName: ${{ hostName }}
                dockerComposeSubstitutions: ${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}
                remoteDockerComposeFile: /opt/reimbursement/docker-compose.yml
                env: ${{ environment.name }}
                setupSteps:
                  - script: |
                      set -o pipefail -o errexit -o nounset

                      scp ${ENV}.env ${HOST}:/opt/reimbursement/.env
                    displayName: 'Copy environment file to server'
                  - script: |
                      set -o pipefail -o errexit -o nounset

                      ssh ${HOST} "mkdir -p /opt/reimbursement/nginx/conf.d/maintenance"
                    displayName: 'Prepare directories'

- ${{ if eq(parameters.demo, true) }}:
  - stage: BuildDemo
    displayName: 'Build Demo'
    dependsOn: []
    jobs:
    - template: templates/build.yaml@TrafineoInfra
      parameters:
        service: demo
        buildContext: "."
        dockerBuildArgs: -f demo/Dockerfile

  - stage: DeployDemo
    displayName: 'Deploy Frontend and Demo'
    dependsOn:
      - Build
      - BuildDemo
    jobs:
    - job: DeployBothServices
      displayName: 'Deploy Frontend and Demo Together'
      variables:
        - group: Trafineo EV Common
        - name: HOST
          value: trafineo-portal-develop
        - name: ENV
          value: develop
      steps:
      - template: templates/ssh-setup.yaml@TrafineoInfra

      - script: |
          set -o pipefail -o errexit -o nounset

          # Prepare frontend docker-compose file
          envsubst '${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}' < docker-compose.template.yml > docker-compose.yml

          # Prepare demo docker-compose file
          envsubst '${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}' < demo/docker-compose.template.yml > demo-compose.yml
        displayName: 'Prepare Docker Compose files'

      - script: |
          set -o pipefail -o errexit -o nounset

          # Copy frontend compose file
          scp docker-compose.yml ${HOST}:/opt/reimbursement/docker-compose.yml

          # Copy demo compose file
          scp demo-compose.yml ${HOST}:/opt/reimbursement/demo-compose.yml

          # Copy environment file
          scp ${ENV}.env ${HOST}:/opt/reimbursement/.env

          # Copy demo nginx config
          scp demo/demo-reimbursement.trafineo.com ${HOST}:/opt/reimbursement/nginx/conf.d/demo-reimbursement.trafineo.com
        displayName: 'Copy files to server'

      - script: |
          set -o pipefail -o errexit -o nounset

          ssh -T ${HOST} <<END
            set -e
            # Create required directories
            mkdir -p /opt/reimbursement/logs/nginx
            mkdir -p /opt/reimbursement/nginx/conf.d/maintenance

            # Login to registry
            docker login "$(EXTERNAL_REGISTRY)" -u "$(EXTERNAL_REGISTRY_USER)" -p "$(EXTERNAL_REGISTRY_PASS)" 2>/dev/null

            # Stop existing containers
            docker-compose -f /opt/reimbursement/docker-compose.yml down 2>/dev/null || true
            docker-compose -p demo -f /opt/reimbursement/demo-compose.yml down 2>/dev/null || true

            # Pull latest images
            docker-compose -f /opt/reimbursement/docker-compose.yml pull --quiet
            docker-compose -p demo -f /opt/reimbursement/demo-compose.yml pull --quiet

            # Start both services with latest images
            docker-compose -f /opt/reimbursement/docker-compose.yml up --no-build --detach --remove-orphans
            docker-compose -p demo -f /opt/reimbursement/demo-compose.yml up --no-build --detach --remove-orphans
          END
        displayName: 'Deploy Frontend and Demo with Latest Images'
        failOnStderr: false
