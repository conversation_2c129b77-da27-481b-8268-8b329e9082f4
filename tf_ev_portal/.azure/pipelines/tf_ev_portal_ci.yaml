pool: Trafineo Pool

trigger:
  branches:
    include:
    - develop
    - qa
    - staging
    - master

parameters:
  - name: demo
    type: boolean
    default: true
  - name: environments
    type: object
    default:
      - name: develop
        hosts:
          - trafineo-portal-develop
        branch: refs/heads/develop
      - name: qa
        hosts:
          - trafineo-portal-qa
        branch: refs/heads/qa
      - name: staging
        hosts:
          - trafineo-portal-staging1
          - trafineo-portal-staging2
        branch: refs/heads/staging
      - name: prod
        hosts:
          - trafineo-portal-prod1
          - trafineo-portal-prod2
        branch: refs/heads/master

variables: 
  - group: Trafineo EV Common
  - name: CI_COMMIT_REF_NAME
    value: ${{ replace(replace(variables['Build.SourceBranch'], 'refs/heads/', ''), 'refs/pull/', 'PR-') }}
  - name: CI_COMMIT_SHORT_SHA
    value: ${{ substring(variables['Build.SourceVersion'], 0, 7) }}
  - name: CI_PROJECT_NAME
    value: trafineo-portal

resources:
  repositories:
    - repository: TrafineoInfra
      type: git
      name: CM_Global_Fleet_Services/tf_ev_infrastructure

stages:
- stage: TestAndSecurity
  displayName: 'Test & Linting'
  jobs:
  - template: templates/linting.yaml@TrafineoInfra
    parameters:
      codeDir: frontend
  - template: templates/test.yaml@TrafineoInfra
    parameters:
      codeDir: frontend

- ${{ if in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/qa', 'refs/heads/staging', 'refs/heads/master') }}:
  - stage: Clean
    displayName: 'Clean'
    dependsOn: []
    jobs:
      - ${{ each environment in parameters.environments }}:
        - ${{ if eq(environment.branch, variables['Build.SourceBranch']) }}:
          - ${{ each hostName in environment.hosts }}:
            - template: templates/host-cleanup.yaml@TrafineoInfra
              parameters:
                hostName: ${{ hostName }}
  
- stage: Build
  displayName: 'Build'
  dependsOn: []
  jobs:
  - template: templates/build.yaml@TrafineoInfra
    parameters:
      service: frontend
      buildContext: "."

- ${{ if in(variables['Build.SourceBranch'], 'refs/heads/develop', 'refs/heads/qa', 'refs/heads/staging', 'refs/heads/master') }}:
  - stage: Deploy
    displayName: 'Deploy'
    dependsOn:
      - Build
    jobs:
      - ${{ each environment in parameters.environments }}:
        - ${{ if eq(environment.branch, variables['Build.SourceBranch']) }}:
          - ${{ each hostName in environment.hosts }}:
            - template: templates/deploy.yaml@TrafineoInfra
              parameters:
                hostName: ${{ hostName }}
                dockerComposeSubstitutions: ${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}
                remoteDockerComposeFile: /opt/reimbursement/docker-compose.yml
                env: ${{ environment.name }}
                setupSteps:
                  - script: |
                      set -o pipefail -o errexit -o nounset

                      scp ${ENV}.env ${HOST}:/opt/reimbursement/.env
                    displayName: 'Copy environment file to server'
                  - script: |
                      set -o pipefail -o errexit -o nounset

                      ssh ${HOST} "mkdir -p /opt/reimbursement/nginx/conf.d/maintenance"
                    displayName: 'Prepare directories'

- ${{ if eq(parameters.demo, true) }}:
  - stage: BuildDemo
    displayName: 'Build Demo'
    dependsOn: []
    jobs:
    - template: templates/build.yaml@TrafineoInfra
      parameters:
        service: demo
        buildContext: "."
        dockerBuildArgs: -f demo/Dockerfile

  - stage: DeployDemo
    displayName: 'Deploy Demo'
    dependsOn:
      - BuildDemo
    jobs:
    - template: templates/deploy.yaml@TrafineoInfra
      parameters:
        hostName: trafineo-portal-develop
        env: develop
        dockerComposeSubstitutions: ${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}
        remoteDockerComposeFile: /opt/reimbursement/demo-compose.yml
        setupSteps:
          - script: |
              set -o pipefail -o errexit -o nounset

              # Generate demo-compose.yml from the correct template
              envsubst '${CI_COMMIT_SHORT_SHA} ${EXTERNAL_REGISTRY}' < demo/docker-compose.template.yml > demo-compose.yml
              scp demo-compose.yml ${HOST}:/opt/reimbursement/demo-compose.yml
            displayName: 'Generate and copy demo compose file'
          - script: |
              set -o pipefail -o errexit -o nounset

              scp ${ENV}.env ${HOST}:/opt/reimbursement/.env
            displayName: 'Copy environment file to server'
          - script: |
              set -o pipefail -o errexit -o nounset

              scp demo/demo-reimbursement.trafineo.com ${HOST}:/opt/reimbursement/nginx/conf.d/demo-reimbursement.trafineo.com
            displayName: 'Copy Nginx config to server'
          - script: |
              set -o pipefail -o errexit -o nounset

              ssh ${HOST} "mkdir -p /opt/reimbursement/logs/nginx"
              ssh ${HOST} "docker-compose -p demo -f /opt/reimbursement/demo-compose.yml up --detach"
            displayName: 'Deploy demo container'
