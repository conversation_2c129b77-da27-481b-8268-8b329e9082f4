const axios = require('axios');
const fs = require('fs');
require('dotenv').config({ path: '.env.development' });
const FormData = require('form-data');
const path = require('path');

const apiToken = process.env.POEDITOR_API_TOKEN;
const projectId = 352621;
const langDir = 'src/lang';

const poeditor = axios.create({
  baseURL: 'https://api.poeditor.com/v2',
});

const getLanguage = (lang) => {
  try {
    console.log(`Downloading language: ${lang}`);
    const file = fs.createWriteStream(
      path.join(__dirname, langDir, `${lang}.json`),
    );
    file.on('open', async () => {
      const apiForm = new FormData();
      apiForm.append('id', projectId);
      apiForm.append('api_token', apiToken);
      apiForm.append('language', lang);
      apiForm.append('type', 'key_value_json');

      const languageUrl = await poeditor.post('/projects/export', apiForm, {
        headers: apiForm.getHeaders(),
      });

      const language = await axios.get(languageUrl.data.result.url, {
        responseType: 'stream',
      });

      language.data.pipe(file);
      console.log(`Finished writing downloaded language to file: ${lang}`);
    });
  } catch (err) {
    console.error(`Could not download Lang ${lang}!`);
    console.error(err.stack);
  }
};

(async () => {
  const form = new FormData();
  form.append('id', projectId);
  form.append('api_token', apiToken);

  if (!fs.existsSync(langDir)) {
    fs.mkdirSync(langDir);
  }

  const requiredLangs = (
    await poeditor.post(`/languages/list`, form, {
      headers: form.getHeaders(),
    })
  ).data.result.languages.map((lang) => lang.code);

  if (!apiToken) {
    console.error('No POEditor Api token available.');
    process.exit(1);
  }

  requiredLangs.forEach((lang) => getLanguage(lang));
})();
