POEDITOR_API_TOKEN = "e3214018b6a8104f387fa68f9f7ada53"

# DEV
REACT_APP_KEYCLOAK_TOKEN_URI = "/protocol/openid-connect/token"
REACT_APP_KEYCLOAK_LOGOUT_URI = "/protocol/openid-connect/logout"
REACT_APP_KEYCLOAK_URL = "http://localhost:3000/auth/realms/trafineo"
REACT_APP_KEYCLOAK_DEV_PROXY_URL = "http://localhost:3000/idp"

REACT_APP_KEYCLOAK_ID = "frontend"
REACT_APP_KEYCLOAK_SECRET = "a73bd556-9449-46b3-8e05-705444a8f39c"

REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE = "fleetmanager_direct"
REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE = "fleetmanager_indirect"
REACT_APP_KEYCLOAK_DRIVER_ROLE = "driver"
REACT_APP_KEYCLOAK_APP_DRIVER_ROLE = "appdriver"
REACT_APP_KEYCLOAK_APP_USER_ROLE = "appuser"
REACT_APP_KEYCLOAK_SUPERUSER_ROLE = "superuser"
REACT_APP_KEYCLOAK_SALESADMIN_ROLE = "sales"
REACT_APP_KEYCLOAK_SUPPORT_ROLE = "support"

REACT_APP_USE_MOCK = "false"

REACT_APP_API_BASEURL = "http://localhost:3000/api/esb"
REACT_APP_SERVICE_BASEURL = "http://localhost:3000/api/service"
