@font-face {
  font-family: 'Univers';
  src: url('./static/Univers-45-light-latin-extended.woff');
  font-weight: 300;
  font-style: normal;
}
@font-face {
  font-family: 'Univers';
  src: url('./static/Univers-55-roman-latin-extended.woff');
  font-weight: 400;
  font-style: normal;
}
@font-face {
  font-family: 'Univers';
  src: url('./static/Univers-65-bold-latin-extended.woff');
  font-weight: 700;
  font-style: normal;
}
.MuiMobileStepper-dotActive {
  background: var(--trafineo-rot-100) !important;
}

.MuiPickersDay-root {
  margin: 0 2px !important;
}

.App {
  text-align: center;
  font-family: var(--font-family);
  min-width: 900px;
  min-height: 100vh;
  font-weight: var(--font-weight);
}

.AppMobile {
  text-align: center;
  font-family: var(--font-family);
  min-height: 100vh;
  font-weight: var(--font-weight);
}

.background {
  background: var(--background);
  background-size: 100% 100%;
  height: 100vh;
  width: 100%;
  position: absolute;
  z-index: -1;
}

.container {
  min-height: var(--app-height);
  min-width: 900px;
}

.blankBar {
  height: var(--header-height);
}

p {
  font-family: var(--font-family);
  font-size: 14px;
  font-weight: var(--font-weight);
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.26px;
  color: #414042;
}

button {
  font-weight: var(--button-font-weight);
  font-family: var(--font-family);
}

h1 {
  font-family: var(--font-family);
  font-size: 38px;
  font-weight: var(--font-weight);
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: normal;
  color: black;
}

h2 {
  font-family: var(--font-family);
  font-size: 19px;
  font-weight: var(--font-weight);
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.34px;
  color: #414042;
}

h3 {
  font-family: var(--font-family);
  font-size: 14px;
  font-weight: var(--font-weight);
  font-stretch: normal;
  font-style: normal;
  line-height: normal;
  letter-spacing: 0.31px;
  color: #414042;
}

a {
  text-decoration: none;
}
