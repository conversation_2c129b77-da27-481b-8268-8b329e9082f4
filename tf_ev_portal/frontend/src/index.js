import 'react-app-polyfill/ie11';
import 'react-app-polyfill/stable';
import React from 'react';
import ReactDOM from 'react-dom';
import { <PERSON>rowserRouter } from 'react-router-dom';
import './index.css';
import App from './App';
import './i18n';
import { grey } from '@material-ui/core/colors';
import * as serviceWorker from './serviceWorker';

import {
  getBranding,
  setBranding,
  getCookieValue,
  isInDemoMode,
} from './utils/helper';
import { ThemeProvider, createTheme } from '@mui/material/styles';

// handle redirect from rapyd
if (window.location.href.indexOf('?rapyd=true') !== -1) {
  const redirectUrl = getCookieValue('redirect');
  if (redirectUrl) {
    window.location.href = redirectUrl;
  }
}
// only display app when not embedded in an iframe
else if (window.location === window.parent.location) {
  if (!isInDemoMode()) {
    setBranding();
  } else if (!getBranding()) {
    setBranding();
  }
  document.querySelector('html').classList.add(getBranding());
  document.querySelector('body').style.display = 'block';
}

const theme = createTheme({
  palette: {
    primary: {
      main:
        getBranding() === 'bp'
          ? '#9acc00'
          : getBranding() === 'aral'
          ? '#0064cc'
          : '#c60018',
    },
    secondary: {
      main: grey[500],
    },
  },
});

//Load mockdata when in mock or demomode
if (
  (process.env.REACT_APP_USE_MOCK === 'true' || isInDemoMode()) &&
  !window.Cypress
) {
  import('./mock/server').then(({ makeServer }) => {
    makeServer();
    ReactDOM.render(
      <React.StrictMode>
        <BrowserRouter>
          <ThemeProvider theme={theme}>
            <App />
          </ThemeProvider>
        </BrowserRouter>
      </React.StrictMode>,
      document.getElementById('root'),
    );
    serviceWorker.register();
  });
} else {
  ReactDOM.render(
    <React.StrictMode>
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <App />
        </ThemeProvider>
      </BrowserRouter>
    </React.StrictMode>,
    document.getElementById('root'),
  );
  serviceWorker.unregister();
}
