// trainingInviteDriver
// trafineo
import trainingInviteDriver_trafineo_en from './trainingInviteDriver_trafineo_en.gif';
import trainingInviteDriver_trafineo_de from './trainingInviteDriver_trafineo_de.gif';
import trainingInviteDriver_trafineo_nl from './trainingInviteDriver_trafineo_nl.gif';
// bp
import trainingInviteDriver_bp_en from './trainingInviteDriver_bp_en.gif';
import trainingInviteDriver_bp_de from './trainingInviteDriver_bp_de.gif';
import trainingInviteDriver_bp_nl from './trainingInviteDriver_bp_nl.gif';
//aral
import trainingInviteDriver_aral_en from './trainingInviteDriver_aral_en.gif';
import trainingInviteDriver_aral_de from './trainingInviteDriver_aral_de.gif';
import trainingInviteDriver_aral_nl from './trainingInviteDriver_aral_nl.gif';

//trainingViewDriverAccount
// trafineo
import trainingViewDriverAccount_trafineo_en from './trainingViewDriverAccount_trafineo_en.gif';
import trainingViewDriverAccount_trafineo_de from './trainingViewDriverAccount_trafineo_de.gif';
import trainingViewDriverAccount_trafineo_nl from './trainingViewDriverAccount_trafineo_nl.gif';
// bp
import trainingViewDriverAccount_bp_en from './trainingViewDriverAccount_bp_en.gif';
import trainingViewDriverAccount_bp_de from './trainingViewDriverAccount_bp_de.gif';
import trainingViewDriverAccount_bp_nl from './trainingViewDriverAccount_bp_nl.gif';
//aral
import trainingViewDriverAccount_aral_en from './trainingViewDriverAccount_aral_en.gif';
import trainingViewDriverAccount_aral_de from './trainingViewDriverAccount_aral_de.gif';
import trainingViewDriverAccount_aral_nl from './trainingViewDriverAccount_aral_nl.gif';

//trainingRejectDriverTariff
// trafineo
import trainingRejectDriverTariff_trafineo_en from './trainingRejectDriverTariff_trafineo_en.gif';
import trainingRejectDriverTariff_trafineo_de from './trainingRejectDriverTariff_trafineo_de.gif';
import trainingRejectDriverTariff_trafineo_nl from './trainingRejectDriverTariff_trafineo_nl.gif';
// bp
import trainingRejectDriverTariff_bp_en from './trainingRejectDriverTariff_bp_en.gif';
import trainingRejectDriverTariff_bp_de from './trainingRejectDriverTariff_bp_de.gif';
import trainingRejectDriverTariff_bp_nl from './trainingRejectDriverTariff_bp_nl.gif';
//aral
import trainingRejectDriverTariff_aral_en from './trainingRejectDriverTariff_aral_en.gif';
import trainingRejectDriverTariff_aral_de from './trainingRejectDriverTariff_aral_de.gif';
import trainingRejectDriverTariff_aral_nl from './trainingRejectDriverTariff_aral_nl.gif';

//trainingApproveDriverTariff
// trafineo
import trainingApproveDriverTariff_trafineo_en from './trainingApproveDriverTariff_trafineo_en.gif';
import trainingApproveDriverTariff_trafineo_de from './trainingApproveDriverTariff_trafineo_de.gif';
import trainingApproveDriverTariff_trafineo_nl from './trainingApproveDriverTariff_trafineo_nl.gif';
// bp
import trainingApproveDriverTariff_bp_en from './trainingApproveDriverTariff_bp_en.gif';
import trainingApproveDriverTariff_bp_de from './trainingApproveDriverTariff_bp_de.gif';
import trainingApproveDriverTariff_bp_nl from './trainingApproveDriverTariff_bp_nl.gif';
//aral
import trainingApproveDriverTariff_aral_en from './trainingApproveDriverTariff_aral_en.gif';
import trainingApproveDriverTariff_aral_de from './trainingApproveDriverTariff_aral_de.gif';
import trainingApproveDriverTariff_aral_nl from './trainingApproveDriverTariff_aral_nl.gif';

//trainingAssignCard
// trafineo
import trainingAssignCard_trafineo_en from './trainingAssignCard_trafineo_en.gif';
import trainingAssignCard_trafineo_de from './trainingAssignCard_trafineo_de.gif';
import trainingAssignCard_trafineo_nl from './trainingAssignCard_trafineo_nl.gif';
// bp
import trainingAssignCard_bp_en from './trainingAssignCard_bp_en.gif';
import trainingAssignCard_bp_de from './trainingAssignCard_bp_de.gif';
import trainingAssignCard_bp_nl from './trainingAssignCard_bp_nl.gif';
//aral
import trainingAssignCard_aral_en from './trainingAssignCard_aral_en.gif';
import trainingAssignCard_aral_de from './trainingAssignCard_aral_de.gif';
import trainingAssignCard_aral_nl from './trainingAssignCard_aral_nl.gif';

//trainingPayoutReport
// trafineo
import trainingPayoutReport_trafineo_en from './trainingPayoutReport_trafineo_en.gif';
import trainingPayoutReport_trafineo_de from './trainingPayoutReport_trafineo_de.gif';
import trainingPayoutReport_trafineo_nl from './trainingPayoutReport_trafineo_nl.gif';
// bp
import trainingPayoutReport_bp_en from './trainingPayoutReport_bp_en.gif';
import trainingPayoutReport_bp_de from './trainingPayoutReport_bp_de.gif';
import trainingPayoutReport_bp_nl from './trainingPayoutReport_bp_nl.gif';
//aral
import trainingPayoutReport_aral_en from './trainingPayoutReport_aral_en.gif';
import trainingPayoutReport_aral_de from './trainingPayoutReport_aral_de.gif';
import trainingPayoutReport_aral_nl from './trainingPayoutReport_aral_nl.gif';

//trainingRequestUpdate
// trafineo
import trainingRequestUpdate_trafineo_en from './trainingRequestUpdate_trafineo_en.gif';
import trainingRequestUpdate_trafineo_de from './trainingRequestUpdate_trafineo_de.gif';
import trainingRequestUpdate_trafineo_nl from './trainingRequestUpdate_trafineo_nl.gif';
// bp
import trainingRequestUpdate_bp_en from './trainingRequestUpdate_bp_en.gif';
import trainingRequestUpdate_bp_de from './trainingRequestUpdate_bp_de.gif';
import trainingRequestUpdate_bp_nl from './trainingRequestUpdate_bp_nl.gif';
//aral
import trainingRequestUpdate_aral_en from './trainingRequestUpdate_aral_en.gif';
import trainingRequestUpdate_aral_de from './trainingRequestUpdate_aral_de.gif';
import trainingRequestUpdate_aral_nl from './trainingRequestUpdate_aral_nl.gif';

//trainingAddTeammember
// trafineo (only trafineo branding exists)
import trainingAddTeammember_trafineo_en from './trainingAddTeammember_trafineo_en.gif';
import trainingAddTeammember_trafineo_de from './trainingAddTeammember_trafineo_de.gif';
import trainingAddTeammember_trafineo_nl from './trainingAddTeammember_trafineo_nl.gif';

// JSON with all GIFs needed for FM-trainings, named [key]_[branding]_[language]

// IMPORTANT
// Training trainingViewDriverAccount is a different gif for trafineo branding. This is the current workaround to show a different gif for indirect FMs
const gifs = {
  trafineo: {
    trainingInviteDriver: {
      en: trainingInviteDriver_trafineo_en,
      de: trainingInviteDriver_trafineo_de,
      nl: trainingInviteDriver_trafineo_nl,
    },
    trainingRejectDriverTariff: {
      en: trainingRejectDriverTariff_trafineo_en,
      de: trainingRejectDriverTariff_trafineo_de,
      nl: trainingRejectDriverTariff_trafineo_nl,
    },
    trainingViewDriverAccount: {
      en: trainingViewDriverAccount_trafineo_en,
      de: trainingViewDriverAccount_trafineo_de,
      nl: trainingViewDriverAccount_trafineo_nl,
    },
    trainingApproveDriverTariff: {
      en: trainingApproveDriverTariff_trafineo_en,
      de: trainingApproveDriverTariff_trafineo_de,
      nl: trainingApproveDriverTariff_trafineo_nl,
    },
    trainingAssignCard: {
      en: trainingAssignCard_trafineo_en,
      de: trainingAssignCard_trafineo_de,
      nl: trainingAssignCard_trafineo_nl,
    },
    trainingPayoutReport: {
      en: trainingPayoutReport_trafineo_en,
      de: trainingPayoutReport_trafineo_de,
      nl: trainingPayoutReport_trafineo_nl,
    },
    trainingRequestUpdate: {
      en: trainingRequestUpdate_trafineo_en,
      de: trainingRequestUpdate_trafineo_de,
      nl: trainingRequestUpdate_trafineo_nl,
    },
    trainingAddTeammember: {
      en: trainingAddTeammember_trafineo_en,
      de: trainingAddTeammember_trafineo_de,
      nl: trainingAddTeammember_trafineo_nl,
    },
  },
  bp: {
    trainingInviteDriver: {
      en: trainingInviteDriver_bp_en,
      de: trainingInviteDriver_bp_de,
      nl: trainingInviteDriver_bp_nl,
    },
    trainingRejectDriverTariff: {
      en: trainingRejectDriverTariff_bp_en,
      de: trainingRejectDriverTariff_bp_de,
      nl: trainingRejectDriverTariff_bp_nl,
    },
    trainingViewDriverAccount: {
      en: trainingViewDriverAccount_bp_en,
      de: trainingViewDriverAccount_bp_de,
      nl: trainingViewDriverAccount_bp_nl,
    },
    trainingApproveDriverTariff: {
      en: trainingApproveDriverTariff_bp_en,
      de: trainingApproveDriverTariff_bp_de,
      nl: trainingApproveDriverTariff_bp_nl,
    },
    trainingAssignCard: {
      en: trainingAssignCard_bp_en,
      de: trainingAssignCard_bp_de,
      nl: trainingAssignCard_bp_nl,
    },
    trainingPayoutReport: {
      en: trainingPayoutReport_bp_en,
      de: trainingPayoutReport_bp_de,
      nl: trainingPayoutReport_bp_nl,
    },
    trainingRequestUpdate: {
      en: trainingRequestUpdate_bp_en,
      de: trainingRequestUpdate_bp_de,
      nl: trainingRequestUpdate_bp_nl,
    },
    trainingAddTeammember: {
      //include trafineo trainings to prevent break on demo portal
      en: trainingAddTeammember_trafineo_en,
      de: trainingAddTeammember_trafineo_de,
      nl: trainingAddTeammember_trafineo_nl,
    },
  },
  aral: {
    trainingInviteDriver: {
      en: trainingInviteDriver_aral_en,
      de: trainingInviteDriver_aral_de,
      nl: trainingInviteDriver_aral_nl,
    },
    trainingRejectDriverTariff: {
      en: trainingRejectDriverTariff_aral_en,
      de: trainingRejectDriverTariff_aral_de,
      nl: trainingRejectDriverTariff_aral_nl,
    },
    trainingViewDriverAccount: {
      en: trainingViewDriverAccount_aral_en,
      de: trainingViewDriverAccount_aral_de,
      nl: trainingViewDriverAccount_aral_nl,
    },
    trainingApproveDriverTariff: {
      en: trainingApproveDriverTariff_aral_en,
      de: trainingApproveDriverTariff_aral_de,
      nl: trainingApproveDriverTariff_aral_nl,
    },
    trainingAssignCard: {
      en: trainingAssignCard_aral_en,
      de: trainingAssignCard_aral_de,
      nl: trainingAssignCard_aral_nl,
    },
    trainingPayoutReport: {
      en: trainingPayoutReport_aral_en,
      de: trainingPayoutReport_aral_de,
      nl: trainingPayoutReport_aral_nl,
    },
    trainingRequestUpdate: {
      en: trainingRequestUpdate_aral_en,
      de: trainingRequestUpdate_aral_de,
      nl: trainingRequestUpdate_aral_nl,
    },
    trainingAddTeammember: {
      //include trafineo trainings to prevent break on demo portal
      en: trainingAddTeammember_trafineo_en,
      de: trainingAddTeammember_trafineo_de,
      nl: trainingAddTeammember_trafineo_nl,
    },
  },
};

export default gifs;
