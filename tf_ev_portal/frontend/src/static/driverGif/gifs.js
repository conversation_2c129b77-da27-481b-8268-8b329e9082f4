// trainingUpdateTariff
// trafineo
import trainingUpdateTariff_trafineo_en from './trainingUpdateTariff_trafineo_en.gif';
import trainingUpdateTariff_trafineo_de from './trainingUpdateTariff_trafineo_de.gif';
import trainingUpdateTariff_trafineo_nl from './trainingUpdateTariff_trafineo_nl.gif';
// bp
import trainingUpdateTariff_bp_en from './trainingUpdateTariff_bp_en.gif';
import trainingUpdateTariff_bp_de from './trainingUpdateTariff_bp_de.gif';
import trainingUpdateTariff_bp_nl from './trainingUpdateTariff_bp_nl.gif';
//aral
import trainingUpdateTariff_aral_en from './trainingUpdateTariff_aral_en.gif';
import trainingUpdateTariff_aral_de from './trainingUpdateTariff_aral_de.gif';
import trainingUpdateTariff_aral_nl from './trainingUpdateTariff_aral_nl.gif';

//trainingUpdateWallbox
// trafineo
import trainingUpdateWallbox_trafineo_en from './trainingUpdateWallbox_trafineo_en.gif';
import trainingUpdateWallbox_trafineo_de from './trainingUpdateWallbox_trafineo_de.gif';
import trainingUpdateWallbox_trafineo_nl from './trainingUpdateWallbox_trafineo_nl.gif';
// bp
import trainingUpdateWallbox_bp_en from './trainingUpdateWallbox_bp_en.gif';
import trainingUpdateWallbox_bp_de from './trainingUpdateWallbox_bp_de.gif';
import trainingUpdateWallbox_bp_nl from './trainingUpdateWallbox_bp_nl.gif';
//aral
import trainingUpdateWallbox_aral_en from './trainingUpdateWallbox_aral_en.gif';
import trainingUpdateWallbox_aral_de from './trainingUpdateWallbox_aral_de.gif';
import trainingUpdateWallbox_aral_nl from './trainingUpdateWallbox_aral_nl.gif';

//trainingDriverPayoutReport
// trafineo
import trainingDriverPayoutReport_trafineo_en from './trainingDriverPayoutReport_trafineo_en.gif';
import trainingDriverPayoutReport_trafineo_de from './trainingDriverPayoutReport_trafineo_de.gif';
import trainingDriverPayoutReport_trafineo_nl from './trainingDriverPayoutReport_trafineo_nl.gif';
// bp
import trainingDriverPayoutReport_bp_en from './trainingDriverPayoutReport_bp_en.gif';
import trainingDriverPayoutReport_bp_de from './trainingDriverPayoutReport_bp_de.gif';
import trainingDriverPayoutReport_bp_nl from './trainingDriverPayoutReport_bp_nl.gif';
//aral
import trainingDriverPayoutReport_aral_en from './trainingDriverPayoutReport_aral_en.gif';
import trainingDriverPayoutReport_aral_de from './trainingDriverPayoutReport_aral_de.gif';
import trainingDriverPayoutReport_aral_nl from './trainingDriverPayoutReport_aral_nl.gif';

// //trainingApproveDriverTariff
// // trafineo
// import trainingApproveDriverTariff_trafineo_en from './trainingApproveDriverTariff_trafineo_en.gif';
// import trainingApproveDriverTariff_trafineo_de from './trainingApproveDriverTariff_trafineo_de.gif';
// import trainingApproveDriverTariff_trafineo_nl from './trainingApproveDriverTariff_trafineo_nl.gif';
// // bp
// import trainingApproveDriverTariff_bp_en from './trainingApproveDriverTariff_bp_en.gif';
// import trainingApproveDriverTariff_bp_de from './trainingApproveDriverTariff_bp_de.gif';
// import trainingApproveDriverTariff_bp_nl from './trainingApproveDriverTariff_bp_nl.gif';
// //aral
// import trainingApproveDriverTariff_aral_en from './trainingApproveDriverTariff_aral_en.gif';
// import trainingApproveDriverTariff_aral_de from './trainingApproveDriverTariff_aral_de.gif';
// import trainingApproveDriverTariff_aral_nl from './trainingApproveDriverTariff_aral_nl.gif';

// //trainingAssignCard
// // trafineo
// import trainingAssignCard_trafineo_en from './trainingAssignCard_trafineo_en.gif';
// import trainingAssignCard_trafineo_de from './trainingAssignCard_trafineo_de.gif';
// import trainingAssignCard_trafineo_nl from './trainingAssignCard_trafineo_nl.gif';
// // bp
// import trainingAssignCard_bp_en from './trainingAssignCard_bp_en.gif';
// import trainingAssignCard_bp_de from './trainingAssignCard_bp_de.gif';
// import trainingAssignCard_bp_nl from './trainingAssignCard_bp_nl.gif';
// //aral
// import trainingAssignCard_aral_en from './trainingAssignCard_aral_en.gif';
// import trainingAssignCard_aral_de from './trainingAssignCard_aral_de.gif';
// import trainingAssignCard_aral_nl from './trainingAssignCard_aral_nl.gif';

//trainingDriverRequestCard
// trafineo (only trafineo branding exists since this is indriect driver only)
import trainingDriverRequestCard_trafineo_en from './trainingDriverRequestCard_trafineo_en.gif';
import trainingDriverRequestCard_trafineo_de from './trainingDriverRequestCard_trafineo_de.gif';
import trainingDriverRequestCard_trafineo_nl from './trainingDriverRequestCard_trafineo_nl.gif';

// JSON with all GIFs needed for trainings, named [key]_[branding]_[language]
const gifs = {
  trafineo: {
    trainingUpdateTariff: {
      en: trainingUpdateTariff_trafineo_en,
      de: trainingUpdateTariff_trafineo_de,
      nl: trainingUpdateTariff_trafineo_nl,
    },
    trainingDriverPayoutReport: {
      en: trainingDriverPayoutReport_trafineo_en,
      de: trainingDriverPayoutReport_trafineo_de,
      nl: trainingDriverPayoutReport_trafineo_nl,
    },
    trainingUpdateWallbox: {
      en: trainingUpdateWallbox_trafineo_en,
      de: trainingUpdateWallbox_trafineo_de,
      nl: trainingUpdateWallbox_trafineo_nl,
    },
    trainingDriverRequestCard: {
      en: trainingDriverRequestCard_trafineo_en,
      de: trainingDriverRequestCard_trafineo_de,
      nl: trainingDriverRequestCard_trafineo_nl,
    },
    // trainingApproveDriverTariff: {
    //   en: trainingApproveDriverTariff_trafineo_en,
    //   de: trainingApproveDriverTariff_trafineo_de,
    //   nl: trainingApproveDriverTariff_trafineo_nl,
    // },
    // trainingAssignCard: {
    //   en: trainingAssignCard_trafineo_en,
    //   de: trainingAssignCard_trafineo_de,
    //   nl: trainingAssignCard_trafineo_nl,
    // },
  },
  bp: {
    trainingUpdateTariff: {
      en: trainingUpdateTariff_bp_en,
      de: trainingUpdateTariff_bp_de,
      nl: trainingUpdateTariff_bp_nl,
    },
    trainingDriverPayoutReport: {
      en: trainingDriverPayoutReport_bp_en,
      de: trainingDriverPayoutReport_bp_de,
      nl: trainingDriverPayoutReport_bp_nl,
    },
    trainingUpdateWallbox: {
      en: trainingUpdateWallbox_bp_en,
      de: trainingUpdateWallbox_bp_de,
      nl: trainingUpdateWallbox_bp_nl,
    },
    trainingDriverRequestCard: {
      en: trainingDriverRequestCard_trafineo_en,
      de: trainingDriverRequestCard_trafineo_de,
      nl: trainingDriverRequestCard_trafineo_nl,
    },
    // trainingApproveDriverTariff: {
    //   en: trainingApproveDriverTariff_bp_en,
    //   de: trainingApproveDriverTariff_bp_de,
    //   nl: trainingApproveDriverTariff_bp_nl,
    // },
    // trainingAssignCard: {
    //   en: trainingAssignCard_bp_en,
    //   de: trainingAssignCard_bp_de,
    //   nl: trainingAssignCard_bp_nl,
    // },
  },
  aral: {
    trainingDriverRequestCard: {
      en: trainingDriverRequestCard_trafineo_en,
      de: trainingDriverRequestCard_trafineo_de,
      nl: trainingDriverRequestCard_trafineo_nl,
    },
    trainingUpdateTariff: {
      en: trainingUpdateTariff_aral_en,
      de: trainingUpdateTariff_aral_de,
      nl: trainingUpdateTariff_aral_nl,
    },
    trainingDriverPayoutReport: {
      en: trainingDriverPayoutReport_aral_en,
      de: trainingDriverPayoutReport_aral_de,
      nl: trainingDriverPayoutReport_aral_nl,
    },
    trainingUpdateWallbox: {
      en: trainingUpdateWallbox_aral_en,
      de: trainingUpdateWallbox_aral_de,
      nl: trainingUpdateWallbox_aral_nl,
    },
    // trainingApproveDriverTariff: {
    //   en: trainingApproveDriverTariff_aral_en,
    //   de: trainingApproveDriverTariff_aral_de,
    //   nl: trainingApproveDriverTariff_aral_nl,
    // },
    // trainingAssignCard: {
    //   en: trainingAssignCard_aral_en,
    //   de: trainingAssignCard_aral_de,
    //   nl: trainingAssignCard_aral_nl,
    // },
  },
};

export default gifs;
