import { useEffect, useState } from 'react';
import { useNavigate, Routes, Navigate } from 'react-router-dom';
import { Route } from 'react-router-dom';
import jwtDecode from 'jwt-decode';
import { HeaderBar, Footer } from './Components';
import i18next from './i18n';
import logger from './utils/logger';
import './App.css';
import { useLocalStorage, useReducerLocalStorage } from './utils';
import { refreshToken } from './Views/Login';
import requester, { idpRequest } from './utils/requester';
import nlLocale from 'date-fns/locale/nl';
import deLocale from 'date-fns/locale/de';
import enLocale from 'date-fns/locale/en-GB';
import evDriverReducer from './evDriverReducer';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import {
  baseViews,
  driverModifyViews,
  driverWelcomeViews,
  fleetManagerViews,
  superUserViews,
  acceptDps,
  error,
  driverInitialDataEnteredViews,
  salesAdminViews,
  supportViews,
} from './Views/Views';
import approvalStatus from './constants/approvalStatus';
import ContextProvider, { defaultDialogContext } from './ContextProvider';
import { isMobile } from 'react-device-detect';
import { db } from './db';
import { useLocation } from 'react-router-dom';
import qs from 'querystring';
import CookieBanner from './Components/CookieBanner';
import TagManager from 'react-gtm-module';
import pageTypes from './pageTypes';

const {
  REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE,
  REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE,
  REACT_APP_KEYCLOAK_DRIVER_ROLE,
  REACT_APP_KEYCLOAK_APP_DRIVER_ROLE,
  REACT_APP_KEYCLOAK_SUPERUSER_ROLE,
  REACT_APP_KEYCLOAK_SUPPORT_ROLE,
  REACT_APP_KEYCLOAK_SALESADMIN_ROLE,
} = process.env;

const App = () => {
  const [isLoggedIn, setIsLoggedIn] = useLocalStorage(
    'user-is-logged-in',
    false,
  );
  const [user, setUser] = useLocalStorage('user-token', '');
  const [dialogData, setDialogData] = useState(defaultDialogContext);
  const [role, setRole] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [evDriverData, evDriverDispatch] = useReducerLocalStorage(
    'ev-driver-data',
    evDriverReducer,
    null,
    undefined,
  );

  const [lang, setLang] = useLocalStorage('selected-language', 'en');
  const [reminder, setReminder] = useLocalStorage('reminder', undefined);
  const [onboardingMode, setOnboardingMode] = useState(false);
  const [views, setViews] = useState(baseViews);
  const [viewSet, setViewSet] = useState(false);
  const [isDriverRegistered, setIsDriverRegistered] = useState(false);
  const [homeChargingAustriaMode, setHomeChargingAustriaMode] = useState(false);
  const [maintenanceMode, setMaintenanceMode] = useState(false);
  const [oldUrl, setOldUrl] = useState('');

  const navigate = useNavigate();

  //GA4 data layer initialzation
  window.dataLayer = window.dataLayer || [];

  //GA4 page view
  useEffect(() => {
    //initialize TagManager ever page
    if (
      oldUrl !== window.location.href &&
      window.location.href[window.location.href.length - 1] !== '/' &&
      oldUrl.split('?')[0] !== window.location.href.split('?')[0]
    ) {
      TagManager.initialize({ gtmId: 'GTM-T25HDDT8' });

      // timeout needed to prevent initial language set in ./i18n for all pageviews in /login
      setTimeout(() => {
        TagManager.dataLayer({
          dataLayer: {
            event: 'page_view',
            page: {
              language: i18next.language,
              name:
                window.location.pathname === '/'
                  ? window.location.pathname + window.location.search
                  : window.location.pathname,
              type: pageTypes[window.location.pathname],
            },
            site: {
              branding: window.localStorage.getItem('branding'),
            },
            user: {
              account_id: user ? jwtDecode(user.access_token).sub : null,
              account_name: user ? jwtDecode(user.access_token).sub : null,
              id: user ? jwtDecode(user.access_token).jti : null,
              login_status: isLoggedIn ? 'logged-in' : 'logged-out',
              type: user ? jwtDecode(user.access_token).role : null,
            },
          },
        });
      }, 1000);
      setOldUrl(window.location.href);
    }
    if (
      oldUrl !== window.location.href &&
      window.location.href[window.location.href.length - 1] === '/' &&
      (evDriverData?.approvalStatus?.indexOf('initial_data_rejected') > -1 ||
        (user && jwtDecode(user.access_token).role === 'Superuser'))
    ) {
      TagManager.initialize({ gtmId: 'GTM-P84DC3Q' });
      setTimeout(() => {
        TagManager.dataLayer({
          dataLayer: {
            event: 'page_view',
            page: {
              language: i18next.language,
              name: window.location.pathname,
              type: window.location.href,
            },
            site: {
              branding: window.localStorage.getItem('branding'),
            },
            user: {
              account_id: user ? jwtDecode(user.access_token).sub : null,
              account_name: user ? jwtDecode(user.access_token).sub : null,
              id: user ? jwtDecode(user.access_token).jti : null,
              login_status: isLoggedIn ? 'logged-in' : 'logged-out',
              type: user ? jwtDecode(user.access_token).role : null,
            },
          },
        });
      }, 1000);
      setOldUrl(window.location.href);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [user, isLoggedIn, oldUrl, window.location.href]);

  const logOut = async (withError) => {
    idpRequest().post(
      process.env.REACT_APP_KEYCLOAK_LOGOUT_URI,
      qs.stringify({
        refresh_token: user.refresh_token,
      }),
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      },
    );
    setUser('');
    sessionStorage.clear();
    evDriverDispatch({ type: 'clear' });
    db.delete();
    setViews(baseViews);
    setIsLoggedIn(false);
    setIsDriverRegistered(false);
    if (withError) {
      navigate('/login?error=true');
    } else {
      navigate('/login');
    }
  };
  db.open();
  const localeMap = {
    en: enLocale,
    nl: nlLocale,
    de: deLocale,
  };

  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const payoutRedirect = parsedQueryString.payouts;

  useEffect(() => {
    try {
      if (isLoggedIn) {
        const decodedUser = jwtDecode(user.access_token);
        const userRole = decodedUser.role;
        if (userRole) {
          setRole(userRole);
          if (
            userRole.toLowerCase() ===
              REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE ||
            userRole.toLowerCase() ===
              REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE
          ) {
            const userViews = { ...baseViews, ...fleetManagerViews };
            setViews(userViews);
            if (payoutRedirect) {
              navigate('/payouts?redirect=true');
            }
          } else if (
            userRole.toLowerCase() === REACT_APP_KEYCLOAK_SALESADMIN_ROLE
          ) {
            const userViews = { ...baseViews, ...salesAdminViews };
            setViews(userViews);
          } else if (
            userRole.toLowerCase() === REACT_APP_KEYCLOAK_SUPERUSER_ROLE
          ) {
            const userViews = { ...baseViews, ...superUserViews };
            setViews(userViews);
          } else if (
            userRole.toLowerCase() === REACT_APP_KEYCLOAK_SUPPORT_ROLE
          ) {
            const userViews = { ...baseViews, ...supportViews };
            setViews(userViews);
          } else if (
            (userRole.toLowerCase() === REACT_APP_KEYCLOAK_DRIVER_ROLE ||
              userRole.toLowerCase() === REACT_APP_KEYCLOAK_APP_DRIVER_ROLE) &&
            window.location.href.indexOf('?redirect=true') === -1 &&
            window.location.href.indexOf('?rapyd=true') === -1
          ) {
            (async () => {
              try {
                setIsLoading(true);
                const { data } = await requester().get(
                  `/Ev_Driver_Data/Get_Ev_Driver_Data`,
                );
                evDriverDispatch({ type: 'set', value: data });
                setIsLoading(false);
              } catch (err) {
                setIsLoading(false);
                logger(true).error(err.message);
                logOut(true);
              }
              try {
                const { data: remiderRsp } = await requester().get(`/reminder`);
                if (remiderRsp) {
                  const contractReminder = remiderRsp.reminders?.filter(
                    (e) =>
                      (e.reminder_type =
                        'electricity_contract_update_reminder'),
                  )[0];
                  setReminder(
                    contractReminder ? contractReminder.interval : undefined,
                  );
                }
              } catch (err) {
                setReminder(undefined);
                setIsLoading(false);
                logger(true).error(err.message);
              }
            })();
            if (payoutRedirect) {
              navigate('/payouts');
            }
          }
        } else {
          navigate('/error');
        }
      } else if (window.location.pathname === '/') {
        navigate('/login');
      }
    } catch (err) {
      logger().error(err.message);
      logOut();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn]);

  useEffect(() => {
    // Refresh JWT Token with IDP to stay logged in, if token is invalid/expired log out
    const refreshLoginToken = async () => {
      try {
        if (user.refresh_token) {
          const newUserData = (await refreshToken(user.refresh_token)).data;
          setUser(newUserData);
          setIsLoggedIn(true);
        }
      } catch (err) {
        console.error(err);
        logger(true).error(err.message);
        logOut();
      }
    };
    if (window.location.pathname !== '/logout') {
      refreshLoginToken();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    i18next.changeLanguage(lang, (err) => {
      if (err) {
        logger().error(err);
      }
      return true;
    });
  }, [lang]);

  useEffect(() => {
    if (Object.keys(views).length > Object.keys(baseViews).length + 1) {
      setViewSet(true);
    }
  }, [views]);

  useEffect(() => {
    setHomeChargingAustriaMode(
      window.location.pathname.indexOf('/home-charging-austria') === 0,
    );
    setMaintenanceMode(window.location.pathname.indexOf('/maintenance') === 0);
  }, []);

  useEffect(() => {
    if (
      role &&
      (role.toLowerCase() === REACT_APP_KEYCLOAK_DRIVER_ROLE ||
        role.toLowerCase() === REACT_APP_KEYCLOAK_APP_DRIVER_ROLE)
    ) {
      let driverViews;
      if (isLoading) {
      } else if (evDriverData?.approvalStatus?.length > 0) {
        if (evDriverData.dpsAccepted) {
          switch (
            evDriverData.approvalStatus.length > 1 &&
            evDriverData.approvalStatus[0] === approvalStatus.appUserOnly
              ? evDriverData.approvalStatus[1]
              : evDriverData.approvalStatus[0]
          ) {
            case approvalStatus.invited:
            case approvalStatus.loggedInOnce:
            case approvalStatus.initialDataRejected:
              driverViews = driverWelcomeViews;
              break;
            case approvalStatus.initialDataEntered:
              driverViews = driverInitialDataEnteredViews;
              break;
            case approvalStatus.bankingModified:
            case approvalStatus.tariffModified:
            case approvalStatus.wallboxModified:
            case approvalStatus.newCardRequest:
              setIsDriverRegistered(true);
              driverViews = driverModifyViews;
              break;
            case approvalStatus.bankingUpdateRequested:
            case approvalStatus.tariffUpdateRequested:
            case approvalStatus.wallboxUpdateRequested:
            case approvalStatus.approved:
              setIsDriverRegistered(true);
              driverViews = driverModifyViews;
              break;
            default:
              driverViews = error;
          }
        } else {
          driverViews = acceptDps;
        }
      } else {
        driverViews = error;
      }
      const userViews = { ...baseViews, ...driverViews };
      setViews(userViews);
    }
  }, [isLoading, evDriverData, role]);

  const onLanguageSelect = (lng) => {
    if (window.location.pathname === '/changePassword') {
      const urlParams = new URLSearchParams(window.location.search);
      urlParams.set('isManualLanguage', true);
    }
    setLang(lng);
  };

  return (
    <LocalizationProvider locale={localeMap[lang]} dateAdapter={AdapterDateFns}>
      <div className={isMobile ? 'AppMobile' : 'App'}>
        <div className="background" />
        <ContextProvider
          {...{
            user,
            logOut,
            lang,
            onboardingMode,
            setOnboardingMode,
            setLang,
            evDriverData,
            evDriverDispatch,
            dialogData,
            setDialogData,
            reminder,
            setReminder,
            role,
          }}
        >
          <div className={!isMobile && 'container'}>
            <div className="blankBar" />
            <Routes>
              {isLoggedIn && (
                <Route
                  path="/login/reimbursements"
                  element={<Navigate to="/payouts" />}
                />
              )}

              {Object.entries(views).map(
                ([path, { view: View, props }], index) => {
                  return (
                    <Route
                      key={`ViewRoute-${index}`}
                      path={path}
                      exact // this might be toggled based on view
                      element={
                        <>
                          <HeaderBar
                            onLanguageSelect={onLanguageSelect}
                            language={lang}
                            onLogout={logOut}
                            isDriverRegistered={isDriverRegistered}
                            homeChargingAustria={homeChargingAustriaMode}
                            onboardingMode={props?.isViewInitial}
                          />
                          {path === '/login' ? (
                            <View {...{ setIsLoggedIn, setUser, isLoggedIn }} />
                          ) : path === '/changePassword' ? (
                            <View {...{ isLoggedIn }} />
                          ) : (
                            <View {...props} />
                          )}
                        </>
                      }
                    ></Route>
                  );
                },
              )}

              {(viewSet && <Route path="*" element={<Navigate to="/" />} />) ||
                (!isLoggedIn && (
                  <>
                    <Route
                      path="/login/reimbursements"
                      element={<Navigate to="/login?payouts=true" />}
                    />
                    <Route path="*" element={<Navigate to="/login" />} />
                  </>
                ))}
            </Routes>
          </div>
          {/* exclude all pages which are linked in the cookie banner itself */}
          {window.location.pathname.indexOf('/cookie-settings') !== 0 &&
            window.location.pathname.indexOf('/myinformation') !== 0 &&
            window.location.pathname.indexOf('/settings') !== 0 &&
            window.location.pathname.indexOf('/imprint') !== 0 &&
            window.location.pathname.indexOf('/terms-and-conditions') !== 0 &&
            window.location.pathname.indexOf('/data-privacy-statement') !==
              0 && <CookieBanner />}

          {!isMobile && !homeChargingAustriaMode && !maintenanceMode && (
            <Footer />
          )}
        </ContextProvider>
      </div>
    </LocalizationProvider>
  );
};
export default App;
