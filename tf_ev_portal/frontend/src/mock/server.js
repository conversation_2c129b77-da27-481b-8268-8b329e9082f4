/* eslint-disable no-shadow */
import { Server, RestSerializer, Response } from 'miragejs';
import qs from 'querystring';
import jwtDecode from 'jwt-decode';
import {
  users,
  Ev_Driver_Data,
  FleetManagers,
  StatusDirectChannelData,
  GetDriverData,
  reimbursements,
  chargingSessions,
  salesLeads,
  statusHistory,
  myTeamFleetManagers,
  onboardingRequests,
  vehicleList,
  Payouts,
  PayoutDownload,
  SalesAdmins,
  newTeamData,
  approvalData,
  chargingSessionsSustainabilityDownload,
  chargingSessionsSustainabilityAggregated,
} from './data/index.ts';
import {
  averageWorkprice,
  card_overview,
  card_overview_by_driver,
  card_overview_by_driver_by_mail,
  totalEnergy,
  paidOut,
} from './dashboardData/index.ts';

const {
  REACT_APP_KEYCLOAK_TOKEN_URI,
  REACT_APP_API_BASEURL,
  REACT_APP_SERVICE_BASEURL,
  REACT_APP_KEYCLOAK_URL,
} = process.env;

export function makeServer({ environment = 'development' } = {}) {
  console.log('logging server');
  const server = new Server({
    environment,
    serializers: {
      application: RestSerializer,
    },

    routes() {
      this.passthrough('/terms-and-conditions-aral.pdf');
      this.passthrough('/data-privacy-statement-de.pdf');
      this.passthrough('/data-privacy-statement-nl.pdf');
      this.passthrough('/test2.pdf');
      this.passthrough('/sample2.pdf');
      this.passthrough('/static/*');
      this.post(
        `${REACT_APP_SERVICE_BASEURL}/approvals/v1/approval`,
        (schema, request) => {
          return new Response(
            500,
            {},
            {
              status_code: 3000,
              status_message:
                'More than one row with the given identifier was found: 5993, for class: com.xintegrate.domain.fes.UserRoleVehicle',
              timestamp: '2024-09-24T06:10:10.663+00:00',
            },
          );
        },
      );
      this.patch(
        `${REACT_APP_SERVICE_BASEURL}/approvals/v1/approval/:id`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              status_code: 3000,
              status_message:
                'More than one row with the given identifier was found: 5993, for class: com.xintegrate.domain.fes.UserRoleVehicle',
              timestamp: '2024-09-24T06:10:10.663+00:00',
            },
          );
        },
      );
      this.post(
        `${REACT_APP_KEYCLOAK_URL}${REACT_APP_KEYCLOAK_TOKEN_URI}`,
        (schema, request) => {
          const { username, refresh_token } = qs.parse(request.requestBody);
          if (username) {
            if (users[username]) {
              return new Response(
                200,
                {},
                {
                  access_token: users[username].jwt,
                  refresh_token: users[username].jwt,
                },
              );
            }
            if (/^\d{3}$/.test(username)) {
              return new Response(parseInt(username), {});
            }
            return new Response(401, {});
          }
          return new Response(
            200,
            {},
            {
              access_token: refresh_token,
              refresh_token,
            },
          );
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data/Get_Ev_Driver_Data`,
        (schema, request) => {
          const { preferred_username } = jwtDecode(
            request.requestHeaders.Authorization,
          );

          return new Response(200, {}, Ev_Driver_Data[preferred_username]);
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data/Patch_Ev_Driver_Data`,
        (schema, request) => {
          const { preferred_username } = jwtDecode(
            request.requestHeaders.Authorization,
          );

          return new Response(200, {}, Ev_Driver_Data[preferred_username]);
        },
      );
      this.get(
        `${REACT_APP_KEYCLOAK_URL}/login-actions/action-token`,
        (schema, request) => {
          return new Response(200, {});
        },
      );
      this.put(
        `${REACT_APP_API_BASEURL}/Reimbursement/Put_Cancel_Reimbursement_Service`,
        (schema, request) => {
          return new Response(200, {}, 'reimbursement service deactivated');
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Additional_Information/Post_DirectChannel`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              root: {
                card_information_rejects: [
                  {
                    card_number: 700674072111001895,
                    error_message:
                      "The user has no permission to change 'email_address_ev_driver', as the ev_driver is already invited",
                  },
                ],
              },
            },
          );
        },
      );
      this.post(`${REACT_APP_API_BASEURL}/driver_management`, () => {
        return new Response(200, {}, {});
      });
      this.put(`${REACT_APP_API_BASEURL}/driver_management`, () => {
        return new Response(200, {}, {});
      });
      this.patch(`${REACT_APP_API_BASEURL}/driver_management/:id`, () => {
        return new Response(200, {}, {});
      });
      this.get(`http://localhost:3000/idp/login-actions/action-token`, () => {
        return new Response(200, {}, {});
      });
      this.delete(`${REACT_APP_API_BASEURL}/driver_management/:email`, () => {
        return new Response(200, {}, {});
      });
      this.post(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data_Approval/Post_Driver_Data_Approval`,
        () => {
          return new Response(
            200,
            {},
            'The request could be processed successfully.',
          );
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data_Approval/Post_Update_Data_Request`,
        (schema, request) => {
          return new Response(200, {}, 'Update data request send');
        },
      );
      this.post(
        `${REACT_APP_SERVICE_BASEURL}/approvals/v1`,
        (schema, request) => {
          return new Response(200, {}, '');
        },
      );
      this.get(
        `${REACT_APP_SERVICE_BASEURL}/users/v1/users`,
        (schema, request) => {
          return new Response(200, {}, newTeamData);
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}//Policies/Post_PoliciesAccepted`,
        (schema, request) => {
          return new Response(200, {}, '');
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data/Post_Ev_Driver_Data`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data/Patch_Ev_Driver_Data`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data_Approval/Post_Driver_Data_Approval`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/card_overview_by_driver`,
        (schema, request) => {
          // return new Response(
          //   504,
          //   { some: 'header' },
          //   { errors: ['name cannot be blank'] },
          // );
          return new Response(
            200,
            {},
            request.queryParams?.ev_driver_idp_id
              ? card_overview_by_driver_by_mail
              : card_overview_by_driver,
          );
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/payout_reports`, (schema, request) => {
        return new Response(200, { 'x-total-count': 10 }, Payouts);
      });
      this.get(
        `${REACT_APP_API_BASEURL}/payout_reports/:id`,
        (schema, request) => {
          return new Response(200, { 'x-total-count': 10 }, PayoutDownload);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/chargingsessions`,
        (schema, request) => {
          // return new Response(
          //   504,
          //   { some: 'header' },
          //   { errors: ['name cannot be blank'] },
          // );
          return new Response(200, { 'x-total-count': 15 }, chargingSessions);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/stats/cdr_totalenergy`,
        (schema, request) => {
          // return new Response(
          //   504,
          //   { some: 'header' },
          //   { errors: ['name cannot be blank'] },
          // );
          return new Response(200, {}, totalEnergy);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/stats/cdr_workprice`,
        (schema, request) => {
          return new Response(200, {}, averageWorkprice);
          // return new Response(
          //   504,
          //   { some: 'header' },
          //   { errors: ['name cannot be blank'] },
          // );
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/stats/payments`, (schema, request) => {
        return new Response(200, {}, paidOut);
        // return new Response(
        //   504,
        //   { some: 'header' },
        //   { errors: ['name cannot be blank'] },
        // );
      });
      this.get(
        `${REACT_APP_API_BASEURL}/authorities/fleetmanagers`,
        (schema, request) => {
          return new Response(200, {}, FleetManagers);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Policies/Get_Policies`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              role: 'Driver',
              language: 'DE',
              type: 'DPS',
              message: 'data-privacy-statement-en.pdf',
            },
          );
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/card_overview`,
        (schema, request) => {
          return new Response(200, {}, card_overview);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/reimbursement`,
        (schema, request) => {
          return new Response(200, {}, reimbursements);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/reimbursement/:id`,
        (schema, request) => {
          return new Response(200, {}, reimbursements);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/charging_sessions`,
        (schema, request) => {
          return new Response(200, {}, chargingSessions);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Information_Overview/charging_sessions/:id`,
        (schema, request) => {
          return new Response(200, {}, chargingSessions);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Reimbursement/Get_Status_DirectChannel`,
        (schema, request) => {
          return new Response(200, {}, StatusDirectChannelData);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data_Approval/Get_Driver_Data`,
        (schema, request) => {
          return new Response(
            200,
            {},
            GetDriverData(request.queryParams.card_number),
          );
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data_Approval/Contract_Upload`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              tariff:
                'data:application/pdf;base64,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',
            },
          );
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Rapyd_User_Management/Rapyd_User`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              redirect_url: '/rapydMock?redirect=true',
            },
          );
        },
        { timing: 500 },
      );
      this.put(
        `${REACT_APP_API_BASEURL}/Rapyd_User_Management/User_Token`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              last_four_iban_digits: '9999',
            },
          );
        },
        { timing: 500 },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/Ev_Driver_Data/Contract_Upload`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/trainings/:id/user`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/sales/leads`, (schema, request) => {
        return new Response(200, {}, salesLeads);
      });
      this.put(`${REACT_APP_API_BASEURL}/sales/leads`, (schema, request) => {
        return new Response(200, {}, salesLeads);
      });
      this.get(
        `${REACT_APP_API_BASEURL}/status_history/:idpId`,
        (schema, request) => {
          return new Response(200, {}, statusHistory);
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/status_history`, (schema, request) => {
        return new Response(200, {}, statusHistory);
      });
      this.post(
        `${REACT_APP_API_BASEURL}/authorities/fleetmanagers/:idpId/companies`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.delete(
        `${REACT_APP_API_BASEURL}/authorities/fleetmanagers/:idpId/companies/:companyId`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/fm_management/:email`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/fm_management/`,
        (schema, request) => {
          return new Response(
            200,
            {},
            {
              idpError: {
                errorCode: 409,
                errorMessage: {
                  errorMessage: 'User exists with same username',
                },
              },
            },
          );
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/fm_management/as_superuser`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/onboarding/registration`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/onboarding/registration`,
        (schema, request) => {
          return new Response(200, {}, onboardingRequests);
        },
      );
      this.get(
        `${REACT_APP_API_BASEURL}/User_Management`,
        (schema, request) => {
          return new Response(200, {}, SalesAdmins);
        },
      );
      this.delete(
        `${REACT_APP_API_BASEURL}/User_Management/:id`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/User_Management/:id`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/onboarding/approval/:id`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/:version/approval/`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.get(
        `${REACT_APP_SERVICE_BASEURL}/approvals/v1/approval`,
        (schema, request) => {
          return new Response(200, {}, approvalData);
        },
      );
      this.get(
        `${REACT_APP_SERVICE_BASEURL}/chargingsessions/v1/chargingsessions/`,
        (schema, request) => {
          return new Response(200, {}, chargingSessionsSustainabilityDownload);
        },
      );

      this.get(
        `${REACT_APP_SERVICE_BASEURL}/chargingsessions/v1/chargingsessions/aggregated`,
        (schema, request) => {
          return new Response(
            200,
            {},
            chargingSessionsSustainabilityAggregated,
          );
        },
      );
      this.delete(
        `${REACT_APP_API_BASEURL}/fm_management/:email`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/fm_management/:email/reinvite`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.patch(
        `${REACT_APP_API_BASEURL}/authorities/company`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/fm_management`, (schema, request) => {
        return new Response(200, {}, myTeamFleetManagers);
      });
      this.get(
        `${REACT_APP_API_BASEURL}/cards/:cardnumber/availability/`,
        (schema, request) => {
          return new Response(
            200,
            {},
            { state: 'successful', type: 'indirect' },
          );
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/DocumentExtraction`,
        (schema, request) => {
          return new Response(200, {}, 'Testresponse');
        },
      );
      this.post(
        `${REACT_APP_API_BASEURL}/SendSupportRequest`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
      this.post(`${REACT_APP_API_BASEURL}/FileUpload`, (schema, request) => {
        return new Response(200, {}, {});
      });
      this.get(`${REACT_APP_API_BASEURL}/vehicle_list`, (schema, request) => {
        return new Response(200, {}, vehicleList);
      });
      this.post(
        `${REACT_APP_API_BASEURL}/ev_driver/:ev_driver_id/token`,
        () => {
          return new Response(200, {}, {});
        },
      );
      this.get(`${REACT_APP_API_BASEURL}/reminder`, (schema, request) => {
        return new Response(
          200,
          {},
          {
            reminders: [
              {
                reminder_type: 'electricity_contract_update_reminder',
                last_reminder_sent: '2022-06-06',
                interval: 6,
              },
            ],
          },
        );
      });
      this.put(`${REACT_APP_API_BASEURL}/reminder`, (schema, request) => {
        return new Response(500, {}, {});
      });
      this.delete(
        `${REACT_APP_API_BASEURL}/reminder/:reminder_type`,
        (schema, request) => {
          return new Response(200, {}, {});
        },
      );
    },
  });

  return server;
}
