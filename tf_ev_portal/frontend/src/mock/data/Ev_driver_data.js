const Ev_Driver_Data = {
  driver_welcome: {
    ev_driver_idp_id: '5258c6de-e907-470b-b36a-bc96b43e7e1e',
    firstname: 'a',
    lastname: 'b',
    trainings: [
      {
        id: 100,
        training_name: 'trainingUpdateTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingUpdateTariffDescription',
          },
        ],
        completed: '2023-06-12T09:13:01.6960000',
      },
      {
        id: 200,
        training_name: 'trainingViewDriverAccount',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingViewDriverAccountDescription',
          },
        ],
        completed: null,
      },
      {
        id: 300,
        training_name: 'trainingAssignCard',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingAssignCardDescription',
          },
        ],
        completed: null,
      },
      {
        id: 400,
        training_name: 'trainingApproveDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingApproveDriverTariffDescription',
          },
        ],
        completed: null,
      },
      {
        id: 500,
        training_name: 'trainingRejectDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRejectDriverTariffDescription',
          },
        ],
        completed: null,
      },
    ],
    vehicles: [
      {
        id: 86,
        Vehicle_Make: 'BMW',
        Vehicle_Model: 'i3',
        Vehicle_Model_Version: '120 Ah',
        Drivetrain_Type: 'BEV',
        Battery_Capacity_Full: 42.2,
        licence_plate: '123',
      },
    ],
    wallboxes: [
      {
        evse_id: null,
        location_of_wallbox: null,
        owner_of_wallbox: true,
        address: {
          street: 'Hauptstraße',
          number: '1',
          city: 'Wien',
          postcode: '12345',
          state: null,
          country: 'AT',
          additional_information: 'x',
        },
      },
      {
        evse_id: 'AT*BPE*EACE1234567',
        location_of_wallbox: 'AT',
        owner_of_wallbox: true,
        address: {
          street: 'Hauptstraße',
          number: '1',
          city: 'Wien',
          postcode: '12345',
          state: null,
          country: 'AT',
          additional_information: 'x',
        },
      },
      {
        evse_id: 'AT*BPE*EACE1234567',
        location_of_wallbox: 'AT',
        owner_of_wallbox: true,
        address: {
          street: 'Hauptstraße',
          number: '1',
          city: 'Wien',
          postcode: '12345',
          state: null,
          country: 'AT',
          additional_information: 'x',
        },
      },
    ],
    cards: [
      {
        card_number: '700674134902000410',
        card_expiry_date: '2026-11-30',
        card_status: true,
        card_expiring_soon: false,
        reimbursement_status_id: 0,
      },
    ],
    reimbursement_status_id: 0,
    last_modified: '2023-07-06T08:25:04.780',
    DPS_accepted: '2022-12-14',
    principle: 'foureye',
    language: 'en',
    driver_approval_status: [
      {
        driver_approval_status_name: 'initial_data_rejected',
      },
    ],
    bank_data: null,
    tariff_id: ********,
    available_tariff_schemes: [
      {
        tariff_type: 'ENERGY',
        tariff_sub_type: 'TWOTARIFF',
      },
    ],
    tariffs: [
      // {
      //   functional_tariff_id: ********,
      //   currency: 'EUR',
      //   tariff_elements: [
      //     {
      //       functional_tariff_id: ********,
      //       comment: 'abc',
      //       price_components: [
      //         {
      //           tariff_type: 'ENERGY',
      //           tariff_sub_type: 'ONETARIFF',
      //           price: 0.3,
      //           step_size: 1,
      //         },
      //       ],
      //       restrictions: [
      //         {
      //           start_time: null,
      //           end_time: null,
      //           min_kwh: null,
      //           max_kwh: null,
      //           min_power: null,
      //           max_power: null,
      //           min_duration: null,
      //           max_duration: null,
      //           day_of_week: [
      //             'MONDAY',
      //             'TUESDAY',
      //             'WEDNESDAY',
      //             'THURSDAY',
      //             'FRIDAY',
      //             'SATURDAY',
      //             'SUNDAY',
      //           ],
      //           start_date: '2023-07-01',
      //           end_date: null,
      //           scan_uri: null,
      //           disclaimer_accepted: true,
      //           contract_approval_date: null,
      //         },
      //       ],
      //     },
      //   ],
      // },
      // {
      //   functional_tariff_id: 95021191,
      //   currency: 'EUR',
      //   tariff_elements: [
      //     {
      //       functional_tariff_id: 95021191,
      //       comment: 'abc',
      //       price_components: [
      //         {
      //           tariff_type: 'ENERGY',
      //           tariff_sub_type: 'ONETARIFF',
      //           price: 0.2,
      //           step_size: 1,
      //         },
      //       ],
      //       restrictions: [
      //         {
      //           start_time: null,
      //           end_time: null,
      //           min_kwh: null,
      //           max_kwh: null,
      //           min_power: null,
      //           max_power: null,
      //           min_duration: null,
      //           max_duration: null,
      //           day_of_week: [
      //             'MONDAY',
      //             'TUESDAY',
      //             'WEDNESDAY',
      //             'THURSDAY',
      //             'FRIDAY',
      //             'SATURDAY',
      //             'SUNDAY',
      //           ],
      //           start_date: '2023-07-01',
      //           end_date: null,
      //           scan_uri: null,
      //           disclaimer_accepted: true,
      //           contract_approval_date: null,
      //         },
      //       ],
      //     },
      //     {
      //       functional_tariff_id: 95021191,
      //       comment: 'abc',
      //       price_components: [
      //         {
      //           tariff_type: 'ENERGY',
      //           tariff_sub_type: 'ONETARIFF',
      //           price: 0.2,
      //           step_size: 1,
      //         },
      //       ],
      //       restrictions: [
      //         {
      //           start_time: null,
      //           end_time: null,
      //           min_kwh: null,
      //           max_kwh: null,
      //           min_power: null,
      //           max_power: null,
      //           min_duration: null,
      //           max_duration: null,
      //           day_of_week: [
      //             'MONDAY',
      //             'TUESDAY',
      //             'WEDNESDAY',
      //             'THURSDAY',
      //             'FRIDAY',
      //             'SATURDAY',
      //             'SUNDAY',
      //           ],
      //           start_date: '2023-07-01',
      //           end_date: null,
      //           scan_uri: null,
      //           disclaimer_accepted: true,
      //           contract_approval_date: null,
      //         },
      //       ],
      //     },
      //   ],
      // },
    ],
    last_updated: '2022-12-14T11:15:39.5000000',
  },
  driver_welcome_twotariff: {
    DPS_accepted: '2021-01-26',
    last_modified: '2018-07-23T07:26:23',
    language: 'en',
    ev_driver_idp_id: 'test-idp-id-915522',
    card_number: '************004275',
    available_tariff_schemes: [
      { tariff_type: 'ENERGY', tariff_sub_type: 'TWOTARIFF' },
    ],
    driver_approval_status: [
      {
        driver_approval_status_name: 'invited',
      },
    ],
  },
  driver_welcome_at: {
    DPS_accepted: '2021-01-26',
    last_modified: '2018-07-23T07:26:23',
    ev_driver_idp_id: 'test-idp-id-915522',
    card_number: '************004275',
    available_tariff_schemes: [
      { tariff_type: 'ENERGY', tariff_sub_type: 'TARIFFAT' },
    ],
    driver_approval_status: [
      {
        driver_approval_status_name: 'invited',
      },
    ],
  },
  driver_app: {
    DPS_accepted: null,
    ev_driver_idp_id: 'test-idp-id-915522',
    last_modified: '2018-07-23T07:26:23',
    card_number: '************004275',
    driver_approval_status: [
      {
        driver_approval_status_name: 'app_user_only',
      },
      {
        driver_approval_status_name: 'invited',
      },
    ],
  },
  driver_summary: {
    DPS_accepted: '2021-01-26',
    ev_driver_idp_id: 'test-idp-id-915222',
    last_modified: '2018-07-23T07:26:23',
    card_number: '************004275',
    driver_approval_status: [
      {
        driver_approval_status_name: 'invited',
      },
    ],
    wallbox: {
      country_code: 'DE',
      evse_id: 'AT*EVN*E4109*4',
    },
    current_electricity_tariff: {
      workprice: 0.31,
      valid_from: '2020-01-20',
    },
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
  },
  driver_summary_nl: {
    DPS_accepted: '2021-01-26',
    ev_driver_idp_id: 'test-idp-id-915522',
    last_modified: '2018-07-23T07:26:23',
    card_number: '************004275',
    driver_approval_status: [
      {
        driver_approval_status_name: 'invited',
      },
    ],
    wallbox: {
      country_code: 'NL',
      evse_id: 'AT*EVN*E4109*4',
    },
    current_electricity_tariff: {
      workprice: 0.31,
      valid_from: '2020-01-20',
    },
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
  },
  driver_waiting: {
    DPS_accepted: '2021-01-26',
    ev_driver_idp_id: 'test-idp-id-915522',
    card_number: '************004275',
    available_tariff_schemes: [
      { tariff_type: 'ENERGY', tariff_sub_type: 'TWOTARIFF' },
    ],
    last_modified: '2018-07-23T07:26:23',
    driver_approval_status: [
      {
        driver_approval_status_name: 'initial_data_entered',
      },
    ],
    wallbox: {
      country_code: 'AT',
      evse_id: 'AT*EVN*E4109*4',
    },
    current_electricity_tariff: {
      workprice: 0.31,
      valid_from: '2020-01-20',
    },
    additional_electricity_tariff: {
      workprice: 0.29,
      valid_from: '2020-03-01',
    },
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
  },
  driver_modify: {
    ev_driver_idp_id: '54bb08e9-0627-4fdd-91c3-7013053352d7',
    firstname: 'Hier Vorname',
    lastname: 'Hier Nachname',
    trainings: [
      {
        id: 800,
        name: 'trainingUpdateTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingUpdateTariffDescription',
          },
        ],
        completed: '2024-04-11T09:13:06.2580000',
      },
      {
        id: 900,
        name: 'trainingUpdateWallbox',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingUpdateWallboxDescription',
          },
        ],
        completed: null,
      },
      {
        id: 1000,
        name: 'trainingDriverPayoutReport',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingDriverPayoutReportDescription',
          },
        ],
        completed: null,
      },
      {
        id: 1200,
        name: 'trainingDriverRequestCard',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingDriverRequestCardDescription',
          },
        ],
        completed: null,
      },
    ],
    vehicles: [
      {
        id: 359,
        Vehicle_Make: 'Volkswagen',
        Vehicle_Model: 'ID. Buzz',
        Vehicle_Model_Version: 'Pro',
        Drivetrain_Type: 'BEV',
        Battery_Capacity_Full: 82,
        Availability_Date_From: '09-2020',
        Availability_Date_To: '10-2025',
        licence_plate: 'JO-321',
      },
    ],
    wallboxes: [
      {
        evse_id: 'NLLMSE10001330931',
        location_of_wallbox: 'de',
        owner_of_wallbox: true,
        address: {
          street: 'fadfdafad',
          number: 'https',
          city: 'c',
          postcode: 'p',
          state: null,
          country: 'NL',
          additional_information: 'ja',
        },
      },
    ],
    cards: [
      {
        card_number: '700674134902000378',
        card_expiry_date: '2026-09-30',
        card_status: true,
        card_expiring_soon: false,
        reimbursement_status_id: 2,
      },
    ],
    reimbursement_status_id: 2,
    last_modified: '2023-09-15T12:24:58.750',
    DPS_accepted: '2022-09-08',
    principle: 'twoeye',
    language: 'de',
    driver_approval_status: [
      {
        driver_approval_status_name: 'approved',
      },
    ],
    bank_data: {
      valid_from: '2023-09-08',
      last_four_iban_digits: '0901',
    },
    available_tariff_schemes: [
      {
        tariff_type: 'ENERGY',
        tariff_sub_type: 'ONETARIFF',
      },
    ],
    tariffs: [
      {
        functional_tariff_id: 2514443,
        currency: 'EUR',
        comment: '',
        disclaimer_accepted: true,
        contract_approval_date: '2024-12-20',
        start_date_time: '2024-12-12T23:00:00',
        end_date_time: '9999-12-31T00:00:00',
        tariff_elements: [
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 1,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-12-13',
                end_date: '9999-12-31',
                start_time: '18:00:00',
                end_time: '00:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                ],
              },
            ],
          },
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 0.7,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-12-13',
                end_date: '9999-12-31',
                start_time: '00:00:00',
                end_time: '18:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                ],
              },
            ],
          },
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 0.7,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-12-13',
                end_date: '9999-12-31',
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: ['SATURDAY', 'SUNDAY'],
              },
            ],
          },
        ],
        energy_mix: {
          is_green_energy: false,
          environ_impact: [
            {
              category: 'CARBON_DIOXIDE',
              amount: 23,
            },
          ],
        },
      },
      {
        functional_tariff_id: 2513333,
        currency: 'EUR',
        comment: '',
        disclaimer_accepted: true,
        contract_approval_date: '2024-11-19',
        start_date_time: '2024-11-20T23:00:00',
        end_date_time: '2024-12-12T23:00:00',
        tariff_elements: [
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 1,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-11-21',
                end_date: '2024-12-12',
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
              },
            ],
          },
        ],
        energy_mix: {
          is_green_energy: true,
          environ_impact: [
            {
              category: 'CARBON_DIOXIDE',
              amount: 0,
            },
          ],
        },
      },
      {
        functional_tariff_id: 2513197,
        currency: 'EUR',
        comment: '',
        disclaimer_accepted: true,
        contract_approval_date: '2024-11-19',
        start_date_time: '2024-11-17T23:00:00',
        end_date_time: '2024-11-20T23:00:00',
        tariff_elements: [
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 1,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-11-18',
                end_date: '2024-11-20',
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
              },
            ],
          },
        ],
        energy_mix: null,
      },
      {
        functional_tariff_id: 14163,
        currency: 'EUR',
        comment: '',
        disclaimer_accepted: true,
        contract_approval_date: '2024-10-30',
        start_date_time: '2024-09-23T22:00:00',
        end_date_time: '2024-11-17T23:00:00',
        tariff_elements: [
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 1,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2024-09-24',
                end_date: '2024-11-17',
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
              },
            ],
          },
        ],
        energy_mix: {
          is_green_energy: false,
          environ_impact: [
            {
              category: 'CARBON_DIOXIDE',
              amount: 564,
            },
          ],
        },
      },
      {
        functional_tariff_id: 1278,
        currency: 'EUR',
        comment: '',
        disclaimer_accepted: true,
        contract_approval_date: '2024-10-16',
        start_date_time: '2023-09-25T22:00:00',
        end_date_time: '2024-09-23T22:00:00',
        tariff_elements: [
          {
            price_components: [
              {
                tariff_type: 'ENERGY',
                price: 1.1,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_date: '2023-09-26',
                end_date: '2024-09-23',
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
              },
            ],
          },
        ],
        energy_mix: null,
      },
    ],
    last_updated: '2023-09-15T12:24:55.7766667',
  },
  driver_modify_cancelled: {
    DPS_accepted: '2021-01-26',
    last_modified: '2018-07-23T07:26:23',
    ev_driver_idp_id: 'test-idp-id-915522',
    card_number: '************004275',
    reimbursement_status_id: 3,
    driver_approval_status: [
      {
        driver_approval_status_name: 'approved',
      },
    ],
    wallbox: {
      country_code: 'DE',
      evse_id: 'DE*EVN*E4109*4',
    },
    tariffs: [
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 29,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.15,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2020-01-01',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 31,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.49,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-02',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 32,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.3,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-03',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 33,
            comment: 'Test für zwei auf einmal',
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'TWOTARIFF',
                price: 0.2,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: '18:00:00',
                end_time: '01:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-06',
                end_date: '9999-12-31',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
          {
            functional_tariff_id: 33,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'TWOTARIFF',
                price: 0.15,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: '01:00:00',
                end_time: '18:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-06',
                end_date: '9999-12-31',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
    ],
    last_updated: '2021-04-01T12:04:25.9700000',
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
  },
  driver_modify_de: {
    DPS_accepted: '2021-01-26',
    ev_driver_idp_id: 'test-idp-id-915522',
    last_modified: '2018-07-23T07:26:23',
    card_number: '************004275',
    driver_approval_status: [
      {
        driver_approval_status_name: 'app_user_only',
      },
      {
        driver_approval_status_name: 'approved',
      },
    ],
    wallbox: {
      country_code: 'DE',
      evse_id: 'DE*EVN*E4109*4',
    },
    current_electricity_tariff: {
      workprice: 0.31,
      valid_from: '2020-01-20',
    },
    additional_electricity_tariff: {
      workprice: 0.21,
      valid_from: '2020-12-20',
    },
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
  },
  driver_modify_update_requested: {
    DPS_accepted: '2021-01-26',
    ev_driver_idp_id: 'test-idp-id-915522',
    card_number: '************004275',
    last_modified: '2018-07-23T07:26:23',
    driver_approval_status: [
      {
        driver_approval_status_name: 'tariff_update_requested',
      },
    ],
    wallbox: {
      country_code: 'DE',
      evse_id: 'DE*EVN*E4109*4',
    },
    current_electricity_tariff: {
      workprice: 0.31,
      valid_from: '2020-01-20',
    },
    additional_electricity_tariff: {
      workprice: 0.21,
      valid_from: '2020-03-20',
    },
    bank_data: {
      valid_from: '2019-07-30',
      last_four_iban_digits: 8015,
    },
    tariff_id: 123,
    available_tariff_schemes: [
      {
        tariff_type: 'ENERGY',
        tariff_sub_type: 'TWOTARIFF',
      },
    ],
    tariffs: [
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 29,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.15,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2020-01-01',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 31,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.49,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-02',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 32,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'ONETARIFF',
                price: 0.3,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: null,
                end_time: null,
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-03',
                end_date: '2021-04-05',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
      {
        currency: 'EUR',
        tariff_elements: [
          {
            functional_tariff_id: 33,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'TWOTARIFF',
                price: 0.2,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: '18:00:00',
                end_time: '01:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-06',
                end_date: '9999-12-31',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
          {
            functional_tariff_id: 33,
            price_components: [
              {
                tariff_type: 'ENERGY',
                tariff_sub_type: 'TWOTARIFF',
                price: 0.15,
                step_size: 1,
              },
            ],
            restrictions: [
              {
                start_time: '01:00:00',
                end_time: '18:00:00',
                min_kwh: null,
                max_kwh: null,
                min_power: null,
                max_power: null,
                min_duration: null,
                max_duration: null,
                day_of_week: [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ],
                start_date: '2021-04-06',
                end_date: '9999-12-31',
                scan_uri: 'cus-************/contracts/ec_911032_20210401.pdf',
                disclaimer_accepted: true,
                contract_approval_date: '2021-04-01',
              },
            ],
          },
        ],
      },
    ],
    last_updated: '2021-04-01T12:04:25.9700000',
  },
};

export default Ev_Driver_Data;
