const SalesAdmins = {
  data: [
    {
      user_idp_id: 'e1142a7f-fb69-48e6-b946-78788e42e562',
      email: 'sebas<PERSON>.<PERSON><PERSON>.<EMAIL>',
      first_name: null,
      last_name: null,
      provider: 'Aral',
      created: '2024-03-14T15:30:01.6200000',
      updated: null,
      role: [
        {
          name: 'Sales',
          tokens: null,
          userdatastatus: null,
          userstatus: [
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-03-14T15:30:01.6200000',
              valid_to: '2024-03-14T15:30:52.5366667',
              actor: {
                email: '<EMAIL>',
                first_name: null,
                last_name: null,
                user_idp_id: '3fcb2f1a-8eb6-4e74-a148-075cd026b357',
              },
            },
            {
              name: 'active',
              description: 'active',
              valid_from: '2024-03-14T15:30:52.5400000',
              valid_to: '9999-12-31T00:00:00',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
          ],
          trainings: [
            {
              id: 100,
              name: 'trainingInviteDriver',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingInviteDriverDescription',
                },
              ],
              completed: null,
            },
            {
              id: 200,
              name: 'trainingViewDriverAccount',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingViewDriverAccountDescription',
                },
              ],
              completed: null,
            },
            {
              id: 300,
              name: 'trainingAssignCard',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingAssignCardDescription',
                },
              ],
              completed: null,
            },
            {
              id: 400,
              name: 'trainingApproveDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingApproveDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 500,
              name: 'trainingRejectDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRejectDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 600,
              name: 'trainingPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingPayoutReportDescription',
                },
              ],
              completed: null,
            },
            {
              id: 700,
              name: 'trainingRequestUpdate',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRequestUpdateDescription',
                },
              ],
              completed: null,
            },
            {
              id: 800,
              name: 'trainingUpdateTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 900,
              name: 'trainingUpdateWallbox',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateWallboxDescription',
                },
              ],
              completed: null,
            },
            {
              id: 1000,
              name: 'trainingDriverPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingDriverPayoutReportDescription',
                },
              ],
              completed: null,
            },
          ],
          vehicle: null,
          team: [
            {
              team_id: 1,
              companies: null,
            },
          ],
        },
      ],
    },
    {
      user_idp_id: '069eda53-2d4a-4d52-a82f-55f44565399d',
      email: '<EMAIL>',
      first_name: null,
      last_name: null,
      provider: 'bp',
      created: '2024-03-14T12:44:56.6733333',
      updated: null,
      role: [
        {
          name: 'Sales',
          tokens: null,
          userdatastatus: null,
          userstatus: [
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-03-14T12:44:56.6733333',
              valid_to: '2024-03-14T12:44:56.6733333',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
            {
              name: 'inactive',
              description: 'inactive',
              valid_from: '2024-03-14T12:44:56.6733333',
              valid_to: '9999-12-31T00:00:00',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
          ],
          trainings: [
            {
              id: 100,
              name: 'trainingInviteDriver',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingInviteDriverDescription',
                },
              ],
              completed: null,
            },
            {
              id: 200,
              name: 'trainingViewDriverAccount',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingViewDriverAccountDescription',
                },
              ],
              completed: null,
            },
            {
              id: 300,
              name: 'trainingAssignCard',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingAssignCardDescription',
                },
              ],
              completed: null,
            },
            {
              id: 400,
              name: 'trainingApproveDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingApproveDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 500,
              name: 'trainingRejectDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRejectDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 600,
              name: 'trainingPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingPayoutReportDescription',
                },
              ],
              completed: null,
            },
            {
              id: 700,
              name: 'trainingRequestUpdate',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRequestUpdateDescription',
                },
              ],
              completed: null,
            },
            {
              id: 800,
              name: 'trainingUpdateTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 900,
              name: 'trainingUpdateWallbox',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateWallboxDescription',
                },
              ],
              completed: null,
            },
            {
              id: 1000,
              name: 'trainingDriverPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingDriverPayoutReportDescription',
                },
              ],
              completed: null,
            },
          ],
          vehicle: null,
          team: [
            {
              team_id: 1,
              companies: null,
            },
          ],
        },
      ],
    },
    {
      user_idp_id: '069eda53-2d4a-4d52-a82f-55f44565399d',
      email: 'Sales_Admin',
      first_name: null,
      last_name: null,
      provider: 'bp',
      created: '2024-03-14T12:44:56.6733333',
      updated: null,
      role: [
        {
          name: 'Sales',
          tokens: null,
          userdatastatus: null,
          userstatus: [
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-03-14T12:44:56.6733333',
              valid_to: '2024-03-14T12:44:56.6733333',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
            {
              name: 'active',
              description: 'active',
              valid_from: '2024-03-14T12:44:56.6733333',
              valid_to: '9999-12-31T00:00:00',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
          ],
          trainings: [
            {
              id: 100,
              name: 'trainingInviteDriver',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingInviteDriverDescription',
                },
              ],
              completed: null,
            },
            {
              id: 200,
              name: 'trainingViewDriverAccount',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingViewDriverAccountDescription',
                },
              ],
              completed: null,
            },
            {
              id: 300,
              name: 'trainingAssignCard',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingAssignCardDescription',
                },
              ],
              completed: null,
            },
            {
              id: 400,
              name: 'trainingApproveDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingApproveDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 500,
              name: 'trainingRejectDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRejectDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 600,
              name: 'trainingPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingPayoutReportDescription',
                },
              ],
              completed: null,
            },
            {
              id: 700,
              name: 'trainingRequestUpdate',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRequestUpdateDescription',
                },
              ],
              completed: null,
            },
            {
              id: 800,
              name: 'trainingUpdateTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 900,
              name: 'trainingUpdateWallbox',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateWallboxDescription',
                },
              ],
              completed: null,
            },
            {
              id: 1000,
              name: 'trainingDriverPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingDriverPayoutReportDescription',
                },
              ],
              completed: null,
            },
          ],
          vehicle: null,
          team: [
            {
              team_id: 1,
              companies: null,
            },
          ],
        },
      ],
    },
    {
      user_idp_id: '3fcb2f1a-8eb6-4e74-a148-075cd026b357',
      email: '<EMAIL>',
      first_name: null,
      last_name: null,
      provider: 'bp',
      created: '2024-03-13T13:53:24.3900000',
      updated: null,
      role: [
        {
          name: 'Sales',
          tokens: null,
          userdatastatus: null,
          userstatus: [
            {
              name: 'active',
              description: 'active',
              valid_from: '2024-03-13T13:57:46.9233333',
              valid_to: '9999-12-31T00:00:00',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2010-01-01T00:00:00',
              valid_to: '2024-03-13T13:57:46.9233333',
              actor: {
                email: null,
                first_name: null,
                last_name: null,
                user_idp_id: null,
              },
            },
          ],
          trainings: [
            {
              id: 100,
              name: 'trainingInviteDriver',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingInviteDriverDescription',
                },
              ],
              completed: null,
            },
            {
              id: 200,
              name: 'trainingViewDriverAccount',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingViewDriverAccountDescription',
                },
              ],
              completed: null,
            },
            {
              id: 300,
              name: 'trainingAssignCard',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingAssignCardDescription',
                },
              ],
              completed: null,
            },
            {
              id: 400,
              name: 'trainingApproveDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingApproveDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 500,
              name: 'trainingRejectDriverTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRejectDriverTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 600,
              name: 'trainingPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingPayoutReportDescription',
                },
              ],
              completed: null,
            },
            {
              id: 700,
              name: 'trainingRequestUpdate',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingRequestUpdateDescription',
                },
              ],
              completed: null,
            },
            {
              id: 800,
              name: 'trainingUpdateTariff',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateTariffDescription',
                },
              ],
              completed: null,
            },
            {
              id: 900,
              name: 'trainingUpdateWallbox',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingUpdateWallboxDescription',
                },
              ],
              completed: null,
            },
            {
              id: 1000,
              name: 'trainingDriverPayoutReport',
              content: [
                {
                  type: 'gif',
                  key: 'description',
                  value: 'trainingDriverPayoutReportDescription',
                },
              ],
              completed: null,
            },
          ],
          vehicle: null,
          team: [
            {
              team_id: 1,
              companies: null,
            },
          ],
        },
      ],
    },
  ],
};

export default SalesAdmins;
