import chargingSessions from './ChargingSessions';
import Ev_Driver_Data from './Ev_driver_data';
import FleetManagers from './Fleetmanagers';
import reimbursements from './Reimbursments';
import salesLeads from './SalesLeads';
import users from './Users';
import vehicleList from './VehicleList';
import SalesAdmins from './SalesAdmins';

const GetDriverData = (cardNumber) => {
  return {
    ev_driver_idp_id: '33b569d0-5718-4de5-91b8-b118e37f8b3a',
    card_number: '700678069797292464',
    driver_approval_status: [
      { driver_approval_status_name: 'app_user_only' },
      { driver_approval_status_name: 'initial_data_entered' },
    ],
    wallbox: { country_code: 'DE', evse_id: 'DE*BPE*EACE1234567*01' },
    pending_tariff: {
      currency: 'EUR',
      functional_tariff_id: 2511975,
      startdatetime: '2024-10-15T22:00:00',
      enddatetime: null,
      tariff_type: 'REGULAR',
      comment: null,
      disclaimer_accepted: true,
      contract_approval_date: null,
      energy_mix: null,
      tariff_elements: [
        {
          price_components: [{ type: 'ENERGY', price: 1.0, step_size: 1 }],
          restrictions: [
            {
              start_time: null,
              end_time: null,
              min_kwh: null,
              max_kwh: null,
              min_power: null,
              max_power: null,
              min_duration: null,
              max_duration: null,
              day_of_week: [
                'MONDAY',
                'TUESDAY',
                'WEDNESDAY',
                'THURSDAY',
                'FRIDAY',
                'SATURDAY',
                'SUNDAY',
              ],
              start_date: '2024-10-16',
              end_date: null,
            },
          ],
        },
      ],
      contract_files: [
        'uploads/1ecba4d9d60073ec69315f89456c174d/Screenshot%202024-10-11%20at%2008.50.47.png',
      ],
      last_updated: '2024-10-16T06:13:48.4166667',
    },
    bank_data: { valid_from: '2024-10-16', last_four_iban_digits: '1211' },
  };
};

const StatusDirectChannelData = {
  fleet_manager_id: 212348,
  total_number: 10,
  invitations_send: 1,
  count_ready_for_approval: 2,
  count_update_data_requested: 3,
  count_reimbursement_active: 10,
};

const chargingSessionsSustainabilityDownload = {
  meta: {
    total_expected_reimbursement_cost: '0.0000',
    total_paid_reimbursement_cost: '0.0000',
    total_sessions_received: 5,
    total_energy: '385.6220',
  },
  data: [
    {
      email: '<EMAIL>',
      evse_id: 'DE*ICE*E0000TEST*6',
      country: 'DE',
      token_visual_number: '700678069797292035',
      expiry_date: '2029-02-28T00:00:00Z',
      session_start: '2024-10-21T07:23:43.205379Z',
      session_end: '2024-10-21T07:23:53.205382Z',
      total_time: 10,
      cdr_total_energy: '10.0000',
      cdr_total_cost: '0.0000',
      cdr_status_id: 6,
      cdr_type: 'public',
      energy_mix: null,
    },
    {
      email: '<EMAIL>',
      evse_id: 'DE*BPE*ESM*0000BD*01',
      country: 'DE',
      token_visual_number: '700678069797304954',
      expiry_date: '2029-10-31T00:00:00Z',
      session_start: '2024-10-19T06:22:00Z',
      session_end: '2024-10-19T17:44:00Z',
      total_time: 37320,
      cdr_total_energy: '22.9000',
      cdr_total_cost: '22.9000',
      cdr_status_id: 12,
      cdr_type: 'home',
      energy_mix: {
        is_green_energy: false,
        energy_sources: [],
        environ_impact: [
          {
            category: 'CARBON_DIOXIDE',
            amount: 564,
          },
        ],
        supplier_name: null,
        energy_product_name: null,
      },
    },
    {
      email: '<EMAIL>',
      evse_id: 'DE*ICE*E0000TEST*6',
      country: 'DE',
      token_visual_number: '700678069797292464',
      expiry_date: '2029-02-28T00:00:00Z',
      session_start: '2024-10-17T09:49:50.534573Z',
      session_end: '2024-10-17T09:50:00.534577Z',
      total_time: 10,
      cdr_total_energy: '10.0000',
      cdr_total_cost: '0.0000',
      cdr_status_id: 6,
      cdr_type: 'public',
      energy_mix: {
        is_green_energy: true,
        energy_sources: [],
        environ_impact: [],
        supplier_name: null,
        energy_product_name: null,
      },
    },
    {
      email: '<EMAIL>',
      evse_id: 'DE*BPE*ESM*0000BD*01',
      country: 'DE',
      token_visual_number: '700673002590003205',
      expiry_date: '2025-04-30T00:00:00Z',
      session_start: '2024-10-16T10:00:00Z',
      session_end: '2024-10-16T16:00:00Z',
      total_time: 21600,
      cdr_total_energy: '20.2220',
      cdr_total_cost: '20.2200',
      cdr_status_id: 12,
      cdr_type: 'home',
      energy_mix: {
        is_green_energy: false,
        energy_sources: [],
        environ_impact: [
          {
            category: 'CARBON_DIOXIDE',
            amount: 564,
          },
        ],
        supplier_name: null,
        energy_product_name: null,
      },
    },
    {
      email: '<EMAIL>',
      evse_id: 'DE*BPE*ESM*0000BD*01',
      country: 'DE',
      token_visual_number: '700678069797304954',
      expiry_date: '2029-10-31T00:00:00Z',
      session_start: '2024-10-09T06:22:00Z',
      session_end: '2024-10-09T17:44:00Z',
      total_time: 37320,
      cdr_total_energy: '19.0000',
      cdr_total_cost: '19.0000',
      cdr_status_id: 12,
      cdr_type: 'home',
      energy_mix: {
        is_green_energy: false,
        energy_sources: [],
        environ_impact: [
          {
            category: 'CARBON_DIOXIDE',
            amount: 564,
          },
        ],
        supplier_name: null,
        energy_product_name: null,
      },
    },
  ],
  status_code: 1000,
  status_message: 'Success',
  timestamp: '2025-01-08T08:44:26.605451573Z',
};

const chargingSessionsSustainabilityAggregated = {
  data: [
    {
      month: 11,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '280.0000',
      total_tariff_cnt: 3,
      green_energy: '118.0000',
      green_tariff_cnt: 1,
    },
    {
      month: 10,
      year: 2024,
      total_co2: '2820.0000',
      total_energy: '375.6200',
      total_tariff_cnt: 2,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 9,
      year: 2024,
      total_co2: '2256.0000',
      total_energy: '60.5000',
      total_tariff_cnt: 2,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 8,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '9.0000',
      total_tariff_cnt: 1,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 7,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '0.0000',
      total_tariff_cnt: 0,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 6,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '57.5000',
      total_tariff_cnt: 1,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 5,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '186.7000',
      total_tariff_cnt: 1,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 4,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '30.6800',
      total_tariff_cnt: 1,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 3,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '0.0000',
      total_tariff_cnt: 0,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 2,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '0.0000',
      total_tariff_cnt: 0,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 1,
      year: 2024,
      total_co2: '0.0000',
      total_energy: '0.0000',
      total_tariff_cnt: 0,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 12,
      year: 2023,
      total_co2: '0.0000',
      total_energy: '394.8100',
      total_tariff_cnt: 2,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
    {
      month: 11,
      year: 2023,
      total_co2: '0.0000',
      total_energy: '206.2500',
      total_tariff_cnt: 1,
      green_energy: '0.0000',
      green_tariff_cnt: 0,
    },
  ],
  status_code: 1000,
  status_message: 'Success',
  timestamp: '2024-11-20T08:59:23.706157482Z',
};

const approvalData = {
  data: [
    {
      id: 17,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: '620b858f-c5d1-4c58-a094-cadbb76f0abe',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 'test',
        last_name: 'zerdsz',
      },
      data: {
        token_visual_number: '700674082292000930',
        expiry_date: '2026-05-31',
      },
    },
    {
      id: 21,
      approvalstatus: 'rejected',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 22,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082292000930',
        expiry_date: '2026-05-31',
      },
    },
    {
      id: 23,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 24,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 25,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 26,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 27,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 28,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 29,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 30,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'd7ebb101-a701-4e2b-a20a-0b07ea0aca50',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 't43s',
        last_name: 'ölkj',
      },
      data: {
        token_visual_number: '700674082424000196',
        expiry_date: '2025-08-31',
      },
    },
    {
      id: 31,
      approvalstatus: 'pending',
      approvaltype: 'token',
      team_id: 150,
      user: {
        user_idp_id: 'ec8fb57a-9d9d-49d7-afec-4ee001e7ec7d',
        email: '<EMAIL>',
        provider: 'Trafineo',
        role: [
          {
            name: 'Driver',
          },
        ],
        first_name: 'Test',
        last_name: 'Tesyt',
      },
      data: {
        token_visual_number: '700674056542004256',
        expiry_date: '2027-05-31',
      },
    },
  ],
  status_code: 1000,
  status_message: 'Success',
  timestamp: '2024-09-25T06:28:26.412+00:00',
};

const myTeamFleetManagers = {
  fleetmanagers: [
    {
      e_mail: '<EMAIL>',
      function_description: 'operative',
      status_description: 'active',
    },
    {
      e_mail: '<EMAIL>',
      function_description: 'operative',
      status_description: 'invited',
    },
    {
      e_mail: '<EMAIL>',
      function_description: 'operative',
      status_description: 'inactive',
    },
    {
      e_mail: '<EMAIL>',
      function_description: 'admin',
      status_description: 'active',
    },
  ],
};

const newTeamData = {
  data: [
    {
      user_idp_id: '3f92cd2f-674d-4f46-99d1-3aebc916727a',
      email: '<EMAIL>',
      first_name: null,
      last_name: null,
      provider: 'Trafineo',
      created: '2024-09-19T07:42:36.8830000',
      updated: null,
      role: [
        {
          name: 'Support',
          userdatastatus: [],
          userstatus: [
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-09-19T07:42:36.8833333',
              valid_to: '9999-12-31T23:59:59.0000000',
            },
          ],
        },
      ],
    },
    {
      user_idp_id: 'a57b2065-7159-401c-95fd-9371716f6f6c',
      email: '<EMAIL>',
      first_name: 'Invited',
      last_name: 'Admin',
      provider: 'Trafineo',
      created: '2024-09-18T08:03:53.9330000',
      updated: null,
      role: [
        {
          name: 'Support',
          userdatastatus: [],
          userstatus: [
            {
              name: 'active',
              description: 'active',
              valid_from: '2024-09-18T08:03:53.9866667',
              valid_to: '9999-12-31T00:00:00.0000000',
            },
          ],
        },
      ],
    },
    {
      user_idp_id: '22ebb2e8-d968-4a72-afa0-90e28f0655b6',
      email: '<EMAIL>',
      first_name: 'inactive',
      last_name: 'admin',
      provider: 'Aral',
      created: '2024-06-12T14:07:14.6900000',
      updated: '2024-06-14T07:55:32.1930000',
      role: [
        {
          name: 'Sales',
          userdatastatus: [],
          userstatus: [
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-06-12T14:07:14.6900000',
              valid_to: '2024-09-20T10:07:16.6166667',
            },
            {
              name: 'inactive',
              description: 'inactive',
              valid_from: '2024-06-14T07:55:23.4666667',
              valid_to: '2024-06-14T07:55:32.1733333',
            },
            {
              name: 'invited',
              description: 'invited',
              valid_from: '2024-06-14T07:55:32.1733333',
              valid_to: '2024-09-20T10:07:16.6166667',
            },
            {
              name: 'inactive',
              description: 'inactive',
              valid_from: '2024-09-20T10:07:16.6166667',
              valid_to: '9999-12-31T23:59:59.9999999',
            },
          ],
        },
      ],
    },
  ],
  status_code: 1000,
  status_message: 'Success',
  timestamp: '2024-09-19T12:35:36.617+00:00',
};

const onboardingRequests = {
  registrations: [
    {
      id: 847,
      created_date: '2024-05-27T07:54:54.7666667',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'RE-3572 Test',
        lastname: 'Multiplication Omission',
        email: '<EMAIL>',
        company_name: 'Test-Tresor',
        phonenumber: '00599666666',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 1,
          company_ids: [
            {
              company_id: 700674164859,
              valid: true,
            },
          ],
        },
        {
          leasingcompany_id: 2,
          company_ids: [
            {
              company_id: 700674163193,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 60,
    },
    {
      id: 844,
      created_date: '2024-05-21T12:33:29.6433333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'testOnboardingALO',
        lastname: 'testOnboardingALO',
        email: '<EMAIL>',
        company_name: 'testComp',
        phonenumber: '1564654654654',
      },
      registration_status: 'rejected',
      comment: '',
      companies: [
        {
          leasingcompany_id: 10,
          company_ids: [
            {
              company_id: 700674555555,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 0,
    },
    {
      id: 843,
      created_date: '2024-05-17T14:26:15.3266667',
      country_code: 'de',
      customer_type: 'direct',
      principle: 'twoeye',
      contact: {
        firstname: 'first ',
        lastname: 'name',
        email: '<EMAIL>',
        company_name: 'eb',
        phonenumber: '123',
      },
      registration_status: 'pending',
      companies: [
        {
          leasingcompany_id: 9,
          company_ids: [
            {
              company_id: 700674123456,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1,
    },
    {
      id: 842,
      created_date: '2024-05-15T15:40:15.6200000',
      country_code: 'de',
      customer_type: 'direct',
      principle: 'twoeye',
      contact: {
        firstname: 'Fürst',
        lastname: 'NAME',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '123',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 9,
          company_ids: [
            {
              company_id: 700674056378,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 2,
    },
    {
      id: 838,
      created_date: '2024-04-30T13:27:10.5533333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Indogermano',
        lastname: 'Fleet',
        email: '<EMAIL>',
        company_name: 'Indogermanoflott',
        phonenumber: '00599666666',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 2,
          company_ids: [
            {
              company_id: 700674001485,
              valid: true,
            },
          ],
        },
        {
          leasingcompany_id: 12,
          company_ids: [
            {
              company_id: 700674012362,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 178,
    },
    {
      id: 833,
      created_date: '2024-04-22T13:20:56.2966667',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test',
        lastname: 'Onboarding',
        email: '<EMAIL>',
        company_name: 'Test Onboarding GmbH',
        phonenumber: '012345',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 22,
          company_ids: [
            {
              company_id: 700674128922,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1150,
    },
    {
      id: 832,
      created_date: '2024-04-22T09:40:46.7000000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test',
        lastname: 'bb',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '12356',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 4,
          company_ids: [
            {
              company_id: 700674925827,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 257,
    },
    {
      id: 831,
      created_date: '2024-04-22T09:21:53.1700000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test',
        lastname: 'Testtest',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '123',
      },
      registration_status: 'rejected',
      comment: '',
      companies: [
        {
          leasingcompany_id: 16,
          company_ids: [
            {
              company_id: 700674908782,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 417,
    },
    {
      id: 830,
      created_date: '2024-04-22T09:15:25.3100000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Seb',
        lastname: 'Test',
        email: '<EMAIL>',
        company_name: 'TestingCompany',
        phonenumber: '12345',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 16,
          company_ids: [
            {
              company_id: 700674908782,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 417,
    },
    {
      id: 828,
      created_date: '2024-04-22T08:44:46.0333333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Bernd',
        lastname: 'Dreiling',
        email: '<EMAIL>',
        company_name: 'Test',
        phonenumber: 'Test',
      },
      registration_status: 'rejected',
      comment: 'Sorry not so good',
      companies: [
        {
          leasingcompany_id: 24,
          company_ids: [
            {
              company_id: 700674920657,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 24,
    },
    {
      id: 826,
      created_date: '2024-04-18T09:50:46.3300000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Vor',
        lastname: 'Name',
        email: '<EMAIL>',
        company_name: 'Testfirma',
        phonenumber: '12345',
      },
      registration_status: 'rejected',
      comment: 'sori',
      companies: [
        {
          leasingcompany_id: 20,
          company_ids: [
            {
              company_id: 700674911417,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1015,
    },
    {
      id: 825,
      created_date: '2024-04-16T11:02:55.7800000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test',
        lastname: 'bb',
        email: '<EMAIL>',
        company_name: 'bb2',
        phonenumber: '12345',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 15,
          company_ids: [
            {
              company_id: 700674939793,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 2,
    },
    {
      id: 824,
      created_date: '2024-04-16T10:56:57.0433333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'twoeye',
      contact: {
        firstname: 'Test',
        lastname: 'Tester',
        email: '<EMAIL>',
        company_name: 'bb2',
        phonenumber: '12345',
      },
      registration_status: 'rejected',
      comment: 'Sori',
      companies: [
        {
          leasingcompany_id: 2,
          company_ids: [
            {
              company_id: 700674131789,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 9,
    },
    {
      id: 823,
      created_date: '2024-04-16T08:54:49.7800000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Langen',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '12345',
      },
      registration_status: 'rejected',
      comment: 'Sorry falsche Unternehmen',
      companies: [
        {
          leasingcompany_id: 5,
          company_ids: [
            {
              company_id: 700674911290,
              valid: false,
            },
            {
              company_id: 700674911417,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1363,
    },
    {
      id: 822,
      created_date: '2024-04-15T09:59:23.0533333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Seb',
        lastname: 'bb',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '12345',
      },
      registration_status: 'rejected',
      comment: 'Invalid',
      companies: [
        {
          leasingcompany_id: 5,
          company_ids: [
            {
              company_id: 700674911417,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1015,
    },
    {
      id: 821,
      created_date: '2024-04-12T09:50:46.0466667',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Langen',
        email: '<EMAIL>',
        company_name: 'bb',
        phonenumber: '12345',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 9,
          company_ids: [
            {
              company_id: 700674911290,
              valid: true,
            },
            {
              company_id: 700674911417,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1363,
    },
    {
      id: 820,
      created_date: '2024-04-12T09:26:57.0500000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Langen',
        email: '<EMAIL>',
        company_name: 'Test',
        phonenumber: '12345',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 16,
          company_ids: [
            {
              company_id: 700674911290,
              valid: true,
            },
            {
              company_id: 700674911417,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1363,
    },
    {
      id: 819,
      created_date: '2024-04-12T09:06:34.8433333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Zwote',
        lastname: 'Flöte',
        email: '<EMAIL>',
        company_name: 'Flöten und Trompeten',
        phonenumber: '005-99-666-666',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 2,
          company_ids: [
            {
              company_id: 700674131789,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 9,
    },
    {
      id: 818,
      created_date: '2024-04-11T14:55:16.4833333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Signor',
        lastname: 'Signin',
        email: '<EMAIL>',
        company_name: 'Indie Com',
        phonenumber: '005-99-666-666',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 5,
          company_ids: [
            {
              company_id: 700674124490,
              valid: true,
            },
          ],
        },
        {
          leasingcompany_id: 8,
          company_ids: [
            {
              company_id: 700674090203,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 8,
    },
    {
      id: 816,
      created_date: '2024-04-05T13:42:55.9133333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Vonhoff',
        email: '<EMAIL>',
        company_name: 'X',
        phonenumber: '022',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 12,
          company_ids: [
            {
              company_id: 700674000149,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 298,
    },
    {
      id: 815,
      created_date: '2024-03-22T13:55:40.1933333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Thomas',
        lastname: 'Zöller',
        email: '<EMAIL>',
        company_name: 'X-INTEGRATE',
        phonenumber: '01722397685',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 1,
          company_ids: [
            {
              company_id: 700674200001,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1,
    },
    {
      id: 814,
      created_date: '2024-03-18T15:23:33.6533333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Vonhofff',
        email: '<EMAIL>',
        company_name: 'X-INTEGRATE Software & Consulting GmbH',
        phonenumber: '12345678',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 15,
          company_ids: [
            {
              company_id: 700674116007,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 375,
    },
    {
      id: 813,
      created_date: '2024-03-18T15:20:08.5566667',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'twoeye',
      contact: {
        firstname: 'Sebastian',
        lastname: 'Vonhoff',
        email: '<EMAIL>',
        company_name: 'X-INTEGRATE Software & Consulting GmbH ',
        phonenumber: '+49178',
      },
      registration_status: 'rejected',
      comment: '',
      companies: [
        {
          leasingcompany_id: 6,
          company_ids: [
            {
              company_id: 700674128922,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1150,
    },
    {
      id: 811,
      created_date: '2024-03-14T10:34:51.8700000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test fristname',
        lastname: 'test lastname',
        email: '<EMAIL>',
        company_name: 'sdflögkj',
        phonenumber: '345254',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 10,
          company_ids: [
            {
              company_id: 700674086480,
              valid: true,
            },
          ],
        },
        {
          leasingcompany_id: 14,
          company_ids: [
            {
              company_id: 700674086357,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 327,
    },
    {
      id: 809,
      created_date: '2024-03-14T08:47:31.1966667',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test fristname',
        lastname: 'test lastname',
        email: '<EMAIL>',
        company_name: 'sdflögkj',
        phonenumber: '345254',
      },
      registration_status: 'rejected',
      comment: 'Stimmt alles nicht',
      companies: [
        {
          leasingcompany_id: 13,
          company_ids: [
            {
              company_id: 700674987654,
              valid: false,
            },
          ],
        },
        {
          leasingcompany_id: 18,
          company_ids: [
            {
              company_id: 700674654321,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 0,
    },
    {
      id: 808,
      created_date: '2024-03-13T16:05:07.6033333',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test fristname',
        lastname: 'test lastname',
        email: '<EMAIL>',
        company_name: 'sdflögkj',
        phonenumber: '345254',
      },
      registration_status: 'pending',
      companies: [
        {
          leasingcompany_id: 17,
          company_ids: [
            {
              company_id: 700674123456,
              valid: false,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: false,
        },
      ],
      cards_sum: 1,
    },
    {
      id: 807,
      created_date: '2024-03-13T15:21:14.1900000',
      country_code: 'de',
      customer_type: 'indirect',
      principle: 'foureye',
      contact: {
        firstname: 'Test fristname',
        lastname: 'test lastname',
        email: '<EMAIL>',
        company_name: 'sdflögkj',
        phonenumber: '345254',
      },
      registration_status: 'approved',
      companies: [
        {
          leasingcompany_id: 9,
          company_ids: [
            {
              company_id: 700674128922,
              valid: true,
            },
          ],
        },
      ],
      emailadresses: [
        {
          email: '<EMAIL>',
          valid: true,
        },
      ],
      cards_sum: 1150,
    },
  ],
};

const statusHistory = [
  {
    status_type: 'approval_status',
    status_id: -1,
    valid_from: '2022-01-26T00:00:00',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: null,
    actor_email_address: null,
  },
  {
    status_type: 'approval_status',
    status_id: 0,
    valid_from: '2022-01-26T00:00:00',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: null,
    actor_email_address: null,
  },
  {
    status_type: 'reimbursement_status',
    status_id: 3,
    valid_from: '2022-01-26T00:00:00',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: null,
    actor_email_address: null,
  },
  {
    status_type: 'reimbursement_status',
    status_id: 3,
    valid_from: '2022-01-26T10:49:10.210',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: '3f0d3d42-bd67-4894-b508-1eec900c941e',
    actor_email_address: '<EMAIL>',
    comment: 'Beispiel',
  },
  {
    status_type: 'approval_status',
    status_id: 4,
    valid_from: '2022-01-26T10:49:10.210',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: '3f0d3d42-bd67-4894-b508-1eec900c941e',
    actor_email_address: '<EMAIL>',
    comment: 'Beispiel',
  },
  {
    status_type: 'approval_status',
    status_id: 2,
    valid_from: '2022-01-26T10:48:45.610',
    valid_to: '2022-01-26T10:49:10.200',
    active: false,
    actor_idp_id: null,
    actor_email_address: null,
  },
  {
    status_type: 'approval_status',
    status_id: 1,
    valid_from: '2022-01-26T10:48:06.803',
    valid_to: '2022-01-26T10:48:45.610',
    active: false,
    actor_idp_id: null,
    actor_email_address: null,
  },
  {
    status_type: 'approval_status',
    status_id: 0,
    valid_from: '2022-01-26T10:44:36.147',
    valid_to: '2022-01-26T10:48:06.810',
    active: false,
    actor_idp_id: '3f0d3d42-bd67-4894-b508-1eec900c941e',
    actor_email_address: '<EMAIL>',
  },
  {
    status_type: 'reimbursement_status',
    status_id: 2,
    valid_from: '2022-01-26T00:00:00',
    valid_to: '9999-12-31T00:00:00',
    active: true,
    actor_idp_id: '3f0d3d42-bd67-4894-b508-1eec900c941e',
    actor_email_address: '<EMAIL>',
  },
];

const Payouts = {
  data: [
    {
      id: 101,
      total_cost: 17.11,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 1,
    },
    {
      id: 102,
      total_cost: 17.12,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 2,
    },
    {
      id: 103,
      total_cost: 17.13,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 3,
    },
    {
      id: 104,
      total_cost: 17.14,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 4,
    },
    {
      id: 105,
      total_cost: 17.15,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 5,
    },
    {
      id: 106,
      total_cost: 17.16,
      workprice: 0.123457,
      total_energy: 138.68,
      created: '2023-04-20T12:57:23.0400000',
      report_year: 2022,
      report_month: 6,
    },
    {
      id: 96,
      total_cost: 287.99,
      workprice: 0.12235,
      total_energy: 1292.4498,
      created: '2023-04-17T10:18:42.3620000',
      report_year: 2023,
      report_month: 3,
    },
  ],
  timestamp: '2023-05-03T11:45:01.803',
};

const PayoutDownload = {
  //download only available in NL for demo
  //language for download is defined by companyId of managed company --> same for the whole fleet
  pdfData:
    'data:application/pdf;base64,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',
};

export {
  users,
  Ev_Driver_Data,
  GetDriverData,
  salesLeads,
  StatusDirectChannelData,
  FleetManagers,
  chargingSessions,
  reimbursements,
  statusHistory,
  myTeamFleetManagers,
  onboardingRequests,
  vehicleList,
  Payouts,
  PayoutDownload,
  SalesAdmins,
  newTeamData,
  approvalData,
  chargingSessionsSustainabilityDownload,
  chargingSessionsSustainabilityAggregated,
};
