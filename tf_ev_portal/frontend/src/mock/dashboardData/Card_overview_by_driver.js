const card_overview_by_driver = {
  user: {
    idp_id: '4cf5a528-1fee-4017-99bb-04d331934d50',
    Provider: 'aral',
    e_mail: '<EMAIL>',
    status: 'active',
    function: 'admin',
    principle: 'foureye',
  },
  counts: {
    total: 21,
    pending_actions: 6,
  },
  information: [
    {
      tokens: [
        {
          token_visual_number: '100000000000000001',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d818c8cdae1',
      email_address_ev_driver:
        '<EMAIL>',
      firstname: 'Indirect',
      lastname: 'User',
      driver_approval_status: [
        {
          driver_approval_status_name: 'created',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      tokens: [
        {
          token_visual_number: '700674058814021596',
          expiry_date: '2025-09-30',
          card_status: true,
          expiring_soon: false,
          issuer: 'Trafineo',
          reimbursement_status_id: 2,
        },
        {
          token_visual_number: '700674058814021547',
          expiry_date: '2025-09-30',
          card_status: true,
          expiring_soon: false,
          issuer: 'Trafineo',
          reimbursement_status_id: 2,
        },
        {
          token_visual_number: '700674058814021232',
          expiry_date: '2025-07-31',
          card_status: true,
          expiring_soon: false,
          issuer: 'Trafineo',
          reimbursement_status_id: 3,
        },
        {
          token_visual_number: '700674058814031769',
          expiry_date: '2028-10-31',
          card_status: true,
          expiring_soon: false,
          issuer: 'Trafineo',
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2025-01-06',
        last_four_iban_digits: '9890',
      },
      wallbox: {
        evse_id: 'DE*ARP*EXHC*02',
        location_of_wallbox: 'DE',
        address: {
          street: 'Alpener',
          number: '17',
          city: 'Köln',
          postcode: '50825',
          country: 'DE',
          additional_information: '',
        },
      },
      vehicle: {
        id: 1075,
        make: 'Alfa Romeo',
        model: 'Tonale',
        licence_plate: 'abc',
      },
      ev_driver_idp_id: '2c93b041-79e3-4539-a9d7-9fc9004c916b',
      email_address_ev_driver:
        '<EMAIL>',
      firstname: 'New',
      lastname: 'Driver',
      driver_approval_status: [
        {
          driver_approval_status_name: 'tariff_update_requested',
          actor: {
            email: '<EMAIL>',
            first_name: null,
            last_name: null,
            user_idp_id: 'ba39d7fe-2870-4686-a2bd-9d60df43dbf7',
          },
        },
        {
          driver_approval_status_name: 'new_card_requested',
          actor: {
            email: null,
            first_name: null,
            last_name: null,
            user_idp_id: null,
          },
        },
        {
          driver_approval_status_name: 'banking_update_requested',
          actor: {
            email: '<EMAIL>',
            first_name: null,
            last_name: null,
            user_idp_id: 'ba39d7fe-2870-4686-a2bd-9d60df43dbf7',
          },
        },
      ],
      tariffs: [
        {
          functional_tariff_id: 2513848,
          start_date_time: '2024-11-24T23:00:00',
          end_date_time: '9999-12-31T00:00:00',
          comment: '',
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2024-12-18T12:57:39.9600000',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.4,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-11-25',
                  end_date: '9999-12-31',
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                },
              ],
            },
          ],
          energy_mix: {
            is_green_energy: false,
            environ_impact: [
              {
                category: 'CARBON_DIOXIDE',
                amount: 50,
              },
            ],
          },
        },
        {
          functional_tariff_id: 2513949,
          start_date_time: '2024-09-30T22:00:00',
          end_date_time: '2024-11-24T23:00:00',
          comment: 'earlier contract',
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2024-12-05T16:07:08.0133333',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.2,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-10-01',
                  end_date: '2024-11-24',
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                },
              ],
            },
          ],
          energy_mix: {
            is_green_energy: false,
            environ_impact: [
              {
                category: 'CARBON_DIOXIDE',
                amount: 500,
              },
            ],
          },
        },
      ],
      last_updated: '2025-01-06T16:07:11.4169620',
    },
    {
      tokens: [
        {
          token_visual_number: '100000000000000001',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d818c8cdae1',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'Indirect',
      lastname: 'User',
      driver_approval_status: [
        {
          driver_approval_status_name: 'created',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000000000001',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d718c8cdae1',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'App',
      lastname: 'User',
      driver_approval_status: [
        {
          driver_approval_status_name: 'app_user_only',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000000000001',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d718c8cdae2',
      email_address_ev_driver: '<EMAIL>',
      driver_approval_status: [
        {
          driver_approval_status_name: 'invited',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000000000001',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d718c8cdae3',
      email_address_ev_driver: '<EMAIL>',
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db94',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000001000010',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-11-07',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: '4bff8df8-3ba9-4d78-a52b-a6a3de81a715',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_rejected',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-21T15:20:19.7500000',
      ordering_number: 3,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db96',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              comment: 'Test',
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db96',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'TestForTarrif',
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'wallbox_update_requested',
        },
        {
          driver_approval_status_name: 'tariff_modified',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              comment: 'Test',
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db96',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'TestForTarrif',
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'tariff_update_requested',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              comment: 'Test',
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },

    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 1,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db97',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000858',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db98',
      invitation_sent_to: '<EMAIL>',
      email_address_ev_driver: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
          driver_approval_status_name: 'tariff_update_requested',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 19,
    },
    {
      tokens: [
        {
          token_visual_number: '700674931159050216',
          expiry_date: '2021-11-30',
          card_status: false,
          expiring_soon: true,
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*323',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'JK',
      },
      ev_driver_idp_id: '49eea47d-0ff1-4699-b85d-4014f22884ef',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
          driver_approval_status_name: 'tariff_update_requested',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501048,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-23',
                  end_date: '2022-11-04',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501073,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '2022-11-09',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501074,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-10',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: 'DE8888',
      last_updated: '2022-11-18T13:10:44.2200000',
      ordering_number: 4,
    },
    {
      tokens: [
        {
          token_visual_number: '700673037839026377',
          expiry_date: '2024-02-29',
          card_status: false,
          expiring_soon: false,
          reimbursement_status_id: 3,
        },
      ],
      banking_details: {
        valid_from: '2019-09-07',
        last_four_iban_digits: '4619',
      },
      wallbox: {
        evse_id: 'DE*TRA*EEVB**********05',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'MW-ZE-8832',
      },
      ev_driver_idp_id: '3b04d5ee-f172-4175-bc4c-d469645f1a57',
      invitation_sent_to: '<EMAIL>',
      firstname: 'John',
      lastname: 'Doe',
      driver_approval_status: [
        {
          driver_approval_status_name: 'app_user_only',
        },
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: null,
      department_id: '4678',
      last_updated: '2022-11-18T12:17:42.4150000',
      ordering_number: 5,
    },
    {
      tokens: [
        {
          token_visual_number: '700673037839026377',
          expiry_date: '2024-02-29',
          card_status: false,
          expiring_soon: false,
          reimbursement_status_id: 3,
        },
      ],
      banking_details: {
        valid_from: '2021-09-03',
        last_four_iban_digits: '9700',
      },
      wallbox: {
        evse_id: 'DE*BPE*ERIO*0000003*01',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: '13',
      },
      ev_driver_idp_id: '47fc2425-22cb-47e2-8eb4-092e9b0a7a27',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'banking_modified',
        },
        {
          driver_approval_status_name: 'banking_modified',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2021-09-03T09:34:25.2566667',
          tariff_elements: [
            {
              functional_tariff_id: 47,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.15,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-09-01',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: '4678',
      last_updated: '2022-11-18T12:17:42.4150000',
      ordering_number: 6,
    },
    {
      tokens: [
        {
          token_visual_number: '700678000183009444',
          expiry_date: '2024-02-29',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2018-09-07',
        last_four_iban_digits: '4227',
      },
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'EM-HZ-5784',
      },
      ev_driver_idp_id: 'a8c04c17-eba8-4788-941a-11d53736b779',
      invitation_sent_to: '<EMAIL>',
      firstname: 'Foo',
      lastname: 'Bar',
      driver_approval_status: [
        {
          driver_approval_status_name: 'wallbox_update_requested',
        },
        {
          driver_approval_status_name: 'banking_update_requested',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2021-08-26T08:08:44.6666667',
          tariff_elements: [
            {
              functional_tariff_id: 3,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.156,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2019-06-23',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: '4885',
      last_updated: '2022-11-18T12:17:42.4150000',
      ordering_number: 7,
    },
    {
      tokens: [
        {
          token_visual_number: '700673010851021022',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: '',
      },
      ev_driver_idp_id: '2f239310-f39e-4063-9c30-34b1902900d5',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-18T12:17:03.2530000',
      ordering_number: 8,
    },
    {
      tokens: [
        {
          token_visual_number: '700673010851021030',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: '',
      },
      ev_driver_idp_id: 'f6526955-46b2-4024-8ec1-88485330df57',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-18T12:17:03.2530000',
      ordering_number: 9,
    },
    {
      tokens: [
        {
          token_visual_number: '700673010851021048',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-08-31',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*325',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: '',
      },
      ev_driver_idp_id: 'c6de8470-6618-4e5e-bff2-76be8b48a519',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
        {
          driver_approval_status_name: 'wallbox_update_requested',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 2501022,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-06-03',
                  end_date: '2022-08-13',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 50,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-10-11',
                  end_date: '2022-06-02',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 2501039,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-08-14',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 51,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-10-01',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-11-18T12:17:03.2530000',
      ordering_number: 10,
    },
    {
      tokens: [
        {
          token_visual_number: '700673010851021055',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: '',
      },
      ev_driver_idp_id: 'ed49400b-c168-4094-b0b9-f80d00e90181',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-18T12:17:03.2530000',
      ordering_number: 11,
    },
    {
      tokens: [
        {
          token_visual_number: '700673010851021063',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-09-21',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 421,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'SD',
      },
      ev_driver_idp_id: 'b278c427-693c-4374-bc21-ebf7e3c0b8ae',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-18T12:17:03.2530000',
      ordering_number: 12,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000001000022',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: '********-b9eb-478f-be89-ed07bc0fb73c',
      invitation_sent_to: '<EMAIL>',
      firstname: 'klöj',
      lastname: 'ölkj',
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-10T12:43:13.3400000',
      ordering_number: 13,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000001000012',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: '1d1b2cf0-9a58-46b2-8e3a-f412f99c3d76',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'invited',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-07T13:10:01.5300000',
      ordering_number: 14,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000001000009',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 3,
        },
      ],
      banking_details: {
        valid_from: '2022-11-07',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*324',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 574,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DF',
      },
      ev_driver_idp_id: 'ea6b91db-4969-4099-869a-804751318a63',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-07T12:31:30.1533333',
          tariff_elements: [
            {
              functional_tariff_id: 2501066,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      last_updated: '2022-11-07T12:31:30.1633333',
      ordering_number: 15,
    },
    {
      tokens: [
        {
          token_visual_number: '100000000001000011',
          expiry_date: '2025-12-31',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: null,
      ev_driver_idp_id: '977d541d-63fe-4a02-972c-f691297ada67',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
        {
          driver_approval_status_name: 'tariff_modified',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 16,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000853',
          expiry_date: '2021-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'TD-GE-1184',
      },
      ev_driver_idp_id: '63fac5e4-5f51-44d2-9870-709970cad83c',
      invitation_sent_to: '<EMAIL>',
      firstname: 'Erika',
      lastname: 'Mustermann',
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
        {
          driver_approval_status_name: 'wallbox_modified',
        },
        {
          driver_approval_status_name: 'tariff_modified',
        },
        {
          driver_approval_status_name: 'banking_modified',
        },
        {
          driver_approval_status_name: 'wallbox_modified',
        },
        {
          driver_approval_status_name: 'tariff_modified',
        },
        {
          driver_approval_status_name: 'banking_modified',
        },
        {
          driver_approval_status_name: 'wallbox_modified',
        },
        {
          driver_approval_status_name: 'tariff_modified',
        },
        {
          driver_approval_status_name: 'banking_modified',
        },
      ],
      tariffs: null,
      department_id: '1678',
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 17,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000856',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: false,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'TEST',
      },
      ev_driver_idp_id: 'deb6e66e-9ccd-457e-a429-d7a01bfbde57',
      invitation_sent_to: '<EMAIL>',
      firstname: 'Test',
      lastname: 'Name',
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      reimbursement_status_id: null,
      tariffs: null,
      department_id: '123',
      last_updated: '2022-10-05T07:41:41.4733333',
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000857',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
        },
      ],
      banking_details: {
        valid_from: '2022-09-21',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DSF',
      },
      ev_driver_idp_id: '4ac9d72c-ed63-4ffe-8736-bfff176f7f18',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: ['created'],
      tariffs: null,
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 18,
    },

    {
      tokens: [
        {
          token_visual_number: '911673011371000856',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'TEST',
      },
      ev_driver_idp_id: 'deb6e66e-9ccd-457e-a429-d7a01bfbde57',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'test',
      lastname: 'name',
      driver_approval_status: [
        {
          driver_approval_status_name: 'logged_in_once',
        },
      ],
      tariffs: null,
      department_id: '123',
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 20,
    },
    {
      tokens: [
        {
          token_visual_number: '911673011371000859',
          expiry_date: '2022-01-01',
          card_status: true,
          expiring_soon: true,
          reimbursement_status_id: 0,
        },
      ],
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DF',
      },
      ev_driver_idp_id: 'df2447ce-5172-4dde-a319-a893a129bc1f',
      invitation_sent_to: '<EMAIL>',
      firstname: null,
      lastname: null,
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-10-05T07:41:41.4733333',
      ordering_number: 21,
    },
  ],
};

export default card_overview_by_driver;
