const trainingsFleetmanagerIndirect = {
  user: {
    idp_id: '3bf3247d-1988-42fd-aaf9-bb530e593ee8',
    provider: 'Trafineo',
    e_mail: '<EMAIL>',
    status: 'active',
    function: 'admin',
    principle: 'foureye',
    trainings: [
      {
        id: 200,
        name: 'trainingViewDriverAccount',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingViewDriverAccountDescription',
          },
        ],
        completed: null,
      },
      {
        id: 400,
        name: 'trainingApproveDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingApproveDriverTariffDescription',
          },
        ],
        completed: null,
      },
      {
        id: 500,
        name: 'trainingRejectDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRejectDriverTariffDescription',
          },
        ],
        completed: null,
      },
      {
        id: 600,
        name: 'trainingPayoutReport',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingPayoutReportDescription',
          },
        ],
        completed: null,
      },
      {
        id: 700,
        name: 'trainingRequestUpdate',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRequestUpdateDescription',
          },
        ],
        completed: null,
      },
      {
        id: 1100,
        name: 'trainingAddTeammember',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingAddTeammemberDescription',
          },
        ],
        completed: null,
      },
    ],
  },
  counts: {
    total: 14,
    active: 2,
    inactive: 12,
    assigned: 12,
    unassigned: 2,
    expiring_soon: 0,
  },
  information: [
    {
      token: {
        token_visual_number: '700674056542004264',
        expiry_date: '2027-05-31',
        card_status: true,
        expiring_soon: false,
        issuer: 'Trafineo',
        reimbursement_status_id: 2,
      },
      banking_details: {
        valid_from: '2024-09-24',
        last_four_iban_digits: '1211',
      },
      wallbox: {
        evse_id: 'DE*BPE*EACE1234567*01',
        location_of_wallbox: 'DE',
        address: {
          street: 'Test',
          number: '1',
          city: '123456',
          postcode: 'Test',
          country: 'DE',
          additional_information: '',
        },
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'lköj',
      },
      last_updated: '2024-11-14T08:06:55.0166667',
      ev_driver_idp_id: 'ec8fb57a-9d9d-49d7-afec-4ee001e7ec7d',
      driver_approval_status: [
        {
          driver_approval_status_name: 'tariff_modified',
          actor: {
            email: null,
            first_name: null,
            last_name: null,
            user_idp_id: null,
          },
        },
      ],
      tariffs: [
        {
          functional_tariff_id: 14178,
          start_date_time: '2024-09-17T22:00:00',
          end_date_time: '9999-12-31T00:00:00',
          comment: '',
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2024-09-24T12:01:51.9033333',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-09-18',
                  end_date: '9999-12-31',
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                },
              ],
            },
          ],
          energy_mix: null,
        },
        {
          functional_tariff_id: 14179,
          start_date_time: '2024-09-11T22:00:00',
          end_date_time: '2024-09-17T22:00:00',
          comment: '',
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2024-09-24T12:06:03.4000000',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-09-12',
                  end_date: '2024-09-17',
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                },
              ],
            },
          ],
          energy_mix: null,
        },
      ],
      email_address_ev_driver: '<EMAIL>',
      firstname: 'Test',
      lastname: 'Tesyt',
    },
  ],
};

export default trainingsFleetmanagerIndirect;
