const card_overview_by_driver_by_mail = {
  user: {
    idp_id: '5ecf7261-2cd1-4fea-bd29-7e153265d02b',
    provider: 'bp',
    e_mail: '<EMAIL>',
    status: 'active',
    function: 'admin',
    principle: 'foureye',
    trainings: [
      {
        id: 100,
        name: 'trainingInviteDriver',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingInviteDriverDescription',
          },
        ],
        completed: '2023-06-12T09:13:01.6960000',
      },
      {
        id: 200,
        name: 'trainingViewDriverAccount',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingViewDriverAccountDescription',
          },
        ],
        completed: '2023-09-22T11:18:42.9670000',
      },
      {
        id: 300,
        name: 'trainingAssignCard',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingAssignCardDescription',
          },
        ],
        completed: null,
      },
      {
        id: 400,
        name: 'trainingApproveDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingApproveDriverTariffDescription',
          },
        ],
        completed: '2023-09-26T11:54:35.4150000',
      },
      {
        id: 500,
        name: 'trainingRejectDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRejectDriverTariffDescription',
          },
        ],
        completed: null,
      },
      {
        id: 600,
        name: 'trainingPayoutReport',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingPayoutReportDescription',
          },
        ],
        completed: '2023-07-20T11:14:42.9120000',
      },
      {
        id: 700,
        name: 'trainingRequestUpdate',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRequestUpdateDescription',
          },
        ],
        completed: null,
      },
    ],
  },
  counts: {
    total: 1,
    pending_actions: 4,
  },
  information: [
    {
      tokens: [
        {
          token_visual_number: '913001000001000021',
          expiry_date: '2022-01-31',
          card_status: true,
          expiring_soon: true,
          issuer: 'Trafineo',
          reimbursement_status_id: 2,
        },
        {
          token_visual_number: '700673002590003205',
          expiry_date: '2025-04-30',
          card_status: true,
          expiring_soon: false,
          issuer: 'Trafineo',
          reimbursement_status_id: 2,
        },
      ],
      banking_details: {
        valid_from: '2024-09-19',
        last_four_iban_digits: '1211',
      },
      wallbox: {
        evse_id: 'DE*BPE*ESM*0000BD*01',
        location_of_wallbox: 'DE',
        address: {
          street: 'Test street',
          number: '1',
          city: 'city',
          postcode: '12345',
          country: 'DE',
          additional_information: '',
        },
      },
      vehicle: {
        id: 585,
        make: 'Mercedes',
        model: 'A 250 e',
        licence_plate: '12324',
      },
      ev_driver_idp_id: '********-7e5c-4b6d-a337-ac3b8df35b5b',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'Test',
      lastname: 'Test',
      driver_approval_status: [
        {
          driver_approval_status_name: 'app_user_only',
          actor: {
            email: null,
            first_name: null,
            last_name: null,
            user_idp_id: null,
          },
        },
        {
          driver_approval_status_name: 'tariff_modified',
          actor: {
            email: null,
            first_name: null,
            last_name: null,
            user_idp_id: null,
          },
        },
      ],
      tariffs: [
        {
          functional_tariff_id: 13104,
          currency: 'EUR',
          comment: '',
          disclaimer_accepted: true,
          contract_approval_date: '2024-09-23',
          start_date_time: '2024-08-26T22:00:00',
          end_date_time: '9999-12-31T00:00:00',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.7,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-08-27',
                  end_date: '9999-12-31',
                  start_time: null,
                  end_time: null,
                  min_kwh: null,
                  max_kwh: null,
                  min_power: null,
                  max_power: null,
                  min_duration: null,
                  max_duration: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                },
              ],
            },
          ],
          energy_mix: null,
        },
      ],
      last_updated: '2024-10-16T12:35:06.9600000',
    },
  ],
};

export default card_overview_by_driver_by_mail;
