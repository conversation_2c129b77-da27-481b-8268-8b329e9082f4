const card_overview = {
  user: {
    idp_id: '4cf5a528-1fee-4017-99bb-04d331934d50',
    Provider: 'aral',
    e_mail: '<EMAIL>',
    status: 'active',
    function: 'admin',
    principle: 'foureye',
    trainings: [
      {
        id: 100,
        training_name: 'trainingInviteDriver',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingInviteDriverDescription',
          },
        ],
        completed: true,
      },
      {
        id: 101,
        training_name: 'trainingGreetDriver',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingGreetDriverDescription',
          },
        ],
        completed: '2023-06-05T13:45:57.6500000',
      },
    ],
  },
  counts: {
    total: 60,
    active: 57,
    inactive: 3,
    assigned: 19,
    unassigned: 41,
    expiring_soon: 5,
  },
  information: [
    {
      token: {
        token_visual_number: '100000000000000001',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },

      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'K-AB-1231',
      },
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d818c8cdae1',
      email_address_ev_driver: '<EMAIL>',
      firstname: 'Indirect',
      lastname: 'User',
      driver_approval_status: [
        {
          driver_approval_status_name: 'created',
        },
      ],
      tariffs: null,
      department_id: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ordering_number: 1,
    },
    {
      token: {
        token_visual_number: '100000000001000017',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
        issuer: 'dcs',
      },
      banking_details: null,
      wallbox: null,
      vehicle: null,
      last_updated: '2022-11-24T17:58:07.0300000',
      ev_driver_idp_id: 'f26febfb-f209-4218-893b-3d718c8cdae9',
      driver_approval_status: [
        {
          driver_approval_status_name: 'app_user_only',
        },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 1,
    },
    {
      token: {
        token_visual_number: '100000000001000003',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: null,
      last_updated: '2022-11-21T17:32:01.6966667',
      ev_driver_idp_id: 'ee62e5bc-e1d4-47fe-b016-0d1e82ce78ae',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'app_user_only' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 2,
    },
    {
      token: {
        token_visual_number: '100000000001000001',
        expiry_date: '9999-12-31',
        card_status: false,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-11-07',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
        address: {
          street: 'Probsteigasse',
          number: '24',
          city: 'Köln',
          postcode: '50670',
          country: 'DE',
          additional_information: '',
        },
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'FDS',
      },
      last_updated: '2022-11-21T15:20:19.7500000',
      ev_driver_idp_id: '4bff8df8-3ba9-4d78-a52b-a6a3de81a71e',
      driver_approval_status: [
        {
          driver_approval_status_name: 'tariff_modified',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501067,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '2022-11-12',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501068,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.213,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-01',
                  end_date: '2022-11-03',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501069,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-26',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501077,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-04',
                  end_date: '2022-11-04',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501065,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.12,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-13',
                  end_date: '2022-11-25',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 3,
    },
    {
      token: {
        token_visual_number: '100000000001000010',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 2,
      },
      banking_details: {
        valid_from: '2022-11-07',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*321', location_of_wallbox: 'DE' },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'FDS',
      },
      last_updated: '2022-11-21T15:20:19.7500000',
      ev_driver_idp_id: '4bff8df8-3ba9-4d78-a52b-a6a3de81a71e',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'tariff_modified' },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501067,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '2022-11-12',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501068,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.213,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-01',
                  end_date: '2022-11-03',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501069,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-26',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501077,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-04',
                  end_date: '2022-11-04',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-21T15:19:31.6366667',
          tariff_elements: [
            {
              functional_tariff_id: 2501065,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.12,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-13',
                  end_date: '2022-11-25',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 4,
    },
    {
      token: {
        token_visual_number: '700674931159050216',
        expiry_date: '2021-11-30',
        card_status: false,
        expiring_soon: true,
        reimbursement_status_id: 3,
      },
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*323', location_of_wallbox: 'DE' },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'JK',
      },
      last_updated: '2022-11-18T13:10:44.2200000',
      ev_driver_idp_id: '49eea47d-0ff1-4699-b85d-4014f22884ef',
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501048,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-23',
                  end_date: '2022-11-04',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501073,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '2022-11-09',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-18T13:10:44.2100000',
          tariff_elements: [
            {
              functional_tariff_id: 2501074,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-10',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: 'DE8888',
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 5,
    },
    {
      token: {
        token_visual_number: '700673037839026377',
        expiry_date: '2024-02-29',
        card_status: false,
        expiring_soon: false,
        reimbursement_status_id: 3,
      },
      banking_details: {
        valid_from: '2019-09-07',
        last_four_iban_digits: '4619',
      },
      wallbox: {
        evse_id: 'DE*TRA*EEVB**********05',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'MW-ZE-8832',
      },
      last_updated: '2022-11-18T12:17:42.4150000',
      ev_driver_idp_id: '3b04d5ee-f172-4175-bc4c-d469645f1a57',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'app_user_only' },
        { driver_approval_status_name: 'approved' },
      ],
      reimbursement_status_id: 3,
      tariffs: null,
      department_id: '4678',
      email_address_ev_driver: '<EMAIL>',
    },
    {
      token: {
        token_visual_number: '700673037839026377',
        expiry_date: '2024-02-29',
        card_status: false,
        expiring_soon: false,
      },
      banking_details: {
        valid_from: '2021-09-03',
        last_four_iban_digits: '9700',
      },
      wallbox: { evse_id: 'DE*BPE*ERIO*0000003*01', location_of_wallbox: 'DE' },
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: '13' },
      last_updated: '2022-11-18T12:17:42.4150000',
      ev_driver_idp_id: '47fc2425-22cb-47e2-8eb4-092e9b0a7a27',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'banking_modified' },
        { driver_approval_status_name: 'banking_modified' },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2021-09-03T09:34:25.2566667',
          tariff_elements: [
            {
              functional_tariff_id: 47,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.15,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-09-01',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: '4678',
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 6,
    },
    {
      token: {
        token_visual_number: '700673037839026377',
        expiry_date: '2024-02-29',
        card_status: false,
        expiring_soon: false,
        reimbursement_status_id: 3,
      },
      banking_details: {
        valid_from: '2019-09-07',
        last_four_iban_digits: '4619',
      },
      wallbox: {
        evse_id: 'DE*TRA*EEVB**********05',
        location_of_wallbox: 'DE',
      },
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'MW-ZE-8832',
      },
      last_updated: '2022-11-18T12:17:42.4150000',
      ev_driver_idp_id: '3b04d5ee-f172-4175-bc4c-d469645f1a57',
      driver_approval_status: [
        {
          driver_approval_status_name: 'app_user_only',
        },
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: null,
      department_id: '4678',
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 7,
    },
    {
      token: {
        token_visual_number: '700678000183009444',
        expiry_date: '2024-02-29',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2018-09-07',
        last_four_iban_digits: '4227',
      },
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'EM-HZ-5784',
      },
      last_updated: '2022-11-18T12:17:42.4150000',
      ev_driver_idp_id: 'a8c04c17-eba8-4788-941a-11d53736b779',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'wallbox_update_requested' },
        { driver_approval_status_name: 'banking_update_requested' },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2021-08-26T08:08:44.6666667',
          tariff_elements: [
            {
              functional_tariff_id: 3,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.156,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2019-06-23',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: '4885',
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 8,
    },
    {
      token: {
        token_visual_number: '700673010851021022',
        expiry_date: '2025-04-30',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: '' },
      last_updated: '2022-11-18T12:17:03.2530000',
      ev_driver_idp_id: '2f239310-f39e-4063-9c30-34b1902900d5',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'logged_in_once' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 9,
    },
    {
      token: {
        token_visual_number: '700673010851021030',
        expiry_date: '2025-04-30',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: '' },
      last_updated: '2022-11-18T12:17:03.2530000',
      ev_driver_idp_id: 'f6526955-46b2-4024-8ec1-88485330df57',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'logged_in_once' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 10,
    },
    {
      token: {
        token_visual_number: '700673010851021048',
        expiry_date: '2025-04-30',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-08-31',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*325', location_of_wallbox: 'DE' },
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: '' },
      last_updated: '2022-11-18T12:17:03.2530000',
      ev_driver_idp_id: 'c6de8470-6618-4e5e-bff2-76be8b48a519',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'approved' },
        { driver_approval_status_name: 'wallbox_update_requested' },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 2501022,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-06-03',
                  end_date: '2022-08-13',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: false,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 50,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-10-11',
                  end_date: '2022-06-02',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 2501039,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-08-14',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-08-31T15:00:58.2133333',
          tariff_elements: [
            {
              functional_tariff_id: 51,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2021-10-01',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 11,
    },
    {
      token: {
        token_visual_number: '700673010851021055',
        expiry_date: '2025-04-30',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: '' },
      last_updated: '2022-11-18T12:17:03.2530000',
      ev_driver_idp_id: 'ed49400b-c168-4094-b0b9-f80d00e90181',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'logged_in_once' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 12,
    },
    {
      token: {
        token_visual_number: '700673010851021063',
        expiry_date: '2025-04-30',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-09-21',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*321', location_of_wallbox: 'DE' },
      vehicle: {
        id: 421,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'SD',
      },
      last_updated: '2022-11-18T12:17:03.2530000',
      ev_driver_idp_id: 'b278c427-693c-4374-bc21-ebf7e3c0b8ae',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'initial_data_entered' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 13,
    },
    {
      token: {
        token_visual_number: '100000000001000022',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: null,
      last_updated: '2022-11-10T12:43:13.3400000',
      ev_driver_idp_id: '********-b9eb-478f-be89-ed07bc0fb73c',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'logged_in_once' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 14,
    },
    {
      token: {
        token_visual_number: '100000000001000012',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: null,
      last_updated: '2022-11-07T13:10:01.5300000',
      ev_driver_idp_id: '1d1b2cf0-9a58-46b2-8e3a-f412f99c3d76',
      driver_approval_status: [
        {
          driver_approval_status_name: 'invited',
        },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 15,
    },
    {
      token: {
        token_visual_number: '100000000001000009',
        expiry_date: '2025-12-31',
        card_status: true,
        expiring_soon: false,
        reimbursement_status_id: 3,
      },
      banking_details: {
        valid_from: '2022-11-07',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*324', location_of_wallbox: 'DE' },
      vehicle: {
        id: 574,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DF',
      },
      last_updated: '2022-11-07T12:31:30.1633333',
      ev_driver_idp_id: 'ea6b91db-4969-4099-869a-804751318a63',
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
        },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-11-07T12:31:30.1533333',
          tariff_elements: [
            {
              functional_tariff_id: 2501066,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-11-05',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 16,
    },
    {
      token: {
        token_visual_number: '911673011371000853',
        expiry_date: '2021-01-01',
        card_status: true,
        expiring_soon: true,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: {
        id: 1,
        make: 'other',
        model: 'other',
        licence_plate: 'TD-GE-1184',
      },
      last_updated: '2022-10-05T07:41:41.4733333',
      ev_driver_idp_id: '63fac5e4-5f51-44d2-9870-709970cad83c',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'initial_data_entered' },
        { driver_approval_status_name: 'wallbox_modified' },
        { driver_approval_status_name: 'tariff_modified' },
        { driver_approval_status_name: 'banking_modified' },
        { driver_approval_status_name: 'wallbox_modified' },
        { driver_approval_status_name: 'tariff_modified' },
        { driver_approval_status_name: 'banking_modified' },
        { driver_approval_status_name: 'wallbox_modified' },
        { driver_approval_status_name: 'tariff_modified' },
        { driver_approval_status_name: 'banking_modified' },
      ],
      tariffs: null,
      department_id: '1678',
      email_address_ev_driver: '<EMAIL>',
    },
    {
      token: {
        token_visual_number: '911673011371000856',
        expiry_date: '2022-01-01',
        card_status: true,
        expiring_soon: true,
        reimbursement_status_id: 0,
      },
      banking_details: null,
      wallbox: null,
      vehicle: { id: 1, make: 'other', model: 'other', licence_plate: 'TEST' },
      last_updated: '2022-10-05T07:41:41.4733333',
      ev_driver_idp_id: 'deb6e66e-9ccd-457e-a429-d7a01bfbde57',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'logged_in_once' },
      ],
      tariffs: null,
      department_id: '123',
      email_address_ev_driver: '<EMAIL>',
    },
    {
      token: {
        token_visual_number: '911673011371000857',
        expiry_date: '2022-01-01',
        card_status: true,
        expiring_soon: true,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-09-21',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*321', location_of_wallbox: 'DE' },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DSF',
      },
      last_updated: '2022-10-05T07:41:41.4733333',
      ev_driver_idp_id: '4ac9d72c-ed63-4ffe-8736-bfff176f7f18',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'initial_data_entered' },
        { driver_approval_status_name: 'tariff_modified' },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
    },
    {
      token: {
        token_visual_number: '911673011371000858',
        expiry_date: '2022-01-01',
        card_status: true,
        expiring_soon: true,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: { evse_id: 'DE*8AA*E456*78*321', location_of_wallbox: 'DE' },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DFS',
      },
      last_updated: '2022-10-05T07:41:41.4733333',
      ev_driver_idp_id: 'b4e5817f-e2f1-4c87-9d83-658eea30db98',
      invitation_sent_to: '<EMAIL>',
      driver_approval_status: [
        { driver_approval_status_name: 'tariff_modified' },
      ],
      tariffs: [
        {
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2022-09-22T12:46:03.0800000',
          tariff_elements: [
            {
              functional_tariff_id: 2501049,
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  tariff_sub_type: 'ONETARIFF',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_time: null,
                  end_time: null,
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                    'SATURDAY',
                    'SUNDAY',
                  ],
                  start_date: '2022-09-03',
                  end_date: '9999-12-31',
                },
              ],
            },
          ],
        },
      ],
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 60,
    },
    {
      token: {
        token_visual_number: '911673011371000859',
        expiry_date: '2022-01-01',
        card_status: true,
        expiring_soon: true,
        reimbursement_status_id: 0,
      },
      banking_details: {
        valid_from: '2022-09-22',
        last_four_iban_digits: '0655',
      },
      wallbox: {
        evse_id: 'DE*8AA*E456*78*321',
        location_of_wallbox: 'DE',
        address: {
          street: 'Im Mediapark',
          number: '5c',
          city: 'köln',
          postcode: '50670',
          country: 'DE',
          additional_information: 'Timetoact',
        },
      },
      vehicle: {
        id: 573,
        make: 'Audi',
        model: 'A3 Sportback',
        licence_plate: 'DF',
      },
      last_updated: '2022-10-05T07:41:41.4733333',
      ev_driver_idp_id: 'df2447ce-5172-4dde-a319-a893a129bc1f',
      driver_approval_status: [
        {
          driver_approval_status_name: 'initial_data_entered',
        },
      ],
      tariffs: null,
      department_id: null,
      email_address_ev_driver: '<EMAIL>',
      ordering_number: 61,
    },
  ],
};

export default card_overview;
