const trainingsFleetmanagerDirect = {
  user: {
    idp_id: '5ecf7261-2cd1-4fea-bd29-7e153265d02b',
    provider: 'bp',
    e_mail: '<EMAIL>',
    status: 'active',
    function: 'admin',
    principle: 'foureye',
    trainings: [
      {
        id: 100,
        name: 'trainingInviteDriver',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingInviteDriverDescription',
          },
        ],
        completed: '2023-06-12T09:13:01.6960000',
      },
      {
        id: 200,
        name: 'trainingViewDriverAccount',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingViewDriverAccountDescription',
          },
        ],
        completed: '2023-09-22T11:18:42.9670000',
      },
      {
        id: 300,
        name: 'trainingAssignCard',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingAssignCardDescription',
          },
        ],
        completed: null,
      },
      {
        id: 400,
        name: 'trainingApproveDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingApproveDriverTariffDescription',
          },
        ],
        completed: '2023-09-26T11:54:35.4150000',
      },
      {
        id: 500,
        name: 'trainingRejectDriverTariff',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRejectDriverTariffDescription',
          },
        ],
        completed: null,
      },
      {
        id: 600,
        name: 'trainingPayoutReport',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingPayoutReportDescription',
          },
        ],
        completed: '2023-07-20T11:14:42.9120000',
      },
      {
        id: 700,
        name: 'trainingRequestUpdate',
        content: [
          {
            type: 'gif',
            key: 'description',
            value: 'trainingRequestUpdateDescription',
          },
        ],
        completed: null,
      },
    ],
  },
  counts: {
    total: 11317,
    active: 6441,
    inactive: 4876,
    assigned: 194,
    unassigned: 11123,
    expiring_soon: 92,
  },
  information: [
    {
      token: {
        token_visual_number: '700678069797303097',
        expiry_date: '2029-02-28',
        card_status: true,
        expiring_soon: false,
        issuer: 'Trafineo',
        reimbursement_status_id: 2,
      },
      banking_details: {
        valid_from: '2024-11-28',
        last_four_iban_digits: '7890',
      },
      wallbox: {
        evse_id: 'DE*BPE*E*123456*01',
        location_of_wallbox: 'DE',
        address: {
          street: 'Street',
          number: '50',
          city: 'City',
          postcode: 'RH12',
          country: 'DE',
          additional_information: '',
        },
      },
      vehicle: {
        id: 903,
        make: 'Skoda',
        model: 'Enyaq',
        licence_plate: 'KN66',
      },
      last_updated: '2025-01-07T16:03:58.2480000',
      ev_driver_idp_id: '1832ece3-d3ff-4053-ae9e-3004e805d3c9',
      driver_approval_status: [
        {
          driver_approval_status_name: 'approved',
          actor: {
            email: '<EMAIL>',
            first_name: null,
            last_name: null,
            user_idp_id: '902e9833-21e7-4496-8d8d-12f43b867c76',
          },
        },
      ],
      tariffs: [
        {
          functional_tariff_id: 2513715,
          start_date_time: '2024-11-27T23:00:00',
          end_date_time: '9999-12-31T00:00:00',
          comment: '',
          currency: 'EUR',
          current_tariff: true,
          last_updated: '2024-12-05T06:27:44.4333333',
          tariff_elements: [
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.1,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-11-28',
                  end_date: '9999-12-31',
                  start_time: '05:00:00',
                  end_time: '09:00:00',
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                  ],
                },
              ],
            },
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.05,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-11-28',
                  end_date: '9999-12-31',
                  start_time: '09:00:00',
                  end_time: '05:00:00',
                  day_of_week: [
                    'MONDAY',
                    'TUESDAY',
                    'WEDNESDAY',
                    'THURSDAY',
                    'FRIDAY',
                  ],
                },
              ],
            },
            {
              price_components: [
                {
                  tariff_type: 'ENERGY',
                  price: 0.05,
                  step_size: 1,
                },
              ],
              restrictions: [
                {
                  start_date: '2024-11-28',
                  end_date: '9999-12-31',
                  start_time: null,
                  end_time: null,
                  day_of_week: ['SATURDAY', 'SUNDAY'],
                },
              ],
            },
          ],
          energy_mix: {
            is_green_energy: false,
            environ_impact: [
              {
                category: 'CARBON_DIOXIDE',
                amount: 0.03,
              },
            ],
          },
        },
      ],
      email_address_ev_driver: '<EMAIL>',
      firstname: 'Samantha',
      lastname: 'Lingard',
    },
  ],
};

export default trainingsFleetmanagerDirect;
