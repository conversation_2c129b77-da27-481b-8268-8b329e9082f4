import { useState, useEffect } from 'react';
import PropTypes from 'prop-types';
import { Box } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import Dialog from '../Dialog';
import Button from '../Button';
import requester from '../../utils/requester';
import logger from '../../utils/logger';
import PdfRender from '../PdfRenderer';
import { Description, InputWrapper } from '../evDriver/PageTemplate';
import Select from '../Select';
import MenuItem from '../MenuItem';
import CountryFlag from '../evDriver/CountryFlag';
import styled from 'styled-components';

const WrapperBox = styled(Box)`
  min-width: 400px;
  display: flex;
  flex-direction: column;
  padding: 2rem 1rem;
  box-sizing: border-box;
  align-items: center;
  height: 80vh;
  width: 70vw;
`;

const DialogBox = styled(Box)`
  height: calc(100% - 6rem);
  width: 100%;
  margin: 1rem;
  padding: 1rem;
  box-sizing: border-box;
  overflow: scroll;
`;

const CloseBox = styled(Box)`
  margin-top: 1rem;
  width: 220px;
`;

const StaticDialog = ({ open, onClose, type, role, langSelect }) => {
  const { t } = useTranslation('actions');
  const tLocalization = useTranslation('localization').t;
  const [country, setCountry] = useState(null);
  const [content, setContent] = useState('');

  useEffect(() => {
    if (open) {
      const getData = async () => {
        try {
          const { data } = await requester().get('/Policies/Get_Policies', {
            params: {
              type: type || 'DPS',
              country_code: langSelect ? country : 'de',
              language: window.localStorage.getItem('selected-language')
                ? window.localStorage.getItem('selected-language').substr(1, 2)
                : 'en',
            },
            headers: {
              role,
            },
          });
          setContent(data.message);
        } catch (err) {
          console.error(err);
          logger(true).error(err.message);
        }
      };
      getData();
    }
  }, [role, type, open, country, langSelect]);

  return (
    <Dialog
      {...{
        open,
        onClose,
      }}
    >
      <WrapperBox>
        {langSelect && (
          <div>
            <InputWrapper>
              <Description>{tLocalization('chooseCountry')}</Description>
              <Select
                autoFocus
                big
                value={country}
                onChange={(e) => {
                  setCountry(e.target.value);
                }}
              >
                {Object.values(['nl', 'at']).map((entry) => (
                  <MenuItem
                    key={entry}
                    value={entry}
                    data-cy={`${entry}Select`}
                  >
                    <CountryFlag country={entry} />
                    {tLocalization(`${entry}CountryName`)}
                  </MenuItem>
                ))}
              </Select>
            </InputWrapper>
          </div>
        )}
        <DialogBox>
          {((langSelect && country !== null) || !langSelect) && (
            <>
              {window.localStorage.getItem('selected-language') &&
                (type === 'DPS' || type === 'DPS_Indirect') && (
                  <PdfRender
                    src={`data-privacy-${window.localStorage
                      .getItem('selected-language')
                      .substring(
                        1,
                        window.localStorage.getItem('selected-language')
                          .length - 1,
                      )}.pdf`}
                  />
                )}

              {type !== 'DPS' && <PdfRender inDialog src={content} />}
              {(type === 'DPS' || type === 'DPS_Indirect') &&
                !window.localStorage.getItem('selected-language') && (
                  <PdfRender src={`data-privacy-en.pdf`} />
                )}
            </>
          )}
        </DialogBox>
        <CloseBox>
          <Button onClick={() => onClose()} variant="primary">
            {t('close')}
          </Button>
        </CloseBox>
      </WrapperBox>
    </Dialog>
  );
};

StaticDialog.propTypes = {
  open: PropTypes.bool,
  langSelect: PropTypes.bool,
  onClose: PropTypes.func,
  type: PropTypes.string,
  role: PropTypes.string,
};

export default StaticDialog;
