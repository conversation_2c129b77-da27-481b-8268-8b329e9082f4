import { PDFDownloadLink } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';
import CircularProgress from './CircularProgress';
import { Button } from '.';
import { LoadingCircleContainer } from './dashboard/DashboardTemplates';
import { MenuItemWrapper } from './HeaderBar';
import { Menu } from '@material-ui/core';

import { useState } from 'react';

const DownloadMenu = ({
  getDownloadData,
  disabled,
  onExcelDownloadClick,
  pdfFileName,
  PdfTable,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [data, setData] = useState(false);
  const { t } = useTranslation('overview');
  const tDirectCustomer = useTranslation('directCustomer').t;

  const handleClick = async (event) => {
    setIsLoading(true);
    setAnchorEl(event.currentTarget);
    const rsp = await getDownloadData();
    if (rsp) {
      setData(rsp);
    }
    setIsLoading(false);
  };

  const handleClose = (e) => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  return (
    <MenuItemWrapper rawQueryString>
      <Button
        style={{ width: '200px' }}
        disableRipple
        disabled={disabled}
        aria-controls="basic-menu"
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
      >
        {tDirectCustomer('downloadData')}
      </Button>
      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        getContentAnchorEl={null}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {(isLoading && (
          <LoadingCircleContainer
            style={{ width: '200px', padding: '0.25rem' }}
          >
            <CircularProgress />
          </LoadingCircleContainer>
        )) || (
          <div style={{ padding: '0.25rem' }}>
            <div style={{ paddingBottom: '0.5rem' }}>
              <Button
                style={{ width: '200px' }}
                onClick={() => onExcelDownloadClick(data)}
                variant="primary"
              >
                {t('Excel')}
              </Button>
            </div>
            <div>
              <PDFDownloadLink
                document={<PdfTable data={data} />}
                fileName={pdfFileName}
              >
                {({ blob, url, loading, error }) =>
                  loading ? (
                    <LoadingCircleContainer>
                      <CircularProgress />
                    </LoadingCircleContainer>
                  ) : (
                    <Button style={{ width: '200px' }} variant="primary">
                      {t('PDF')}
                    </Button>
                  )
                }
              </PDFDownloadLink>
            </div>
          </div>
        )}
      </Menu>
    </MenuItemWrapper>
  );
};
export default DownloadMenu;
