import PropTypes from 'prop-types';
import styled from 'styled-components';

const StyledError = styled.p`
  display: ${(props) => (props.hidden ? 'none' : '')};
  font-size: 14px;
  text-align: center;
  color: var(--error-color);
`;

function Error({ visible = false, text, ...props }) {
  return (
    <StyledError hidden={!visible} {...props}>
      {text}
    </StyledError>
  );
}

Error.defaultProps = {
  visible: false,
};

Error.propTypes = {
  visible: PropTypes.bool,
  text: PropTypes.string.isRequired,
};

export default Error;
