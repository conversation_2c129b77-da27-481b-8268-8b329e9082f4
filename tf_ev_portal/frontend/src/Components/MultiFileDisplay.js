import CancelButton from '../Components/CancelButton';
import styled from 'styled-components';

const File = styled.div`
  color: ${(props) => props.textColor};
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 38px;
  flex-grow: 0;
  font-size: 14px;
  margin-right: 0.5rem;
  padding: 0 0.5rem;
  color: black;
  border-radius: 50px;
  border: solid 2px;
  background-color: #f1f1f1;
  border-color: var(--trafineo-rot-100);
`;
const FileRow = styled.div`
  display: flex;
  margin-bottom: 1rem;
  min-height: 50px;
  flex-wrap: wrap;
  width: 100%;
`;
const FileWrapper = styled.div`
  position: relative;
  display: flex;
  margin-top: 0.5rem;
`;

const MultiFileDisplay = ({ files, onFileDelete }) => {
  return (
    <FileRow>
      {files.map((e) => (
        <FileWrapper data-cy="file" key={e.name}>
          <File>{e.name}</File>
          <CancelButton
            color="var(--trafineo-rot-100)"
            onClick={() => {
              onFileDelete(e.name);
            }}
          />
        </FileWrapper>
      ))}
    </FileRow>
  );
};
export default MultiFileDisplay;
