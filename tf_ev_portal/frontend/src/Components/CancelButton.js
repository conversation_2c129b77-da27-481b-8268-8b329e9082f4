import PropTypes from 'prop-types';
import ClearIcon from '@material-ui/icons/Clear';
import styled from 'styled-components';

const StyledButton = styled.button`
  width: 20px;
  height: 20px;
  border: solid 1px;
  border-color: ${(props) =>
    props.color ? props.color : 'var(--trafineo-rot-100)'};
  box-shadow: 0 0 16px 0 rgba(198, 0, 24, 0.16);
  background-color: ${(props) => (props.color ? props.color : '#FFF')};
  border-radius: 16px;
  color: ${(props) => (props.color ? '#FFF' : 'var(--trafineo-rot-100)')};
  font-size: 12px;
  outline: none;
  will-change: filter;
  transition: filter 0.25s ease-in-out;
  cursor: pointer;
  :hover {
    filter: brightness(90%);
  }
  position: absolute;
  top: -5px;
  right: ${(props) => (props.color ? '12px' : '-5px')};
`;

const StyledClearIcon = styled(ClearIcon)`
  height: 15px !important;
  width: 15px !important;
  margin-left: -4px;
`;

const CancelButton = ({ children, color, ...rest }) => {
  // variants: primary, secondary, tertiary, special, disabled
  // variants are unused for now

  return (
    <StyledButton color={color} {...rest}>
      <StyledClearIcon></StyledClearIcon>
    </StyledButton>
  );
};

CancelButton.defaultProps = {
  color: null,
};

CancelButton.propTypes = {
  color: PropTypes.string,
};

export default CancelButton;
