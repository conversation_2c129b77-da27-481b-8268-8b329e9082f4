import { Fragment } from 'react';
import { Page, Document, StyleSheet, View, Text } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';
import { formatDateTime } from '../utils/helper';

const styles = StyleSheet.create({
  page: {
    fontSize: 9,
    flexDirection: 'column',
  },
  tableContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowuneven: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'lightgray',
  },
  titleRow: {
    backgroundColor: 'black',
    color: 'white',
    flexDirection: 'row',
    alignItems: 'center',
  },
  col: {
    width: '25%',
    textAlign: 'left',
    padding: '3px',
  },
});

const ReimbursementTable = ({ data }) => {
  return (
    <Document>
      <Page size="A4" wrap style={styles.page}>
        <ItemsTable data={data} />
      </Page>
    </Document>
  );
};

const ItemsTable = ({ data }) => {
  return (
    <View style={styles.tableContainer}>
      <TableRow data={data} />
    </View>
  );
};

const TableRow = ({ data }) => {
  const { t } = useTranslation('overview');
  const rows = data.map((item, index) => {
    const Row = () => (
      <>
        <View
          style={index % 2 === 0 ? styles.row : styles.rowuneven}
          key={index}
        >
          <Text style={styles.col}>{formatDateTime(item.date_paid)}</Text>
          <Text style={styles.col}>{item.paid_amount}</Text>
          <Text style={styles.col}>{item.applied_tariff}</Text>
          <Text style={styles.col}>{item.consumption}</Text>
        </View>
      </>
    );
    if (index === 0 || index % 31 === 0) {
      return (
        <>
          <View
            break={index > 0 && index % 31 === 0}
            style={styles.rowempty}
          ></View>
          <View style={styles.titleRow}>
            <Text style={styles.col}>{t('datePaid')}</Text>
            <Text style={styles.col}>{t('paidAmmount')}</Text>
            <Text style={styles.col}>{t('appliedTariff')}</Text>
            <Text style={styles.col}>{t('consumption')}</Text>
          </View>
          <Row />
        </>
      );
    }

    return <Row />;
  });
  return <Fragment>{rows}</Fragment>;
};

export default ReimbursementTable;
