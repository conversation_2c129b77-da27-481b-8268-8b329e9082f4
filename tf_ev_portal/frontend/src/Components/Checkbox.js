import MuiCheckbox from '@material-ui/core/Checkbox';
import CloseIcon from '@material-ui/icons/Close';
import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import classNames from 'classnames';

const useStyles = makeStyles({
  root: {
    width: '24px',
    height: '24px',
    padding: '0',
    borderRadius: '4px',
    boxShadow: '0 0 3px 0 rgba(0, 0, 0, 0.31)',
    backgroundColor: '#ffffff',
    '& svg': {
      display: 'none',
    },
    '&:hover': {
      backgroundColor: '#ffffff!important',
    },
  },
  large: {
    width: '32px',
    height: '32px',
  },
  error: {
    border: '1px solid var(--error-color)',
  },
  small: {
    width: '14px',
    height: '14px',
    '& svg': {
      width: '14px',
      height: '14px',
    },
  },
  checked: {
    '& svg': {
      display: 'block',
      color: 'var(--trafineo-rot-100)',
    },
  },
  disabled: {
    background: '#e7e7e7 !important',
    border: '1px solid #acadae',
    boxShadow: '0 0 16px 0 rgba(172,173,174, 0.14)',
    opacity: 0.5,
  },
});

function Checkbox({ boxSize, checked, disabled, error, ...rest }) {
  const classes = useStyles();

  return (
    <MuiCheckbox
      disableRipple
      {...{ checked, disabled }}
      className={classNames(
        classes.root,
        checked && classes.checked,
        error && classes.error,
        boxSize === 'large' && classes.large,
        boxSize === 'small' && classes.small,
        disabled && classes.disabled,
      )}
      checkedIcon={<CloseIcon />}
      {...rest}
    />
  );
}

Checkbox.defaultProps = {
  boxSize: '',
  checked: false,
  disabled: false,
};

Checkbox.propTypes = {
  boxSize: PropTypes.string,
  checked: PropTypes.bool,
  disabled: PropTypes.bool,
};

export default Checkbox;
