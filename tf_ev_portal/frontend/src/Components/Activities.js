import { useState, useCallback, useEffect } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { formatDateTime } from '../utils/helper';
import requester from '../utils/requester';
import Center from './helper/Center';
import CircularProgress from './CircularProgress';
import {
  HeadlineBox,
  TableContainer,
  Wrapper,
} from './dashboard/DashboardTemplates';
import { Row, RowBox, RowContainer, TitleRow } from './dashboard/RowTemplates';

import Button from './Button';
import DialogWrapper from './DialogWrapper';
import { isMobile } from 'react-device-detect';
import ChatIcon from '@mui/icons-material/Chat';
import TagManager from 'react-gtm-module';

const Activities = ({ driverData }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [activities, setActivities] = useState([]);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState('');
  const { t } = useTranslation('profile');
  const tDirectCustomer = useTranslation('directCustomer').t;

  const getData = useCallback(async () => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get(
          `/status_history/${
            driverData ? `${driverData.ev_driver_idp_id}` : ''
          }`,
        )
      ).data;
      setIsLoading(false);
      return rsp;
    } catch (err) {
      setIsLoading(false);
      return undefined;
    }
  }, [driverData]);

  const checkForDirectDriverWithCreatedStatus = (activityData) => {
    const hasNegativeStatus = activityData.find(
      (activity) => activity.status_id === -1,
    );

    if (!hasNegativeStatus) {
      return activityData;
    }

    const isDirect = activityData.find(
      (activity) =>
        activity.status_id !== -1 &&
        activity.valid_from === hasNegativeStatus.valid_from,
    );

    if (!isDirect) {
      return activityData;
    }

    activityData.splice(
      activityData.findIndex((activity) => activity.status_id === -1),
      1,
    );

    return activityData;
  };

  const onClose = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'popup_interaction',
        popup: {
          name: `popup_activities_comment`,
          interaction_type: 'close',
        },
      },
    });

    setIsDialogOpen(false);
  };

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getData();
      if (rspData) {
        setActivities(checkForDirectDriverWithCreatedStatus(rspData));
      }
    };
    fetchData();
  }, [getData]);

  const Table = () => {
    return (
      <Wrapper>
        <TableContainer>
          <TitleRow style={{ height: '52px' }}>
            <HeadlineBox style={{ height: '70px', margin: 0 }}>
              <RowBox bold width={2.5}></RowBox>
              <RowBox bold width={17}>
                {t('dateOfAction')}
              </RowBox>
              <RowBox bold width={25}>
                {t('fleetManager')}
              </RowBox>
              <RowBox bold width={53}>
                {t('status')}
              </RowBox>
              <RowBox bold width={17}>
                {t('comment')}
              </RowBox>
            </HeadlineBox>
          </TitleRow>
          {(isLoading && (
            <Center style={{ padding: '5rem' }}>
              <CircularProgress />
            </Center>
          )) || (
            <div>
              {activities.map((row, i) => {
                return (
                  <Row
                    style={
                      isMobile ? { minHeight: '70px' } : { height: '70px' }
                    }
                  >
                    <RowContainer>
                      <RowBox bold width={2.5}></RowBox>
                      <RowBox width={17}>
                        {formatDateTime(row.valid_from)}
                      </RowBox>
                      <RowBox width={25}>{row.actor_email_address}</RowBox>
                      <RowBox width={53}>
                        {t(
                          `${driverData ? 'status' : 'driverStatus'}${
                            row.status_type === 'reimbursement_status'
                              ? !row.actor_email_address && row.status_id === 3
                                ? 'ReimbursementDenied'
                                : 'Reimbursement'
                              : ''
                          }${row.status_id}`,
                        )}
                      </RowBox>
                      <RowBox width={17}>
                        {row.comment && (
                          <>
                            <Button
                              onClick={() => {
                                TagManager.dataLayer({
                                  dataLayer: {
                                    event: 'popup_interaction',
                                    popup: {
                                      name: `popup_activities_comment`,
                                      interaction_type: 'open',
                                    },
                                  },
                                });
                                setIsDialogOpen(true);
                                setDialogData(row.comment);
                              }}
                              variant="primary"
                            >
                              {isMobile ? <ChatIcon /> : t('showComment')}
                            </Button>
                          </>
                        )}
                      </RowBox>
                    </RowContainer>
                  </Row>
                );
              })}
            </div>
          )}

          {!isLoading && activities.length === 0 && (
            <Center>
              <h2>{tDirectCustomer('noDataAvailable')}</h2>
            </Center>
          )}
        </TableContainer>
        <DialogWrapper
          open={isDialogOpen}
          onClose={onClose}
          headline={t('comment')}
        >
          <p>{dialogData}</p>
        </DialogWrapper>
      </Wrapper>
    );
  };
  return <Table />;
};

Activities.propTypes = {
  driverData: PropTypes.object,
};

export default Activities;
