import styled from 'styled-components';
import PropTypes from 'prop-types';
import Center from './Center';
import ContentWidth from './ContentWidth';

const Paper = styled.div`
  ${(props) => {
    if (props.paper) {
      return `
          border-radius: 7px;
          background: #fff;
          box-shadow: 0 0 6px 0 rgba(0, 0, 0, 0.31);
          margin-bottom: 4rem;
        `;
    }
    return ``;
  }}
`;

const Content = ({ children, headline, paper }) => (
  <Center>
    <ContentWidth>
      {headline}
      <Paper paper={paper}>{children}</Paper>
    </ContentWidth>
  </Center>
);

Content.defaultProps = {
  headline: '',
  paper: false,
};

Content.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
  headline: PropTypes.string,
  paper: PropTypes.bool,
};

export default Content;
