import styled from 'styled-components';
import PropTypes from 'prop-types';

const Container = styled.div`
  display: flex;
  justify-content: center;
  text-align: ${(props) => props.textAlign};
`;

const Center = ({ children, textAlign, ...rest }) => {
  return (
    <Container {...rest} textAlign={textAlign}>
      {children}
    </Container>
  );
};

Center.defaultProps = {
  textAlign: 'left',
};

Center.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
  textAlign: PropTypes.string,
};

export default Center;
