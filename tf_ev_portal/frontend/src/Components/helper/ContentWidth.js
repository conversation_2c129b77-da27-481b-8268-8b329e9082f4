import styled from 'styled-components';
import PropTypes from 'prop-types';

const Container = styled.div`
  width: 100%;
  padding: 0 20px;
  max-width: 1500px;
  box-sizing: border-box;
  position: relative;
`;

const ContentWidth = ({ children }) => {
  return <Container>{children}</Container>;
};

ContentWidth.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
};

export default ContentWidth;
