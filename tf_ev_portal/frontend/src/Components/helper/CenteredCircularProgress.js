import styled from 'styled-components';
import CircularProgress from '../CircularProgress';

const Wrapper = styled.div`
  display: flex;
  height: var(--app-height-driver);
`;

const Container = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
`;

const CenteredCircularProgress = () => {
  return (
    <Wrapper>
      <Container>
        <CircularProgress />
      </Container>
    </Wrapper>
  );
};

export default CenteredCircularProgress;
