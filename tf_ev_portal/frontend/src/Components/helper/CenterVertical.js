import styled from 'styled-components';
import PropTypes from 'prop-types';

const Container = styled.div`
  display: flex;
  align-items: center;
  text-align: left;
`;

const CenterVertical = ({ children }) => {
  return <Container>{children}</Container>;
};

CenterVertical.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
};

export default CenterVertical;
