import { Fragment } from 'react';
import { Page, Document, StyleSheet, View, Text } from '@react-pdf/renderer';
import { useTranslation } from 'react-i18next';
import { formatDateTime, secondsToHms } from '../utils/helper';

const styles = StyleSheet.create({
  page: {
    fontSize: 9,
    flexDirection: 'column',
  },
  tableContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  row: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  rowuneven: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'lightgray',
  },
  titleRow: {
    backgroundColor: 'black',
    color: 'white',
    flexDirection: 'row',
    alignItems: 'center',
  },
  col: {
    width: '13%',
    textAlign: 'left',
    padding: '3px',
  },
  colLarge: {
    width: '17.5%',
    textAlign: 'left',
    padding: '5x',
  },
});

const CdrTable = ({ data }) => {
  return (
    <Document>
      <Page size="A4" wrap style={styles.page}>
        <ItemsTable data={data} />
      </Page>
    </Document>
  );
};

const ItemsTable = ({ data }) => {
  return (
    <View style={styles.tableContainer}>
      <TableRow data={data} />
    </View>
  );
};

const TableRow = ({ data }) => {
  const { t } = useTranslation('overview');
  const rows = data.map((item, index) => {
    const Row = () => (
      <>
        <View
          style={index % 2 === 0 ? styles.row : styles.rowuneven}
          key={index}
        >
          <Text style={styles.col}>
            {t(`reimbursementStatus${item.cdr_status_id}`)}
          </Text>
          <Text style={styles.colLarge}>{item.token_visual_number}</Text>
          <Text style={styles.colLarge}>{item.evse_id}</Text>
          <Text style={styles.col}>{formatDateTime(item.session_start)}</Text>
          <Text style={styles.col}>{formatDateTime(item.session_end)}</Text>
          <Text style={styles.col}>{item.cdr_total_energy}</Text>
          <Text style={styles.col}>{secondsToHms(item.total_time)}</Text>
        </View>
      </>
    );
    if (index === 0 || index % 31 === 0) {
      return (
        <>
          <View
            break={index > 0 && index % 31 === 0}
            style={styles.rowempty}
          ></View>
          <View style={styles.titleRow}>
            <Text style={styles.col}>{t('status')}</Text>
            <Text style={styles.colLarge}>{t('cardNumber')}</Text>
            <Text style={styles.colLarge}>{t('evse')}</Text>
            <Text style={styles.col}>{t('startTime')}</Text>
            <Text style={styles.col}>{t('stopTime')}</Text>
            <Text style={styles.col}>{t('totalEnergy')}</Text>
            <Text style={styles.col}>{t('totalTime')}</Text>
          </View>
          <Row />
        </>
      );
    }

    return <Row />;
  });
  return <Fragment>{rows}</Fragment>;
};

export default CdrTable;
