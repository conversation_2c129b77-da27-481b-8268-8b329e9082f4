import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import QuestionMark from './QuestionMark';
import { isMobile } from 'react-device-detect';

const useStyles = makeStyles({
  textField: {
    minWidth: '200px',
    textAlign: 'left',
    margin: '0.5rem 0',
  },
  input: {
    fontFamily: 'var(--font-family)',
    width: '100%',
    boxSizing: 'border-box',
    marginTop: 0,
    lineHeight: isMobile ? '24px' : '21px',
    border: '1px solid #EBEBEB',
    opacity: 0.7,
    backgroundColor: '#F7F7F7',
    borderRadius: 0,
    caretColor: '#666',
    boxShadow: 'none',
    fontSize: isMobile ? '16px' : '14px',
    padding: '0.5rem',
    outline: 'none',
    '&:disabled': {
      backgroundColor: 'var(--trafineo-grau-50)',
    },
    '&:focus': {
      border: 'solid 1px var(--default-text)',
    },
    '&:hover': {
      border: 'solid 1px var(--default-text)',
    },
  },
  small: {
    height: 30,
    lineHeight: 1.17,
    fontWeight: 'bold',
    fontSize: '12px',
    margin: '0',
  },
  label: {
    fontSize: '19px',
    fontWeight: 'normal',
    fontStretch: 'normal',
    fontStyle: 'normal',
    lineHeight: 1.16,
    letterSpacing: '0.34px',
    textAlign: 'left',
    color: '#414042',
  },
  tooltip: {
    marginLeft: '0.5rem',
    background: '#ddd',
    color: '#666',
    width: 20,
    borderRadius: '50%',
    display: 'inline-block',
    textAlign: 'center',
  },
  disabled: {
    color: 'rgba(65, 64, 66, 0.55)',
  },
});

const TextArea = ({
  fatLabel,
  disabled,
  label,
  tooltip,
  small,
  className,
  ...rest
}) => {
  const classes = useStyles();

  return (
    <div className={classNames(classes.textField, className)}>
      <label
        className={classNames(classes.label, disabled && classes.disabled)}
      >
        <b>{fatLabel}</b> {label}
        {tooltip ? <QuestionMark tooltip={tooltip} /> : ''}
        <textarea
          {...rest}
          className={classNames(classes.input, small && classes.small)}
          disabled={disabled}
        />
      </label>
    </div>
  );
};

TextArea.defaultProps = {
  fatLabel: '',
  label: '',
  tooltip: false,
  small: false,
  disabled: false,
  className: '',
};

TextArea.propTypes = {
  fatLabel: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.shape({
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    placement: PropTypes.string,
  }),
  small: PropTypes.bool,
  disabled: PropTypes.bool,
  className: PropTypes.string,
};

export default TextArea;
