import PropTypes from 'prop-types';
import styled from 'styled-components';
import { Dialog } from '@material-ui/core';
import SuccessMessage from './SuccessMessage';
import CloseIcon from './myTeam/CloseIcon';
import { isMobile } from 'react-device-detect';

const Wrapper = styled.div`
  position: relative;
  width: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  width: ${(props) => props.width};
  input {
    background-color: white;
    ::placeholder {
      color: grey;
      opacity: 1;
    }
  }
  button {
    width: fit-content;
    margin-left: 0.5rem;
  }
  max-width: 100%;
  overflow-x: hidden;
`;

const Headline = styled.div`
  color: black;
  margin-bottom: 2rem;
  font-size: 24px;
  font-weight: 700;
`;

const Description = styled.div`
  margin-bottom: 2rem;
  font-size: 16px;
  line-height: 24px;
  color: var(--default-text);
`;

const DialogWrapper = ({
  open,
  showSuccess,
  successMessage,
  onClose,
  description,
  children,
  headline,
  width,
}) => {
  return (
    <Dialog
      disableScrollLock
      maxWidth={false}
      scroll="paper"
      open={open}
      onClose={onClose}
    >
      <CloseIcon onClick={onClose} />
      {showSuccess ? (
        <SuccessMessage message={successMessage} />
      ) : (
        <Wrapper width={width}>
          <Headline>{headline}</Headline>
          {description && <Description>{description}</Description>}
          <div>{children}</div>
        </Wrapper>
      )}
    </Dialog>
  );
};
DialogWrapper.defaultProps = isMobile
  ? {}
  : {
      width: '550px',
    };

DialogWrapper.propTypes = {
  open: PropTypes.bool,
  showSuccess: PropTypes.bool,
  onClose: PropTypes.func,
  width: PropTypes.string,
  successMessage: PropTypes.string,
  description: PropTypes.string,
  headline: PropTypes.string,
};

export default DialogWrapper;
