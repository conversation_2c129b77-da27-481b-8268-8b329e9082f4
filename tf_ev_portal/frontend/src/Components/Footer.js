import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

import { Link } from 'react-router-dom';
import Center from './helper/Center';
import b2mobilityLogo from '../static/img/B2Mobility.png';
import { userContext } from '../ContextProvider';
import { getBranding } from '../utils/helper';

import HelpIcon from '@mui/icons-material/Help';

const Container = styled.div`
  position: fixed;
  bottom: 0;
  width: 100vw;
  z-index: 1000;
  background-color: var(--trafineo-grau-20);
`;

const Wrapper = styled.div`
  display: flex;
  padding: 0 1rem;
  box-sizing: border-box;
  width: 100%;
`;

const ImageWrapper = styled.div`
  display: flex;
  margin-left: auto;
  justify-content: center;
  align-items: center;
  img {
    height: 25px;
  }
`;

const List = styled.div`
  display: flex;
  height: 40px;
`;

const ListItem = styled(Link)`
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--default-text);
  height: 100%;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
  text-align: center;
  padding: 0 10px;
  text-decoration: none;
  :hover {
    cursor: pointer;
    text-decoration: none;
  }
  :first-child {
    padding: 0 10px 0 0;
  }
`;

const StyledFAQ = styled.a`
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--trafineo-rot-100);
  height: 100%;
  font-family: Arial, Helvetica, sans-serif;
  font-size: 12px;
  text-align: center;
  padding: 0 10px;
  text-decoration: none;
  :hover {
    cursor: pointer;
    text-decoration: none;
  }
  :first-child {
    padding: 0 10px 0 0;
  }
`;

const StyledHelpIcon = styled(HelpIcon)`
  color: var(--trafineo-rot-100);
  margin: auto;
  font-size: 18px !important;
  padding-right: 2px;
`;

const Footer = ({ isHomeChargingAustria }) => {
  const { user } = useContext(userContext);

  const loggedInNav = [
    'imprint',
    'data-privacy-statement',
    'terms-and-conditions',
    'cookies',
    'faq',
  ];
  const baseNav = ['imprint', 'data-privacy-statement', 'cookies', 'faq'];

  const { i18n } = useTranslation('home');

  const { t } = useTranslation('footer');
  return (
    <Container>
      <Center>
        <Wrapper>
          <List>
            {(user ? loggedInNav : baseNav).map((name) => {
              if (isHomeChargingAustria && name === 'imprint') {
                return (
                  <ListItem key={name} to={`/imprint-home-charging-austria`}>
                    {t(name)}
                  </ListItem>
                );
              }
              if (name === 'cookies') {
                return (
                  <ListItem
                    key={name}
                    to={`/cookie-settings`}
                    state={{ prevPathname: window.location.pathname }}
                  >
                    {t(name)}
                  </ListItem>
                );
              }
              if (name === 'faq') {
                return (
                  <>
                    {/* <StyledHelpIcon />
                    <StyledFAQ
                      target="blanc_"
                      key={name}
                      href={
                        getBranding() === 'trafineo'
                          ? `https://ev-helpcenter.trafineo.com/hc/${
                              i18n.language === 'en' ? 'en-gb' : i18n.language
                            }`
                          : getBranding() === 'aral'
                          ? `https://ev-helpcenter.aral-service.trafineo.com/hc/${
                              i18n.language === 'en' ? 'en-gb' : i18n.language
                            }`
                          : `https://ev-helpcenter.bp-service.trafineo.com/hc/${
                              i18n.language === 'en' ? 'en-gb' : i18n.language
                            }`
                      }
                    >
                      {t(name)}
                    </StyledFAQ> */}
                  </>
                );
              }
              if (!isHomeChargingAustria) {
                return (
                  <ListItem key={name} to={`/${name}`}>
                    {t(name)}
                  </ListItem>
                );
              }
              return <></>;
            })}
          </List>
          {getBranding() !== 'trafineo' && !isHomeChargingAustria && (
            <ImageWrapper>
              <img src={b2mobilityLogo} alt="b2Mobility-Logo" />
            </ImageWrapper>
          )}
        </Wrapper>
      </Center>
    </Container>
  );
};

export default Footer;
