import styled from 'styled-components';
import { downloadFile } from '../utils/helper';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { isMobile } from 'react-device-detect';
import PropTypes from 'prop-types';
import requester from '../utils/requester';

const FileName = styled.div`
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 12px;
  margin-top: 0.25rem;
  padding: 0 0.5rem;
  color: var(--default-text);
  text-align: center;
`;
const FileRow = styled.div`
  display: flex;
  min-height: 50px;
  flex-wrap: wrap;
  width: 100%;
`;
const FileWrapper = styled.div`
  position: relative;
  display: flex;
  margin-top: 0.5rem;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  margin-bottom: 0.25rem;
  padding: 0 0.25rem;
  box-sizing: border-box;
  width: ${isMobile ? '50%' : '25%'};
  svg {
    color: var(--default-text);
  }
  :hover {
    cursor: pointer;
    opacity: 0.8;
  }
`;

const FileDownload = ({ files, idpId }) => {
  const getFile = async (path) => {
    try {
      const rsp = await requester().get(
        '/Ev_Driver_Data_Approval/Contract_Upload',
        {
          params: {
            ev_driver_idp_id: idpId,
            blob_file_uri: path,
          },
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
      downloadFile(decodeURIComponent(path.substr(41)), rsp.data.tariff);
    } catch (error) {
      console.error(error);
    }
  };

  if (!files) {
    return <></>;
  }
  if (idpId) {
    return (
      <FileRow>
        {files.map((e) => {
          const name = e.substr(41);
          return (
            <FileWrapper onClick={() => getFile(e)} title={name} key={name}>
              <InsertDriveFileIcon fontSize="large" />
              <FileName>{decodeURIComponent(name)}</FileName>
            </FileWrapper>
          );
        })}
      </FileRow>
    );
  }
  return (
    <FileRow>
      {files.map((e) => (
        <FileWrapper
          onClick={() => downloadFile(e.name, e.data)}
          title={e.name}
          key={e.name}
        >
          <InsertDriveFileIcon fontSize="large" />
          <FileName>{e.name}</FileName>
        </FileWrapper>
      ))}
    </FileRow>
  );
};

FileDownload.propTypes = {
  files: PropTypes.array,
  idpId: PropTypes.string,
};
export default FileDownload;
