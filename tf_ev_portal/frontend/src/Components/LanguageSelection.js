import CountryFlag from './evDriver/CountryFlag';
import CircularProgress from './CircularProgress';
import Center from './helper/Center';
import { Placeholder } from './evDriver/PageTemplate';
import Select from './Select';
import { useTranslation } from 'react-i18next';
import { StyledMenuItem } from './evDriver/PageTemplate';
import { languages } from '../constants/localization';
import PropTypes from 'prop-types';

const LanguageSelection = ({ isLoading = false, language, changeLanguage }) => {
  const tLocalization = useTranslation('localization').t;
  const tServiceProvider = useTranslation('serviceProvider').t;

  return (
    <div style={{ width: '100%' }}>
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <>
          <Select
            big
            onChange={(e) => {
              changeLanguage(e.target.value);
            }}
            value={language}
            displayEmpty
            renderValue={
              language
                ? undefined
                : () => (
                    <Placeholder>
                      {tServiceProvider('inviteLanguagePlaceholder')}
                    </Placeholder>
                  )
            }
          >
            {Object.keys(languages).map((entry) => (
              <StyledMenuItem
                key={entry}
                value={entry}
                data-cy={`${entry}Select`}
              >
                <CountryFlag country={entry} />
                {tLocalization(`${entry}Language`)}
              </StyledMenuItem>
            ))}
          </Select>
        </>
      )}
    </div>
  );
};

LanguageSelection.propTypes = {
  isLoading: PropTypes.bool,
  language: PropTypes.string,
  changeLanguage: PropTypes.func,
};

export default LanguageSelection;
