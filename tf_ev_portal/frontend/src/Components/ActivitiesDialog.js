import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import DialogWrapper from './DialogWrapper';
import Activities from './Activities';

const ActivitiesDialog = ({ open, driverData, onClose }) => {
  const { t } = useTranslation('profile');

  return (
    <DialogWrapper
      width="1200px"
      open={open}
      onClose={onClose}
      headline={
        driverData
          ? t('activitiesForDriver', {
              email: driverData?.invitation_sent_to,
            })
          : t('myActivities')
      }
    >
      {open && <Activities driverData={driverData} />}
    </DialogWrapper>
  );
};

ActivitiesDialog.propTypes = {
  open: PropTypes.bool,
  // eslint-disable-next-line react/forbid-prop-types
  driverData: PropTypes.object,
  onClose: PropTypes.func,
};

export default ActivitiesDialog;
