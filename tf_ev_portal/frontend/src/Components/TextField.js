import { forwardRef } from 'react';
import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import classNames from 'classnames';
import QuestionMark from './QuestionMark';
import searchIcon from '../static/img/search.svg';
import { isMobile } from 'react-device-detect';
import CircleIcon from '@mui/icons-material/Circle';
import ErrorIcon from '@mui/icons-material/Error';

const TextField = forwardRef(
  (
    {
      fatLabel,
      disabled,
      label,
      tooltip,
      error,
      small,
      className,
      search,
      newDriver,
      noDisplay,
      disableMinWidth,
      fullWidth,
      tiny,
      icon,
      updateIcon,
      ...rest
    },
    ref,
  ) => {
    const useStyles = makeStyles({
      textField: {
        minWidth: !search ? (isMobile ? '0' : noDisplay ? '0' : '200px') : '0',
        textAlign: 'left',
        position: 'relative',
        margin: search ? '0' : '0.5rem 0',
      },
      noMinWidth: {
        minWidth: '0',
      },
      input: {
        display: noDisplay ? 'none' : '',
        width: 'calc(100% - 2rem)',
        marginTop: 12,
        borderRadius: 10,
        minHeight: 32,
        padding: '0 1rem',
        lineHeight: '22px',
        border: 'none',
        boxShadow: '0 0 6px 0 rgba(0, 0, 0, 0.31)',
        opacity: 0.7,
        backgroundColor: '#ffffff',
        caretColor: '#666',
        fontSize: '19px',
        height: 46,
        outline: 'none',
        '&:disabled': {
          backgroundColor: 'var(--trafineo-grau-50)',
        },
      },
      small: {
        height: 30,
        lineHeight: 1.17,
        fontWeight: 'bold',
        fontSize: '12px',
        margin: '0',
      },
      tiny: {
        marginTop: 6,
        padding: '0 0.5rem',
        height: '25px',
        minHeight: '25px',
        borderRadius: '4px',
        fontSize: '14px',
        border: 'solid 1px #707070',
        minWidth: '0 !important',
      },
      noDisplay: {
        width: 0,
        height: 0,
        border: 'none',
        boxShadow: 'none',
        opacity: 0.1,
        backgroundColor: 'transparent',
        caretColor: 'transparent',
        outline: 'none',
        '&:disabled': {
          backgroundColor: 'transparent',
        },
        '&:focus': {
          border: 'none',
        },
        '&:hover': {
          border: 'none',
        },
      },
      newDriver: {
        fontFamily: 'var(--font-family)',
        width: '100%',
        boxSizing: 'border-box',
        marginTop: 0,
        minHeight: 32,
        lineHeight: '22px',
        border: '1px solid #EBEBEB',
        opacity: 0.7,
        backgroundColor: '#F7F7F7',
        borderRadius: 0,
        caretColor: '#666',
        boxShadow: 'none',
        fontSize: isMobile ? '16px' : '14px',
        height: 46,
        padding: '0 0.5rem',
        outline: 'none',
        '&:disabled': {
          backgroundColor: 'var(--trafineo-grau-50)',
        },
        '&:focus': {
          border: 'solid 1px var(--default-text)',
        },
        '&:hover': {
          border: 'solid 1px var(--default-text)',
        },
      },
      label: {
        fontSize: '19px',
        fontWeight: 'normal',
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: 1.16,
        letterSpacing: '0.34px',
        textAlign: 'left',
        color: '#414042',
      },
      icon: {
        width: '0.7em !important',
        height: '0.7em !important',
        color: 'var(--trafineo-rot-100) !important',
      },
      updateIcon: {
        width: '0.7em !important',
        height: '0.7em !important',
        color: '#ff7e00 !important',
      },
      labelWrapper: {
        display: 'flex',
        alignItems: 'center',
        marginBottom: noDisplay ? 0 : '0.5rem',
      },
      fullWidth: {
        width: '100%',
      },
      tinyLabel: {
        fontSize: '14px',
        fontWeight: 'normal',
        fontStretch: 'normal',
        fontStyle: 'normal',
        lineHeight: 1.16,
        letterSpacing: '0',
        textAlign: 'left',
        color: '#414042',
      },
      newDriverLabel: {
        marginBottom: '0.5rem',
        textAlign: 'left',
        fontSize: '14px',
        lineHeight: '21px',
        fontWeight: 400,
        color: 'var(--default-text)',
      },
      noDisplayLabel: {
        marginBottom: '0rem',
        textAlign: 'left',
        fontSize: '14px',
        lineHeight: '21px',
        fontWeight: 400,
        color: 'var(--default-text)',
      },
      error: {
        border: '1px solid var(--error-color) !important',
        color: 'var(--error-color) !important',
      },
      errorLabel: {
        color: 'var(--error-color)',
      },
      search: {
        margin: 0,
        height: '32px',
        minHeight: '32px',
        border: 'solid 0.5px #707070',
        borderRadius: 0,
        fontSize: '14px',
        fontFamily: 'var(--font-family)',
        color: '#707070 !impoartant',
        minWidth: '0 !important',
        width: 'calc(100% - 35px)',
        padding: '0 0 0 34px',
        boxShadow: 'none',
      },
      searchIcon: {
        height: '15px',
        position: 'absolute',
        top: '10px',
        left: '12px',
      },
      disabled: {
        color: 'rgba(65, 64, 66, 0.55)',
      },
      noMargin: {
        margin: '0',
      },
    });
    const classes = useStyles();

    return (
      <div
        className={classNames(
          classes.textField,
          className,
          (tiny || disableMinWidth) && classes.noMinWidth,
          noDisplay && classes.noMargin,
          newDriver && classes.noMargin,
          fullWidth && classes.fullWidth,
        )}
      >
        {(search && (
          <>
            <input
              ref={ref}
              {...rest}
              className={classNames(
                classes.input,
                small && classes.small,
                tiny && classes.tiny,
                error && classes.error,
                search && classes.search,
              )}
              disabled={disabled}
            />
            <img src={searchIcon} alt="search" className={classes.searchIcon} />
          </>
        )) || (
          <label
            className={classNames(
              classes.label,
              disabled && classes.disabled,
              tiny && classes.tinyLabel,
              newDriver && classes.newDriverLabel,
              noDisplay && classes.noDisplayLabel,
              error && classes.errorLabel,
            )}
          >
            <div className={classes.labelWrapper}>
              <b>{fatLabel}</b> {label}
              {icon && <CircleIcon className={classes.icon} />}
              {updateIcon && <ErrorIcon className={classes.updateIcon} />}
              {tooltip ? (
                <QuestionMark
                  mobile={isMobile}
                  small={newDriver || noDisplay}
                  tooltip={tooltip}
                />
              ) : (
                ''
              )}
            </div>
            <input
              data-cy="dashboardSearchInput"
              ref={ref}
              {...rest}
              className={classNames(
                classes.input,
                small && classes.small,
                newDriver && classes.newDriver,
                tiny && classes.tiny,
                error && classes.error,
              )}
              disabled={disabled}
            />
          </label>
        )}
      </div>
    );
  },
);

TextField.defaultProps = {
  fatLabel: '',
  label: '',
  tooltip: null,
  small: false,
  newDriver: false,
  tiny: false,
  disableMinWidth: false,
  disabled: false,
  error: false,
  search: false,
  fullWidth: false,
  className: '',
};

TextField.propTypes = {
  fatLabel: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.shape({
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    placement: PropTypes.string,
  }),
  small: PropTypes.bool,
  tiny: PropTypes.bool,
  newDriver: PropTypes.bool,
  disableMinWidth: PropTypes.bool,
  fullWidth: PropTypes.bool,
  disabled: PropTypes.bool,
  error: PropTypes.bool,
  search: PropTypes.bool,
  className: PropTypes.string,
};

export default TextField;
