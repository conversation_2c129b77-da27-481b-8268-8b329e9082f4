import { forwardRef } from 'react';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import Radio from '@material-ui/core/Radio';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import {
  Row,
  InputWrapper,
  Hint,
  Error,
  Description,
  Col,
  Flex,
  RowUpload,
} from '../../Components/evDriver/PageTemplate';
import CurrencyField from './CurrencyField';
import TimeField from './TimeField';
import { isMobile } from 'react-device-detect';
import Checkbox from '../Checkbox';
import styled from 'styled-components';

const CheckboxWrapper = styled.div`
  margin-top: 8px;
  display: flex;
  width: 100%;
  margin-bottom: 5px;
`;

const CheckboxText = styled.div`
  display: inline-flex;
  font-size: 14px;
  padding-left: 5px;
  line-height: 15px;
  color: ${(props) =>
    props.error ? 'var(--error-color)' : 'var(--default-text)'};
`;

/**
 * TariffField consists of the work price and the date input fields.
 */

const useStyles = makeStyles({
  root: {
    '&.Mui-checked': {
      color: 'var(--trafineo-rot-100)',
      '&:hover': {
        backgroundColor: 'transparent',
      },
    },
    '&:hover': {
      backgroundColor: 'transparent',
    },
  },
  radio: {
    display: 'flex',
    flexDirection: isMobile ? 'column' : 'row',
    justifyContent: 'center',
    alignItems: isMobile ? 'flex-start' : 'center',
    marginLeft: isMobile ? '22px' : 0,
  },
  label: {
    color: 'var(--default-text)',
    '& .MuiTypography-root': {
      fontSize: '14px !important',
    },
  },
});

const TariffField = forwardRef(
  (
    {
      priceValue,
      offPeakPriceValue,
      onPriceChange,
      onOffPeakPriceChange,
      priceLabel,
      sameAsWeekdays,
      setSameAsWeekdays,
      viewMode,
      offPeakFrom,
      onOffPeakFromChange,
      offPeakTo,
      onOffPeakToChange,
      priceValid,
      offPeakPriceValid,
      offPeakTimespanValid,
      onFocusPrice,
      onFocusOffPeakPrice,
      onFocusOffPeakTimespanValid,
      offPeakIsHigherAccepted,
      setOffPeakIsHigherAccepted,
    },
    ref,
  ) => {
    const classes = useStyles();
    const { t } = useTranslation('evDriver');

    const styledRadioButton = (
      <Radio
        classes={{
          root: classes.root,
        }}
      />
    );

    return (
      (viewMode === 'ONETARIFF' && (
        <>
          <Row>
            <InputWrapper>
              <Description error={!priceValid}>{t('tariffNormal')}</Description>
              <CurrencyField
                autoFocus
                value={priceValue}
                onChange={onPriceChange}
                error={!priceValid}
                onFocus={onFocusPrice}
              />
              {(!priceValid && (
                <Error data-cy="priceValidationError">
                  {t('priceValidationError')}
                </Error>
              )) || <Hint>{priceLabel}</Hint>}
            </InputWrapper>
          </Row>
        </>
      )) || (
        <>
          <Row>
            <InputWrapper>
              <Description error={!priceValid}>{t('tariffNormal')}</Description>
              <CurrencyField
                autoFocus
                value={priceValue}
                onChange={onPriceChange}
                error={!priceValid}
                onFocus={onFocusPrice}
              />
              {(!priceValid && <Error>{t('priceValidationError')}</Error>) || (
                <Hint>{priceLabel}</Hint>
              )}
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper>
              <Description error={offPeakPriceValid !== 3}>
                {t('tarriffOffPeak')}
              </Description>
              <CurrencyField
                value={offPeakPriceValue}
                onChange={onOffPeakPriceChange}
                error={offPeakPriceValid !== 3}
                onFocus={onFocusOffPeakPrice}
              />
              {Number(offPeakPriceValue) > Number(priceValue) && (
                <CheckboxWrapper>
                  <Checkbox
                    checked={offPeakIsHigherAccepted}
                    boxSize="small"
                    onClick={() =>
                      setOffPeakIsHigherAccepted(!offPeakIsHigherAccepted)
                    }
                  ></Checkbox>
                  <CheckboxText error={offPeakPriceValid === 4}>
                    {t('tarifOffPeakValidityCheck')}
                  </CheckboxText>
                </CheckboxWrapper>
              )}
              {(offPeakPriceValid !== 3 && (
                <>
                  <Error
                    data-cy={
                      offPeakPriceValid === 1
                        ? 'tariffErrorOffPeakPriceTooLow'
                        : 'priceValidationError'
                    }
                  >
                    {t(
                      offPeakPriceValid === 1
                        ? 'tariffErrorOffPeakPriceTooLow'
                        : 'priceValidationError',
                    )}
                  </Error>
                </>
              )) || <Hint>{priceLabel}</Hint>}
            </InputWrapper>
          </Row>
          <RowUpload>
            <Flex>
              <Col margin="40px">
                <Description horizontal>{t('weekdays')}</Description>
              </Col>
              <Col>
                <Description error={!offPeakTimespanValid}>
                  {t('tariffStartTime')}
                </Description>
                <TimeField
                  onFocus={onFocusOffPeakTimespanValid}
                  error={!offPeakTimespanValid}
                  value={offPeakFrom}
                  onChange={onOffPeakFromChange}
                />
              </Col>
              <Col>
                <Description error={!offPeakTimespanValid}>
                  {t('tariffEndTime')}
                </Description>
                <TimeField
                  onFocus={onFocusOffPeakTimespanValid}
                  error={!offPeakTimespanValid}
                  value={offPeakTo}
                  onChange={onOffPeakToChange}
                />
              </Col>
            </Flex>
            {!offPeakTimespanValid && (
              <Error data-cy="tariffErrorOffPeakTimeSpan">
                {t('tariffErrorOffPeakTimeSpan')}
              </Error>
            )}
          </RowUpload>

          <Row>
            <Col isMobile={isMobile} margin="13px">
              <Description horizontal>{t('weekend')}</Description>
            </Col>
            <Col isMobile={isMobile}>
              <RadioGroup
                className={classes.radio}
                value={sameAsWeekdays}
                onChange={(event) => {
                  setSameAsWeekdays(event.target.value === 'true');
                }}
              >
                <FormControlLabel
                  value
                  classes={{
                    root: classes.label,
                  }}
                  control={styledRadioButton}
                  label={t('tariffLikeWeekdays')}
                />
                <FormControlLabel
                  value={false}
                  classes={{
                    root: classes.label,
                  }}
                  control={styledRadioButton}
                  label={t('tariffAllDay')}
                />
              </RadioGroup>
            </Col>
          </Row>
        </>
      )
    );
  },
);

TariffField.defaultProps = {
  fatLabel: '',
  label: '',
  tooltip: {},
  dateTooltip: {},
  disabled: false,
  priceValue: '',
  onPriceChange: () => {},
  priceLabel: '',
  dateLabel: '',
  dateValue: '',
  onDateChange: () => {},
  className: '',
  minDate: null,
};

TariffField.propTypes = {
  fatLabel: PropTypes.string,
  label: PropTypes.string,
  tooltip: PropTypes.shape({
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    placement: PropTypes.string,
  }),
  dateTooltip: PropTypes.shape({
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    placement: PropTypes.string,
  }),
  disabled: PropTypes.bool,
  sameAsWeekdays: PropTypes.bool,
  setSameAsWeekdays: PropTypes.func,
  priceValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  offPeakPriceValue: PropTypes.oneOfType([PropTypes.string, PropTypes.number]),
  onPriceChange: PropTypes.func,
  onOffPeakPriceChange: PropTypes.func,
  priceLabel: PropTypes.string,
  dateLabel: PropTypes.string,
  dateValue: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.instanceOf(Date),
  ]),
  onDateChange: PropTypes.func,
  className: PropTypes.string,
  viewMode: PropTypes.string,
  minDate: PropTypes.instanceOf(Date),
  offPeakFrom: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.instanceOf(Date),
  ]),
  onOffPeakFromChange: PropTypes.func,
  offPeakTo: PropTypes.oneOfType([
    PropTypes.string,
    PropTypes.instanceOf(Date),
  ]),
  onOffPeakToChange: PropTypes.func,
};

export default TariffField;
