import { useContext } from 'react';
import styled from 'styled-components';
import CheckIcon from '@material-ui/icons/Check';
import { useTranslation } from 'react-i18next';
import { QuestionMark } from '../../Components';
import CircularProgress from '../CircularProgress';
import { evDriverContext } from '../../ContextProvider';
import KeyboardArrowLeft from '@material-ui/icons/KeyboardArrowLeft';
import KeyboardArrowRight from '@material-ui/icons/KeyboardArrowRight';
import EvStationIcon from '@material-ui/icons/EvStation';
import AccountBalanceIcon from '@material-ui/icons/AccountBalance';
import CallMergeIcon from '@material-ui/icons/CallMerge';
import DriveEtaIcon from '@material-ui/icons/DriveEta';
import OfflineBoltIcon from '@material-ui/icons/OfflineBolt';
import { isMobile } from 'react-device-detect';
import { useNavigate } from 'react-router-dom';

import Button from '../Button';
import { MenuItem } from '@material-ui/core';
import PageWrapper from '../PageWrapper';

export const StaticValue = styled.div`
  font-size: 14px;
  ${(props) => (props.red === true ? 'color: var(--error-color)' : '')};
  opacity: 0.5;
  margin-bottom: 1.5rem;
`;

export const TariffTypeSelectionContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

export const TariffIcon = styled.img`
  display: flex;
  height: 16px;
  margin-right: 0.5rem;
`;

export const TariffTypeSelectionOption = styled.button`
  display: flex;
  outline: none;
  border: 1px solid gray;
  padding: 1rem;
  border-radius: 10px;
  margin-bottom: 0.5rem;
  border: 1px solid #ebebeb;
  border-color: ${(props) =>
    props.active === true ? 'var(--trafineo-rot-100) !important' : '#ebebeb'};

  opacity: 0.7;
  background: white;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  align-items: center;
  color: black;
  :hover {
    border-color: grey;
  }
  :focus-visible {
    border-color: grey;
  }
  :focus-visible {
    border-color: grey;
    outline: none;
  }
`;

export const Col = styled.div`
  margin-top: ${(props) => props.margin};
  display: flex;
  justify-content: flex-start;
  display: flex;
  align-items: center;
  flex-direction: column;
  margin-right: ${(props) => (props.isMobile === true ? '0' : '1rem')};
`;
export const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

export const TextArea = styled.textarea`
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 20px;
  padding: 0.5rem;
`;

export const Flex = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
`;

export const Placeholder = styled.div`
  color: grey;
`;

export const Loader = styled.div`
  padding: 0 2rem;
  display: flex;
`;

export const Description = styled.div`
  text-align: left;
  font-size: 14px;
  line-height: 21px;
  margin-bottom: ${(props) => (props.horizontal ? 0 : '0.25rem')};
  font-weight: ${(props) => (props.bold ? '700' : '400')};
  display: flex;
  align-items: center;
  color: var(--default-text);
  ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
`;

export const Hint = styled.div`
  color: var(--trafineo-grau-100);
  text-align: left;
  font-size: 11px;
  padding-top: 0.25rem;
`;

export const Error = styled.div`
  color: var(--error-color);
  text-align: left;
  font-size: 14px;
  padding-top: 0.25rem;
`;

export const InputWrapper = styled.div`
  width: ${(props) =>
    props.isMobile === true || props.fullWidth ? '100%' : '320px'};
  display: flex;
  flex-direction: column;
`;

export const Row = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 2rem;
`;

export const RowUpload = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  margin-bottom: 2rem;
  flex-direction: column;
`;
export const InformationContainer = styled.div`
  padding: 1rem;
  background-color: #f5f6f8;
  display: flex;
  margin-top: 1rem;
`;

export const InformationIcon = styled.div`
  margin-right: 0.5rem;
  margin-top: 3px;
  img {
    width: 15px;
    height: 15px;
  }
`;
export const InformationText = styled.div`
  text-align: left;
  font-size: 14px;
  font-weight: 400;
  line-height: 21px;
  color: var(--default-text);
`;
export const TooltipTextWrapper = styled.div`
  display: inline-block;
  font-size: 14px;
`;
export const TooltipContainer = styled.div`
  display: flex;
  white-space: pre-wrap;
`;

export const Headline = styled.h1`
  font-size: ${(props) => (props.isMobile === true ? '20px' : '25px')};
  font-weight: bold;
  width: 100%;
  margin: ${(props) => (props.isMobile === true ? '1rem 0' : '2rem 0')};
  ${(props) => props.isMobile && 'padding: 0 1rem'};
  color: black;
  text-align: ${(props) => (props.isMobile === true ? 'center' : 'left')};
`;

export const HeaderDescription = styled.div`
  text-align: ${(props) => (props.isMobile === true ? 'center' : 'left')};
  font-size: 16px;
  line-height: 24px;
  color: var(--default-text);
  font-weight: 400;
  margin-bottom: 2rem;
  width: 100%;
`;

export const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  width: 100%;
  button {
    width: ${(props) => (props.isMobile === true ? '100%' : 'fit-content')};
  }
`;

const Wrapper = styled.div`
  max-width: 1500px;
  margin: 0 auto;
  z-index: 5;
  background: transparent;
`;

const Page = styled.div`
  display: flex;
  min-height: var(--app-height-driver);
  ${(props) => (props.isMobile === true ? 'flex-direction: column' : '')};
`;

const SideNavContainer = styled.div`
  width: ${(props) => (props.isMobile === true ? '100%' : '30%')};
  height: auto;
  display: flex;
  background: ${(props) =>
    props.isMobile === true ? 'var(--menu-background)' : 'transparent'};
  flex-direction: ${(props) => (props.isMobile === true ? 'row' : 'column')};
  align-items: center;
  ${(props) => (props.isMobile === true ? '' : 'padding: 1.25rem 1rem;')};
  position: relative;
`;

const NavLine = styled.svg`
  z-index: 0;
  position: absolute;
  left: 30.5px;
  top: 60px;
`;

const NavlineMobile = styled.svg`
  z-index: 0;
  position: absolute;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  top: 30px;
`;

const MobileCardNumber = styled.div`
  color: white;
  font-size: 14px;
  position: absolute;
  left: 0;
  right: 0;
  margin-left: auto;
  margin-right: auto;
  bottom: 10px;
`;

const ContentWrapper = styled.div`
  width: ${(props) => (props.onBoardingMode === true ? '70%' : '100%')};
  display: flex;

  justify-content: ${(props) =>
    props.onBoardingMode ? 'flex-start' : 'center'};
`;
const Content = styled.div`
  background: white;
  min-height: 575px;
  max-width: 625px;
  display: flex;
  justify-content: start;
  padding: ${(props) => (props.isMobile === true ? '1rem' : '0 2rem 0 4rem')};
  flex-direction: column;
`;

const TemplateRow = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
`;

const BottomWrapper = styled.div`
  display: flex;
  margin-bottom: 2rem;
`;

const Children = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
`;

const ButtonContainer = styled.div`
  width: 100%;
  display: flex;
  margin: 2rem 0;
  justify-content: flex-end;
`;

const BackIcon = styled(KeyboardArrowLeft)`
  font-size: 18px;
`;

const NextIcon = styled(KeyboardArrowRight)`
  font-size: 18px;
`;

const Background = styled.div`
  background: ${(props) =>
    props.onBoardingMode === true ? 'var(--trafineo-rot-100)' : 'white'};
  background: ${(props) =>
    props.onBoardingMode === true ? 'var(--backgroud-gradient)' : 'white'};
`;

const NavItem = styled.div`
  width: 100%;
  display: flex;
  margin: 1rem 0;
  justify-content: flex-start;
  cursor: ${(props) => (props.complete ? 'pointer' : 'inherit')};
  flex-direction: ${(props) => (props.isMobile === true ? 'column' : 'row')};
  height: ${(props) => (props.isMobile === true ? '50px' : 'auto')};
  align-items: center;
`;

const NavItemNumber = styled.div`
  width: 30px;
  height: 30px;
  display: flex;
  background: ${(props) =>
    props.active || props.complete ? 'white' : 'var(--trafineo-grau-20)'};
  color: ${(props) =>
    props.active ? 'var(--trafineo-rot-100)' : 'var(--trafineo-grau-100)'};
  border-radius: 25px;
  justify-content: center;
  align-items: center;
  font-weight: 400;
  margin-right: ${(props) => (props.isMobile === true ? '0' : '0.5rem')};
  z-index: 1;
  position: relative;
  svg {
    color: ${(props) =>
      props.active || props.complete
        ? 'var(--trafineo-rot-100)'
        : 'var(--default-text)'};
    position: absolute;
    top: 3px;
    left: 5px;
    width: 20px;
  }
`;

export const FadeIn = styled.div`
  animation-name: 'FadeIn';
  animation-duration: var(--fadein-duration);
  transition-timing-function: linear;
`;

const NavItemText = styled.div`
  color: white;
  font-weight: 400;
  ${(props) =>
    props.isMobile === true ? 'font-size: 12px;margin-top:0.5rem' : ''};
  text-align: ${(props) => (props.isMobile === true ? 'center' : 'left')};
`;

const PageTemplate = ({
  children,
  headline,
  description,
  next,
  onNext,
  onPrevious,
  currentPage,
  cardNumber,
  onBoardingMode,
  tooltip,
  nextText,
  isLoading,
  hideButtons,
}) => {
  const { t } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;
  const { evDriverData } = useContext(evDriverContext);
  const navigate = useNavigate();

  const availableTariffSchemes = evDriverData?.availableTariffSchemes;
  const isAtDriver = evDriverData?.country?.new === 'at';

  const canSelectTariffType =
    availableTariffSchemes?.filter((e) => e.tariff_sub_type === 'TWOTARIFF')
      .length > 0 || isAtDriver;
  const adjustedCurrentPage =
    !canSelectTariffType && currentPage > 1 ? currentPage - 1 : currentPage;
  const SideNav = () => {
    const NavItems = () => {
      const pages = canSelectTariffType
        ? [
            {
              title: t('OnboardingStep1'),
              icon: <EvStationIcon />,
              link: '/',
            },
            {
              title: t('OnboardingStep2'),
              icon: <CallMergeIcon />,
              link: '/tariffselection',
            },
            {
              title: t('OnboardingStep3'),
              icon: <OfflineBoltIcon />,
              link: '/electricityTariff',
            },
            {
              title: t('OnboardingStep5'),
              icon: <AccountBalanceIcon />,
              link: '/bankData',
            },
            {
              title: t('OnboardingStep7'),
              icon: <DriveEtaIcon />,
              link: '/vehicle',
            },
            {
              title: t('OnboardingStep6'),
              icon: <CheckIcon />,
              link: '/summary',
            },
          ]
        : [
            {
              title: t('OnboardingStep1'),
              icon: <EvStationIcon />,
              link: '/',
            },
            {
              title: t('OnboardingStep3'),
              icon: <OfflineBoltIcon />,
              link: '/electricityTariff',
            },
            {
              title: t('OnboardingStep5'),
              icon: <AccountBalanceIcon />,
              link: '/bankData',
            },
            {
              title: t('OnboardingStep7'),
              icon: <DriveEtaIcon />,
              link: '/vehicle',
            },
            {
              title: t('OnboardingStep6'),
              icon: <CheckIcon />,
              link: '/summary',
            },
          ];
      const Items = pages.map((page, i) => {
        const pageNumber = i + 1;
        return (
          <NavItem
            key={page.title}
            complete={adjustedCurrentPage > pageNumber}
            isMobile={isMobile}
            data-cy={`onboardingStep${i}`}
            onClick={
              adjustedCurrentPage > pageNumber
                ? () => {
                    navigate(page.link);
                  }
                : null
            }
          >
            <NavItemNumber
              active={adjustedCurrentPage === pageNumber}
              complete={adjustedCurrentPage > pageNumber}
              isMobile={isMobile}
            >
              {isMobile ? (
                page.icon
              ) : adjustedCurrentPage <= pageNumber ? (
                pageNumber
              ) : (
                <CheckIcon />
              )}
            </NavItemNumber>
            {!isMobile && <NavItemText>{page.title}</NavItemText>}
          </NavItem>
        );
      });
      return Items;
    };

    return (
      <SideNavContainer isMobile={isMobile}>
        {isMobile ? (
          <>
            <NavlineMobile width="80%" height="2">
              <line
                x1="0"
                y1="0"
                x2="100%"
                y2="0"
                stroke="white"
                strokeWidth="2"
              />
            </NavlineMobile>
            <MobileCardNumber>{t('OnboardingCardnumber')}</MobileCardNumber>
          </>
        ) : (
          <>
            <NavLine width="2" height={canSelectTariffType ? '350' : '290'}>
              <line
                x1="0"
                y1="50"
                x2="0"
                y2={canSelectTariffType ? '350' : '290'}
                stroke="white"
                strokeWidth="2"
              />
            </NavLine>
            <NavItem>
              <NavItemText>{t('OnboardingCardnumber')}</NavItemText>
            </NavItem>
          </>
        )}
        <NavItems />
      </SideNavContainer>
    );
  };
  if (onBoardingMode) {
    return (
      <Background onBoardingMode={!isMobile && onBoardingMode}>
        <Wrapper>
          <Page isMobile={isMobile}>
            {onBoardingMode && <SideNav />}
            <ContentWrapper onBoardingMode={!isMobile && onBoardingMode}>
              <Content isMobile={isMobile}>
                <div>
                  <TemplateRow>
                    <Headline isMobile={isMobile}>{headline}</Headline>
                  </TemplateRow>
                  {description && (
                    <TemplateRow>
                      <HeaderDescription isMobile={isMobile}>
                        {description}
                        {tooltip && (
                          <TooltipTextWrapper>
                            <QuestionMark tooltip={tooltip} small>
                              ?
                            </QuestionMark>
                          </TooltipTextWrapper>
                        )}
                      </HeaderDescription>
                    </TemplateRow>
                  )}
                </div>
                <Children>{children}</Children>
                <BottomWrapper>
                  <TemplateRow>
                    <ButtonContainer>
                      {!isLoading && onPrevious && (
                        <Button
                          onClick={onPrevious}
                          isMobile={isMobile}
                          data-cy="prev"
                          variant="prev"
                        >
                          <BackIcon />
                          {tActions('prev')}
                        </Button>
                      )}
                      {(isLoading && (
                        <Loader>
                          <CircularProgress />
                        </Loader>
                      )) || (
                        <Button
                          onClick={onNext}
                          isMobile={isMobile}
                          disabled={!next}
                          variant="next"
                          data-cy="next"
                        >
                          {nextText ? nextText : tActions('next')}
                          <NextIcon />
                        </Button>
                      )}
                    </ButtonContainer>
                  </TemplateRow>
                </BottomWrapper>
              </Content>
            </ContentWrapper>
          </Page>
        </Wrapper>
      </Background>
    );
  }
  return (
    <PageWrapper
      maxWidth="625px"
      title={headline}
      isMobile={isMobile}
      description={
        description && (
          <>
            {description}
            {tooltip && (
              <TooltipTextWrapper>
                <QuestionMark tooltip={tooltip} small>
                  ?
                </QuestionMark>
              </TooltipTextWrapper>
            )}
          </>
        )
      }
    >
      <Content style={{ padding: 0 }}>
        <Children>{children}</Children>
        {!hideButtons && (
          <BottomWrapper>
            <TemplateRow>
              <ButtonContainer>
                {!isLoading && onPrevious && (
                  <Button
                    onClick={onPrevious}
                    isMobile={isMobile}
                    variant="prev"
                  >
                    <BackIcon />
                    {tActions('prev')}
                  </Button>
                )}
                {(isLoading && (
                  <Loader>
                    <CircularProgress />
                  </Loader>
                )) || (
                  <Button
                    onClick={onNext}
                    isMobile={isMobile}
                    disabled={!next}
                    variant="next"
                  >
                    {nextText ? nextText : tActions('next')}
                    <NextIcon />
                  </Button>
                )}
              </ButtonContainer>
            </TemplateRow>
          </BottomWrapper>
        )}
      </Content>
    </PageWrapper>
  );
};

export default PageTemplate;
