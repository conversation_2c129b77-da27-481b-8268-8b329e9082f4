import { forwardRef } from 'react';
import styled from 'styled-components';
import { Description } from '../../Components/evDriver/PageTemplate';
import { isMobile } from 'react-device-detect';
const CurrencyWrapper = styled.div`
  display: flex;
  align-items: center;
  input {
    width: 100px;
    border: 1px solid;
    border-color: ${(props) =>
      props.error === true ? 'var(--error-color)' : '#ebebeb'};
    ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
    height: 46px;
    opacity: 0.7;
    outline: none;
    padding: 0 0.5rem;
    font-size: ${isMobile ? '16px' : '14px'};
    box-shadow: none;
    box-sizing: border-box;
    min-height: 32px;
    line-height: 22px;
    border-radius: 0;
    background-color: #f7f7f7;
    margin-right: 0.5rem;
    :focus {
      border: solid 1px var(--default-text);
    }
  }
`;
const CurrencyField = forwardRef(({ isCO2 = false, error, ...rest }, ref) => {
  return (
    <CurrencyWrapper error={error}>
      <input
        data-cy={isCO2 ? 'co2Input' : 'currencyInput'}
        ref={ref}
        {...rest}
        placeholder={isCO2 ? '0,01' : '0,000000'}
        type={
          navigator.userAgent.indexOf('Trident') !== -1 ||
          navigator.userAgent.indexOf('Edge') !== -1
            ? 'text'
            : 'number'
        }
        onWheel={(e) => e.target.blur()}
        step={isCO2 ? '0.01' : '0.000001'}
      />
      <Description horizontal>{isCO2 ? 'g/kWh' : 'EUR/kWh'}</Description>
    </CurrencyWrapper>
  );
});

export default CurrencyField;
