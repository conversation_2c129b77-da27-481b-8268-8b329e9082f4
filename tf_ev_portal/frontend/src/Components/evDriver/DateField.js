import { useEffect, useState } from 'react';
import { Box } from '@material-ui/core';
import PropTypes from 'prop-types';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';
import { DesktopDatePicker, MobileDatePicker } from '@mui/x-date-pickers';
import { DialogActions, TextField } from '@mui/material';
import Button from '../Button';
import styled from 'styled-components';

const StyledInput = styled.input`
  font-family: var(--font-family);
  width: 200px;
  border: 1px solid;
  height: 46px;
  border-color: ${(props) => (props.error ? 'var(--error-color)' : '#ebebeb')};
  opacity: 0.7;
  outline: none;
  padding: 0 0.5rem;
  font-size: ${(props) => (props.isMobile ? '16px' : '14px')};
  box-shadow: none;
  box-sizing: border-box;
  line-height: 22px;
  border-radius: 0;
  background-color: #f7f7f7;
  :focus {
    border: solid 1px var(--default-text);
  }
`;

const MobileInput = styled(TextField)`
  .MuiOutlinedInput-root {
    border-radius: 0;
    background-color: #f7f7f7;
    padding-right: 20px;
  }
  .Mui-error fieldset {
    border: 1px solid var(--error-color) !important;
  }
  .Mui-error input {
    color: var(--error-color) !important;
  }
  button {
    padding: 0;
  }
  .Mui-focused fieldset {
    border: solid 1px var(--default-text) !important;
  }
  input {
    font-family: var(--font-family);
    border: 1px solid;
    height: 46px;
    border-color: transparent;
    opacity: 0.7;
    outline: none;
    padding: 0 0.5rem;
    font-size: 16px;
    box-shadow: none;
    box-sizing: border-box;
    line-height: 22px;
    border-radius: 0;
    background-color: #f7f7f7;
  }
`;

/**
 * DateField wraps the MUI date picker.
 */

const DateField = ({ error, value, onChange, ...rest }) => {
  //workaround for refilling the datefield after closing datepicker, after value was previously deleted
  useEffect(() => {
    setDate(value);
  }, [value]);

  const [dialogOpen, setDialogOpen] = useState(false);
  const [date, setDate] = useState(null);
  const { t } = useTranslation('evDriver');

  if (!isMobile) {
    return (
      <DesktopDatePicker
        inputFormat="dd.MM.yyyy"
        closeOnSelect
        mask="__.__.____"
        value={date}
        toolbarTitle={t('date')}
        open={dialogOpen}
        //workaround for refilling the datefield after closing datepicker, after value was previously deleted
        onChange={() => false}
        onClose={() => setDialogOpen(false)}
        onAccept={(e) => {
          if (e) {
            onChange(e);
          }
        }}
        renderInput={({ inputRef, inputProps, InputProps }) => (
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <StyledInput
              data-cy="datepicker"
              ref={inputRef}
              {...inputProps}
              {...rest}
              readOnly={true}
              onClick={() => setDialogOpen(true)}
            />
          </Box>
        )}
      />
    );
  }

  const MyActionBar = ({ onAccept, onCancel }) => {
    return (
      <DialogActions>
        <Button style={{ width: 'fit-content' }} onClick={onCancel}>
          {t('cancel')}
        </Button>
        <Button
          variant="primary"
          style={{ width: 'fit-content' }}
          onClick={onAccept}
        >
          Ok
        </Button>
      </DialogActions>
    );
  };

  return (
    <MobileDatePicker
      inputFormat="dd.MM.yyyy"
      toolbarTitle={t('date')}
      value={date}
      okLabel="Jo"
      components={{
        ActionBar: MyActionBar,
      }}
      onAccept={(e) => {
        if (e) {
          onChange(e);
        }
      }}
      onChange={(e) => setDate(e)}
      renderInput={(params) => (
        <MobileInput
          data-cy="datepicker"
          {...params}
          placeholder="dd.mm.yyyy"
          readOnly
        />
      )}
    />
  );
};

DateField.defaultProps = {
  value: '',
};

DateField.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
};

export default DateField;
