import { useState } from 'react';
import { Box } from '@material-ui/core';
import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import { MobileTimePicker } from '@mui/x-date-pickers/MobileTimePicker';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';

import { DesktopTimePicker } from '@mui/x-date-pickers/DesktopTimePicker';
import { DialogActions } from '@mui/material';
import Button from '../Button';

/**
 * DateField wraps the MUI date picker.
 */

const TimeField = ({ error, value, onChange, ...rest }) => {
  const useStyles = makeStyles({
    input: {
      '& .MuiOutlinedInput-root': {
        borderRadius: 0,
        backgroundColor: '#f7f7f7',
        paddingRight: '20px',
      },
      '& .Mui-error fieldset': {
        border: '1px solid var(--error-color) !important',
      },
      '& .Mui-error input': {
        color: 'var(--error-color) !important',
      },
      '& button': {
        padding: 0,
      },
      '& .Mui-focused fieldset': {
        border: 'solid 1px var(--default-text) !important',
      },
      '& input': {
        fontFamily: 'var(--font-family)',
        width: isMobile ? '58px' : '60px',
        border: '1px solid',
        height: '46px',
        borderColor: '#ebebeb',
        opacity: 0.7,
        outline: 'none',
        padding: '0 0.5rem',
        fontSize: isMobile ? '16px' : '14px',
        boxShadow: 'none',
        boxSizing: 'border-box',
        lineHeight: '22px',
        borderRadius: 0,
        backgroundColor: '#f7f7f7',
      },
    },
    mobileInput: {
      fontFamily: 'var(--font-family)',
      width: isMobile ? '58px' : '55px',
      border: '1px solid',
      height: '46px',
      borderColor: error ? 'var(--error-color)' : '#ebebeb',
      opacity: 0.7,
      outline: 'none',
      padding: '0 0.5rem',
      fontSize: isMobile ? '16px' : '14px',
      boxShadow: 'none',
      boxSizing: 'border-box',
      lineHeight: '22px',
      borderRadius: 0,
      backgroundColor: '#f7f7f7',
      caretColor: 'rgba(0,0,0,0)',
      '&:focus': {
        border: 'solid 1px var(--default-text)',
      },
    },
  });
  const classes = useStyles();
  const [dialogOpen, setDialogOpen] = useState(false);
  const { t } = useTranslation('evDriver');

  const MyActionBar = ({ onAccept, onCancel }) => {
    return (
      <DialogActions>
        <Button style={{ width: 'fit-content' }} onClick={onCancel}>
          {t('cancel')}
        </Button>
        <Button
          variant="primary"
          data-cy="ok"
          style={{ width: 'fit-content' }}
          onClick={onAccept}
        >
          Ok
        </Button>
      </DialogActions>
    );
  };
  return (
    <>
      {isMobile && (
        <MobileTimePicker
          ampm={false}
          autoOk
          toolbarTitle={t('time')}
          showToolbar
          value={value}
          open={dialogOpen}
          components={{
            ActionBar: MyActionBar,
          }}
          onClose={() => setDialogOpen(false)}
          onChange={(e) => {
            onChange(e);
          }}
          renderInput={({ inputRef, inputProps }) => (
            <Box
              sx={{ display: 'flex', alignItems: 'center' }}
              onClick={() => setDialogOpen(true)}
            >
              <input
                data-cy="timepicker"
                className={classes.mobileInput}
                readOnly
                ref={inputRef}
                {...inputProps}
                onClick={() => setDialogOpen(true)}
              />
            </Box>
          )}
        />
      )}
      {!isMobile && (
        <DesktopTimePicker
          ampm={false}
          autoOk
          toolbarTitle={t('time')}
          showToolbar
          value={value}
          open={dialogOpen}
          components={{
            ActionBar: MyActionBar,
          }}
          onClose={() => setDialogOpen(false)}
          onChange={(e) => {
            onChange(e);
          }}
          renderInput={({ inputRef, inputProps }) => (
            <Box
              sx={{ display: 'flex', alignItems: 'center' }}
              onClick={() => setDialogOpen(true)}
            >
              <input
                data-cy="timepicker"
                className={classes.mobileInput}
                readOnly
                ref={inputRef}
                {...inputProps}
                onClick={() => setDialogOpen(true)}
              />
            </Box>
          )}
        />
      )}
    </>
  );
};

TimeField.defaultProps = {
  value: '',
};

TimeField.propTypes = {
  value: PropTypes.oneOfType([PropTypes.string, PropTypes.instanceOf(Date)]),
};

export default TimeField;
