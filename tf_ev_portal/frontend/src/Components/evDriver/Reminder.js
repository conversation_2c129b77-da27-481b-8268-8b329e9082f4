import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { FormControlLabel, Switch } from '@material-ui/core';
import Select from '../Select';
import MenuItem from '../MenuItem';
import { Hint, InputWrapper } from './PageTemplate';

const Root = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
`;

const StyledSwitch = styled(Switch)`
  .MuiSwitch-colorSecondary.Mui-checked {
    color: var(--trafineo-rot-100);
  }
  .MuiSwitch-colorSecondary.Mui-checked + .MuiSwitch-track {
    background-color: var(--trafineo-rot-100);
  }
`;

const StyledFormControlLabel = styled(FormControlLabel)`
  .MuiTypography-root {
    font-size: 14px !important;
    color: var(--default-text);
    text-align: left;
  }
`;

const Reminder = ({ value, onChange, hint }) => {
  const { t } = useTranslation('evDriver');

  return (
    <InputWrapper fullWidth>
      <Root>
        <StyledFormControlLabel
          control={
            <StyledSwitch
              checked={value.active}
              onChange={() => onChange({ ...value, active: !value.active })}
              name="principle"
            />
          }
          label={t('reminderLabel')}
        />
        <Select
          disabled={!value.active}
          value={value.interval}
          onChange={(e) => onChange({ ...value, interval: e.target.value })}
        >
          {Object.values([1, 3, 6, 9, 12]).map((entry) => (
            <MenuItem key={entry} value={entry} data-cy={`${entry}Select`}>
              {`${entry} ${t('months')}`}
            </MenuItem>
          ))}
        </Select>
      </Root>
      <Hint style={{ marginTop: '0.75rem' }}>{hint}</Hint>
    </InputWrapper>
  );
};

Reminder.propTypes = {
  value: PropTypes.object,
  onChange: PropTypes.func,
  hint: PropTypes.string,
};

export default Reminder;
