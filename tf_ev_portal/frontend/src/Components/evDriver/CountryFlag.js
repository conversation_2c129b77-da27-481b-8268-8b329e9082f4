import styled from 'styled-components';
import PropTypes from 'prop-types';

/**
 * CountryFlag shows the country flag for each country prop.
 */

const CountryIcon = styled.span`
  margin-right: 0.5rem;
  height: 1.25rem;
  width: 35px;
`;

const CountryIconSmall = styled.span`
  margin-left: 0.5rem;
  top: -1px;
  height: 17px;
  width: 26px;
`;

const CountryFlag = ({ country, small }) =>
  small ? (
    <CountryIconSmall
      className={`flag-icon flag-icon-${country === 'en' ? 'gb' : country}`}
    />
  ) : (
    <CountryIcon
      className={`flag-icon flag-icon-${country === 'en' ? 'gb' : country}`}
    />
  );

CountryFlag.defaultProps = {
  country: '',
  small: false,
};

CountryFlag.propTypes = {
  country: PropTypes.string,
  small: PropTypes.bool,
};

export default CountryFlag;
