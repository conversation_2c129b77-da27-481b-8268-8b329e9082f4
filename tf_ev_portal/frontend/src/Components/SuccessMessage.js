import styled from 'styled-components';
import PropTypes from 'prop-types';
import statusIcon from '../static/img/icons/Icon_Done.svg';
import AddCircleIcon from '@mui/icons-material/AddCircle';

const Wrapper = styled.div`
  padding: 3rem;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
`;
const ImgWrapper = styled.div`
  margin-bottom: 1rem;
`;
const Text = styled.div`
  line-height: 24px;
  text-align: center;
  white-space: pre-line;
`;
const StyledIcon = styled(AddCircleIcon)`
  color: #ffc107;
  height: 55px !important;
  width: 55px !important;
  rotate: 45deg;
`;

const SuccessMessage = ({ message, email, alternateIcon = false }) => {
  return (
    <Wrapper>
      <ImgWrapper>
        {alternateIcon && <StyledIcon />}
        {!alternateIcon && <img src={statusIcon} alt="success" />}
      </ImgWrapper>
      <Text>
        {message} {email && <a href={`mailto:${email}`}>{email}</a>}
      </Text>
    </Wrapper>
  );
};

SuccessMessage.propTypes = {
  message: PropTypes.string,
  alternateIcon: PropTypes.bool,
};
export default SuccessMessage;
