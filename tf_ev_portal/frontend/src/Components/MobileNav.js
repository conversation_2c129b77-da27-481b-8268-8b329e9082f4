import { useState } from 'react';
import clsx from 'clsx';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router';

import { makeStyles } from '@material-ui/core/styles';
import SwipeableDrawer from '@material-ui/core/SwipeableDrawer';
import List from '@material-ui/core/List';
import Divider from '@material-ui/core/Divider';
import ListItem from '@material-ui/core/ListItem';
import ListItemIcon from '@material-ui/core/ListItemIcon';
import ListItemText from '@material-ui/core/ListItemText';
import GridViewIcon from '@mui/icons-material/GridView';
import { IconButton } from '@material-ui/core';

//Icons
import EuroIcon from '@material-ui/icons/Euro';
import EvStationIcon from '@material-ui/icons/EvStation';
import InfoIcon from '@material-ui/icons/Info';
import MenuIcon from '@material-ui/icons/Menu';
import CloseIcon from '@material-ui/icons/Close';
import PersonIcon from '@material-ui/icons/Person';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import HelpIcon from '@material-ui/icons/Help';
import AssignmentIcon from '@material-ui/icons/Assignment';
import SettingsIcon from '@mui/icons-material/Settings';
import CookieIcon from '@mui/icons-material/Cookie';

import ActivitiesDialog from './ActivitiesDialog';
import { getBranding } from '../utils/helper';
import styled from 'styled-components';

const CloseButton = styled(IconButton)`
  color: #fff !important;
  position: absolute !important;
  top: 10px;
  right: 0;
`;

const MenuButton = styled(IconButton)`
  color: #000;
`;

const ListIcon = styled(ListItemIcon)`
  min-width: 38px !important;
  color: var(--default-text) !important;
`;

const LanSelectTitle = styled.div`
  padding: 16px 16px 8px 16px;
  font-size: 16px;
  font-weight: bold;
`;

const LanSelectWrapper = styled.div`
  padding: 8px 12px;
  .MuiButton-label {
    font-size: 16px;
  }
  .MuiButton-startIcon {
    margin-right: 14px;
  }
  .MuiSvgIcon-root {
    font-size: 24px !important;
  }
  :span {
    text-transform: none;
  }
  :svg {
    color: var(--default-text);
    width: 24px;
    height: 24px;
  }
`;

const useStyles = makeStyles({
  list: {
    boxSizing: 'border-box',
    width: '80vw',
  },
  fullList: {
    width: 'auto',
  },
});

const MenuHeader = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  background: var(--menu-background);
  position: relative;
  border-bottom-left-radius: 7.5px;
`;

const MenuHeaderText = styled.div`
  width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: column;
  color: white;
  text-align: center;
  padding: 3rem;
  line-height: 25px;
`;

export default function MobileNav({
  langSelect,
  logout,
  email,
  isDriverRegistered,
}) {
  const classes = useStyles();
  const tEvDriver = useTranslation('evDriver').t;
  const [open, setOpen] = useState(false);
  const { t } = useTranslation('headerbar');
  const tFooter = useTranslation('footer').t;
  const navigate = useNavigate();
  const [isActivitiesDialogVisible, setIsActivitiesDialogVisible] =
    useState(false);
  const tOverview = useTranslation('overview').t;

  const { i18n } = useTranslation('home');

  const toggleDrawer = (open) => (event) => {
    if (
      event &&
      event.type === 'keydown' &&
      (event.key === 'Tab' || event.key === 'Shift')
    ) {
      return;
    }

    setOpen(open);
  };

  const loggedInNav = [
    {
      label: tEvDriver('myInformation'),
      click: () =>
        navigate(
          `/myinformation${isDriverRegistered ? '/personalinformation' : ''}`,
        ),
      icon: <PersonIcon />,
    },
    {
      label: tFooter('faq'),
      icon: <HelpIcon />,
    },
    {
      label: tFooter('cookies'),
      click: () => navigate('/cookie-settings'),
      icon: <CookieIcon />,
    },
    {
      label: tFooter('data-privacy-statement'),
      click: () => navigate('/data-privacy-statement'),
      icon: <AssignmentIcon />,
    },
    {
      label: tFooter('terms-and-conditions'),
      click: () => navigate('/terms-and-conditions'),
      icon: <AssignmentIcon />,
    },
    {
      label: tFooter('imprint'),
      click: () => navigate('/imprint'),
      icon: <InfoIcon />,
      last: true,
    },
  ];

  if (isDriverRegistered) {
    loggedInNav.unshift({
      label: tEvDriver('driverDashboard'),
      click: () => navigate('/'),
      icon: <GridViewIcon />,
    });
    loggedInNav.splice(2, 0, {
      label: tOverview('myReimbursements'),
      click: () => navigate('/reimbursements'),
      icon: <EuroIcon />,
    });
    loggedInNav.splice(2, 0, {
      label: tOverview('myChargingSessions'),
      click: () => navigate('/charging_sessions'),
      icon: <EvStationIcon />,
    });
    loggedInNav.splice(2, 0, {
      label: tEvDriver('settings'),
      click: () => navigate('/settings'),
      icon: <SettingsIcon />,
    });
  }

  const loggedOutNav = [
    {
      label: tFooter('imprint'),
      click: () => navigate('/imprint'),
      icon: <InfoIcon />,
      last: true,
    },
    {
      label: tFooter('data-privacy-statement'),
      click: () => navigate('/data-privacy-statement'),
      icon: <AssignmentIcon />,
    },
    {
      label: tFooter('cookies'),
      click: () =>
        navigate('/cookie-settings', {
          state: { prevPathname: window.location.pathname },
        }),
      icon: <CookieIcon />,
    },
    {
      label: tFooter('faq'),
      icon: <HelpIcon />,
      last: true,
    },
  ];

  const list = (anchor) => (
    <div
      className={clsx(classes.list, {
        [classes.fullList]: anchor === 'top' || anchor === 'bottom',
      })}
      role="presentation"
    >
      <List>
        {(email ? loggedInNav : loggedOutNav).map((e) => {
          if (e.label.includes('FAQ')) {
            return (<>
              {/* <ListItem
                button
                onClick={() => {
                  setOpen(false);
                }}
                component="a"
                key={e.label}
                target="_blanc"
                href={
                  getBranding() === 'trafineo'
                    ? `https://ev-helpcenter.trafineo.com/hc/${
                        i18n.language === 'en' ? 'en-gb' : i18n.language
                      }`
                    : getBranding() === 'aral'
                    ? `https://ev-helpcenter.aral-service.trafineo.com/hc/${
                        i18n.language === 'en' ? 'en-gb' : i18n.language
                      }`
                    : `https://ev-helpcenter.bp-service.trafineo.com/hc/${
                        i18n.language === 'en' ? 'en-gb' : i18n.language
                      }`
                }
              >
                <ListIcon>{e.icon}</ListIcon>
                <ListItemText primary={e.label} />
              </ListItem> */}
              </>
            );
          }
          return (
            <ListItem
              button
              onClick={() => {
                e.click();
                setOpen(false);
              }}
              key={e.label}
            >
              <ListIcon>{e.icon}</ListIcon>
              <ListItemText primary={e.label} />
            </ListItem>
          );
        })}
      </List>
      <Divider />
      <LanSelectTitle>{t('language')}</LanSelectTitle>
      <LanSelectWrapper>{langSelect}</LanSelectWrapper>
      <Divider />
      {email && (
        <ListItem
          button
          onClick={() => {
            logout();
            setOpen(false);
          }}
          key={'logout'}
        >
          <ListIcon>
            <ExitToAppIcon />
          </ListIcon>
          <ListItemText primary={t('logout')} />
        </ListItem>
      )}
    </div>
  );

  return (
    <div>
      {isActivitiesDialogVisible && (
        <ActivitiesDialog
          open={isActivitiesDialogVisible}
          onClose={() => {
            setIsActivitiesDialogVisible(false);
          }}
        />
      )}
      <MenuButton data-cy="revertTariff" onClick={toggleDrawer(true)}>
        <MenuIcon />
      </MenuButton>
      <SwipeableDrawer
        anchor="right"
        open={open}
        onClose={toggleDrawer(false)}
        onOpen={toggleDrawer(true)}
      >
        <MenuHeader>
          <CloseButton data-cy="revertTariff" onClick={toggleDrawer(false)}>
            <CloseIcon />
          </CloseButton>
          <MenuHeaderText>
            <div>
              <b>{t('welcome')}</b>
            </div>
            {email && <div>{email}</div>}
          </MenuHeaderText>
        </MenuHeader>
        {list('right')}
      </SwipeableDrawer>
    </div>
  );
}
