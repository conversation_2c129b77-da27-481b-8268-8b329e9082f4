import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';

const StyledTimeout = styled.p`
  font-size: 19px;
  text-align: center;
  color: var(--trafineo-grau-100);
  opacity: 70%;
  position: absolute;
  z-index: 3;
  width: 100%;
  height: 95%;
  margin: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
`;

const Timeout = () => {
  const { t } = useTranslation('evDriver');

  return <StyledTimeout>{t('timeoutMessage')}</StyledTimeout>;
};

Timeout.defaultProps = {
  visible: false,
};

Timeout.propTypes = {
  visible: PropTypes.bool,
  text: PropTypes.string.isRequired,
};

export default Timeout;
