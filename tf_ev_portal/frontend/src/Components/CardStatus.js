import { useTranslation } from 'react-i18next';
import { StatusIndicator } from './dashboard/RowTemplates';

const CardStatus = ({ card_status, expiring_soon }) => {
  const { t } = useTranslation('directCustomer');
  return (
    <>
      {!card_status ? (
        <StatusIndicator margin background="#dbdbdb" color="#323338">
          {t('statusInactive')}
        </StatusIndicator>
      ) : expiring_soon ? (
        <StatusIndicator
          margin
          background="rgba(255, 196, 32, 0.49)"
          color="#ff7e00"
        >
          {t('expiring_soonFilter')}
        </StatusIndicator>
      ) : (
        <StatusIndicator
          margin
          color="#090"
          background="rgba(153, 204, 0, 0.27)"
        >
          {t('statusActive')}
        </StatusIndicator>
      )}
    </>
  );
};

export default CardStatus;
