import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material';
import { useContext } from 'react';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import TariffIcon from '../static/img/icon-tariff.svg';
import { evDriverContext } from '../ContextProvider';
import { db } from '../db';
import { formatDate, getTariffChronology } from '../utils/helper';
import FileDownload from './FileDownload';
import { EditButton, RevertButton } from './ActionButtons';
import qs from 'querystring';

const DetailContainer = styled.div`
  ${(props) =>
    isMobile
      ? ''
      : props.padding
      ? 'padding-left: calc(200px + 1.25rem)'
      : 'padding-left: 1.25rem'};
`;
const ValidityContainer = styled.div`
  ${(props) => props.padding && 'padding-left: 1.25rem'};
`;

const LabelCol = styled.div`
  display: flex;
  font-weight: bold;
  ${(props) => props.singleTariff && 'width: ' + (isMobile ? 'auto' : '200px')};
`;

const ContentCol = styled.div`
  display: flex;
  font-size: 14px;
  line-height: 21px;
`;

const DetailRow = styled.div`
  display: flex;
  font-size: 14px;
  line-height: 21px;
  margin: 0.25rem 0;
`;
const Label = styled.b`
  margin-right: 0.25rem;
`;
const StyledTariffIcon = styled.img`
  margin-right: 0.5rem;
  width: 13px;
`;
const ActionsContainer = styled.div`
  margin-left: auto;
`;

export const StyledAccordion = styled(Accordion)`
  border-radius: 0;
  box-shadow: none !important;
  background-color: #f2f2f2 !important;
  :nth-child(even) {
    background-color: #fbfafa !important;
  }
  ${(props) => props.singleTariff && 'background-color: white !important'};
  ::before {
    display: none;
  }
`;
export const StyledAccordionSummary = styled(AccordionSummary)`
  width: 100%;
  ${(props) => props.modified && 'color: var(--trafineo-rot-100) !important'};
  padding: 0 1rem;
  & .MuiAccordionSummary-content {
    font-size: 14px;
    line-height: 24px;
    margin: 1rem 0 !important;
    align-items: center;
  }
`;
export const StyledAccordionDetails = styled(AccordionDetails)`
  display: flex;
  ${(props) => props.modified && 'color: var(--trafineo-rot-100)'};
  flex-direction: column;
  margin: 0.5rem 0;
`;

const Tariff = ({
  tariff,
  tariffModified,
  singleTariff,
  index,
  files,
  editDisabled,
  skipTypeSelection = false,
}) => {
  const navigate = useNavigate();
  const { t } = useTranslation('evDriver');
  const { evDriverDispatch } = useContext(evDriverContext);
  const location = useLocation();

  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);

  if (parsedQueryString && parsedQueryString.skip) {
    skipTypeSelection = parsedQueryString.skip;
  }

  return (
    <StyledAccordion
      singleTariff={singleTariff}
      modified={tariffModified}
      defaultExpanded={tariffModified || singleTariff}
      disableGutters
      square
    >
      <StyledAccordionSummary
        modified={tariffModified}
        expandIcon={
          tariffModified || singleTariff ? undefined : <ExpandMoreIcon />
        }
        aria-controls="panel1a-content"
        id="panel1a-header"
      >
        <LabelCol singleTariff={singleTariff}>
          <StyledTariffIcon src={TariffIcon} alt="Tariff" />
          {singleTariff &&
            t(
              getTariffChronology(
                tariff.tariff_elements[0].restrictions[0].start_date,
                tariff.tariff_elements[0].restrictions[0].end_date,
              ),
            )}
        </LabelCol>
        <ValidityContainer padding={singleTariff}>
          <ContentCol>
            <Label>
              {t('validity')}
              {':'}
            </Label>
            {formatDate(tariff.tariff_elements[0].restrictions[0].start_date)}{' '}
            {t('until')}{' '}
            {tariff.tariff_elements[0].restrictions[0].end_date === '9999-12-31'
              ? '-'
              : formatDate(tariff.tariff_elements[0].restrictions[0].end_date)}
          </ContentCol>
        </ValidityContainer>
        <ActionsContainer>
          <EditButton
            disabled={(!tariffModified && editDisabled) || singleTariff}
            onClick={() => {
              if (tariffModified) {
                evDriverDispatch({
                  type: 'tariffType',
                  value: {
                    tariffType: null,
                  },
                });
              } else {
                evDriverDispatch({
                  type: 'revert',
                  value: {
                    tariffType: null,
                  },
                });
              }
              navigate(
                `/tariffselection?id=${index}&skipType=${skipTypeSelection}`,
              );
            }}
          />
          {tariffModified && !singleTariff && (
            <RevertButton
              onClick={() => {
                evDriverDispatch({
                  type: 'revert',
                  value: {
                    tariffType: null,
                  },
                });
                db.delete();
              }}
              variant="smallSec"
              data-cy="revertTariff"
            />
          )}
        </ActionsContainer>
      </StyledAccordionSummary>
      <StyledAccordionDetails modified={tariffModified}>
        <DetailContainer padding={singleTariff}>
          <DetailRow>
            <ContentCol>
              <Label>{t('tariffPrice')}</Label>
              {tariff.tariff_elements[0].price_components[0].price}
              {' EUR / kWh'}
            </ContentCol>
          </DetailRow>
          {tariff.tariff_elements.length > 1 && (
            <>
              <DetailRow>
                <ContentCol>
                  <Label>{t('tariffOffPeakPrice')}</Label>
                  {tariff.tariff_elements[1].price_components[0].price}
                  {' EUR / kWh'}
                </ContentCol>
              </DetailRow>
              <DetailRow style={{ marginTop: '1.5rem' }}>
                <ContentCol>
                  <Label>{t('tariffValidityPeriodOffPeak')}</Label>
                </ContentCol>
              </DetailRow>
              <DetailRow>
                <ContentCol>
                  {t('tariffMondayToFriday')}{' '}
                  {t('tariffFromTo', {
                    from: tariff.tariff_elements[1].restrictions[0].start_time,
                    to: tariff.tariff_elements[1].restrictions[0].end_time,
                  })}
                </ContentCol>
              </DetailRow>
              <DetailRow>
                <ContentCol>
                  {t('tariffSaturdaySunday')}{' '}
                  {tariff.tariff_elements.length > 2
                    ? t('tariffAllDay')
                    : t('tariffFromTo', {
                        from: tariff.tariff_elements[1].restrictions[0]
                          .start_time,
                        to: tariff.tariff_elements[1].restrictions[0].end_time,
                      })}
                </ContentCol>
              </DetailRow>
            </>
          )}
          <DetailRow>
            <ContentCol>
              <Label>{t('greenEnergy')}</Label>
              {t(
                tariff?.energy_mix?.is_green_energy === null
                  ? 'greenTariffNotProvided'
                  : tariff.energy_mix
                  ? JSON.stringify(tariff.energy_mix) === '{}'
                    ? 'greenTariffNotProvided'
                    : tariff.energy_mix?.is_green_energy
                    ? 'trueGreenTariff'
                    : tariff.energy_mix?.environ_impact
                    ? 'falseGreenTariff'
                    : 'trueGreenTariff'
                  : 'greenTariffNotProvided',
              )}
            </ContentCol>
          </DetailRow>
          {(tariff.energy_mix?.environ_impact ||
            tariff.energy_mix?.is_green_energy) && (
            <DetailRow>
              <ContentCol>
                <Label>{t('emissionRate')}</Label>

                {tariff.energy_mix?.is_green_energy
                  ? '0.00'
                  : tariff.energy_mix.environ_impact?.find(
                      (e) => e.category === 'CARBON_DIOXIDE',
                    ).amount}
                {' g/kWh'}
              </ContentCol>
            </DetailRow>
          )}
          {files && (
            <DetailRow>
              <ContentCol style={{ width: '100%' }}>
                <div
                  style={{
                    display: 'flex',
                    flexDirection: 'column',
                    width: '100%',
                  }}
                >
                  <Label>{`${t('files')} :`}</Label>
                  <FileDownload files={files} />
                </div>
              </ContentCol>
            </DetailRow>
          )}
          {tariff.comment && (
            <DetailRow>
              <ContentCol>
                <Label>{t('Comments')}</Label>
                {tariff.comment}
              </ContentCol>
            </DetailRow>
          )}
        </DetailContainer>
      </StyledAccordionDetails>
    </StyledAccordion>
  );
};
export default Tariff;
