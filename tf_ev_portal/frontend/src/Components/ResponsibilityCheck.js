import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import yellowInfoIcon from '../static/img/icons/Icon_Info_Yellow.svg';
import Checkbox from './Checkbox';

const ResponsibilityContainer = styled.div`
  display: flex;
  flex-direction: column;
  border: solid 1px var(--trafineo-grau-70);
  background-color: var(--trafineo-grau-20);
  padding: 1rem 1rem 1rem 1em;
  margin-top: 1rem;
`;

const ResponsibilityRow = styled.div`
  display: flex;
  align-items: center;
  & p {
    padding-left: 0.5rem;
  }
`;

const ResponsibilityIcon = styled.img`
  height: 16px;
  width: 16px;
  margin-left: 0.25rem;
`;

const ResponsibilityCheck = ({ responsibility, setResponsibility }) => {
  const { t } = useTranslation('evDriver');
  return (
    <ResponsibilityContainer>
      <ResponsibilityRow>
        <ResponsibilityIcon src={yellowInfoIcon} alt="responsibility" />
        <p>{t('headlineCheckboxElectricityTariff')}</p>
      </ResponsibilityRow>
      <ResponsibilityRow>
        <Checkbox
          id="responsibiliyCheckBox"
          checked={responsibility}
          onChange={() => setResponsibility(!responsibility)}
        />
        <p>{t('checkboxElectricityTariff')}</p>
      </ResponsibilityRow>
    </ResponsibilityContainer>
  );
};

ResponsibilityCheck.propTypes = {
  responsibility: PropTypes.bool.isRequired,
  setResponsibility: PropTypes.func.isRequired,
};

export default ResponsibilityCheck;
