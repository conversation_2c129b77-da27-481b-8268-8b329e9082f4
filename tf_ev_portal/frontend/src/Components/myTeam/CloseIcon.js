import styled from 'styled-components';
import PropTypes from 'prop-types';
import Close from '@material-ui/icons/Close';
import { IconButton } from '@material-ui/core';

const CloseIconWrapper = styled.div`
  position: absolute;
  color: var(--default-text);
  position: absolute;
  justify-content: center;
  align-items: center;
  display: flex;
  right: 1rem;
  top: 1rem;
  z-index: 1;
  cursor: pointer;
  :hover {
    opacity: 0.7;
  }
`;

const CloseIcon = ({ onClick }) => {
  return (
    <CloseIconWrapper>
      <IconButton data-cy="closeButton" onClick={onClick}>
        <Close />
      </IconButton>
    </CloseIconWrapper>
  );
};
CloseIcon.propTypes = {
  message: PropTypes.func,
};
export default CloseIcon;
