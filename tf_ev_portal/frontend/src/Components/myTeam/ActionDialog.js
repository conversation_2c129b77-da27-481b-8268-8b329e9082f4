import { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import requester, { serviceRequest } from '../../utils/requester';
import styled from 'styled-components';
import { Dialog, MenuItem } from '@material-ui/core';
import CountryFlag from '../evDriver/CountryFlag';
import { languages } from '../../constants/localization';
import {
  Description,
  Error,
  InputWrapper,
  Row,
} from '../evDriver/PageTemplate';
import { Button } from '..';
import SuccessMessage from '../SuccessMessage';
import CloseIcon from './CloseIcon';
import CircularProgress from '../CircularProgress';
import Select from '../Select';
import TagManager from 'react-gtm-module';

const DialogWrapper = styled.div`
  position: relative;
  width: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  width: 500px;
  input {
    width: 500px;
    background-color: white;
    ::placeholder {
      color: grey;
      opacity: 1;
    }
  }
  button {
    width: fit-content;
    margin-left: 0.5rem;
  }
`;
const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const ActionDescription = styled.div`
  margin: 2rem 0;
  font-size: 16px;
  line-height: 24px;
`;
const ButtonWrapper = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
`;

const Placeholder = styled.div`
  color: grey;
`;

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const ErrorContainer = styled.div`
  margin-bottom: 1rem;
`;

const ActionDialog = ({
  open,
  action,
  onClose,
  email,
  salesAdmin,
  idpId,
  support,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [language, setLanugage] = useState({ value: '', error: false });
  const { t } = useTranslation('serviceProvider');
  const tLocalization = useTranslation('localization').t;
  const resetComponent = () => {
    setShowError(false);
    setIsLoading(false);
    setShowSuccess(false);
  };

  useEffect(() => {
    if (open) {
      resetComponent();
    }
  }, [open]);

  const actions = {
    reinvite: useCallback(async () => {
      try {
        const rsp =
          salesAdmin || support
            ? await serviceRequest().put(`users/v1/users/`, {
                email: email,
                role: 'support',
                language: language.value,
                function: 'admin',
              })
            : await requester().patch(
                `/fm_management/${email}/reinvite`,
                {
                  language: language.value,
                },
                {
                  headers: {
                    'Content-Type': 'application/json',
                  },
                },
              );
        return rsp;
      } catch (e) {
        console.error(e);
        return null;
      }
    }, [email, salesAdmin, language.value, support]),
    deactivate: useCallback(async () => {
      try {
        const rsp =
          support || salesAdmin
            ? await serviceRequest().delete(`users/v1/users/${idpId}`, {})
            : await requester().delete(`/fm_management/${email}`);

        return rsp;
      } catch (e) {
        console.error(e);
        return null;
      }
    }, [email, salesAdmin, idpId, support]),
    promote: useCallback(async () => {
      try {
        const rsp = await requester().patch(
          `/fm_management/${email}`,
          {
            function: 'admin',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        return rsp;
      } catch (e) {
        return null;
      }
    }, [email]),
    demote: useCallback(async () => {
      try {
        const rsp = await requester().patch(
          `/fm_management/${email}`,
          {
            function: 'operative',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        return rsp;
      } catch (e) {
        return null;
      }
    }, [email]),
  };

  const performAction = async () => {
    setIsLoading(true);
    const rspData = await actions[action]();
    TagManager.dataLayer({
      dataLayer: {
        event: 'generic_event',
        generic_event: {
          name: action,
          action: 'request',
          category: 'action_dialog_button',
          label: `${action}Action`,
        },
      },
    });
    setShowError(rspData ? false : true);
    setShowSuccess(rspData ? true : false);
    setIsLoading(false);
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <CloseIcon onClick={onClose} />
      {showSuccess ? (
        <SuccessMessage
          message={t(`${action}Sucess`, { fleetManagerMail: email })}
        />
      ) : (
        <DialogWrapper>
          <ActionDescription>
            {t(`${action}Description`, { fleetManagerMail: email })}
          </ActionDescription>
          {action === 'reinvite' && (
            <Row>
              <InputWrapper>
                {!salesAdmin && (
                  <>
                    <Description error={language.error}>
                      {t('inviteLanguage')}
                    </Description>
                    <Select
                      big
                      error={language.error}
                      value={language.value}
                      onFocus={() => setLanugage({ ...language, error: false })}
                      onChange={(e) => {
                        setLanugage({ ...language, value: e.target.value });
                      }}
                      displayEmpty
                      renderValue={
                        language.value !== ''
                          ? undefined
                          : () => (
                              <Placeholder>
                                {t('inviteLanguagePlaceholder')}
                              </Placeholder>
                            )
                      }
                    >
                      {Object.keys(languages).map((entry) => (
                        <StyledMenuItem
                          key={entry}
                          value={entry}
                          data-cy={`${entry}Select`}
                        >
                          <CountryFlag country={entry} />
                          {tLocalization(`${entry}Language`)}
                        </StyledMenuItem>
                      ))}
                    </Select>
                  </>
                )}
              </InputWrapper>
            </Row>
          )}

          {showError && (
            <ErrorContainer>
              <Error>{t('requestError')}</Error>
            </ErrorContainer>
          )}

          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button onClick={onClose} variant="secondary">
                {t(`cancelButton`)}
              </Button>
              <Button
                disabled={
                  salesAdmin
                    ? false
                    : action === 'reinvite' && language.value === ''
                }
                onClick={performAction}
                variant="primary"
              >
                {t(`${action}Action`)}
              </Button>
            </ButtonWrapper>
          )}
        </DialogWrapper>
      )}
    </Dialog>
  );
};

ActionDialog.propTypes = {
  open: PropTypes.bool,
  action: PropTypes.string,
  email: PropTypes.string,
  onClose: PropTypes.func,
};

export default ActionDialog;
