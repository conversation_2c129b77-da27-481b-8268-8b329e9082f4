import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import requester from '../../utils/requester';
import styled from 'styled-components';
import { languages, countries } from '../../constants/localization';
import AddBoxIcon from '@material-ui/icons/AddBox';
import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';

import { IconButton, MenuItem } from '@material-ui/core';
import {
  Description,
  InputWrapper,
  Row,
  Error,
} from '../evDriver/PageTemplate';
import { Button, Checkbox, TextField } from '..';
import Select from '../Select';
import CountryFlag from '../evDriver/CountryFlag';
import CircularProgress from '../CircularProgress';
import { mailRegex, prohibitedDomains } from '../../utils/helper';
import DialogWrapper from '../DialogWrapper';

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const Placeholder = styled.div`
  color: grey;
`;

const ErrorContainer = styled.div`
  margin-bottom: 1rem;
`;

const CheckboxText = styled.div`
  margin-left: 0.5rem;
  font-size: 14px;
`;
const CheckboxContainer = styled.div`
  color: black;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;
const CompanyIdInputWrapper = styled.div`
  display: flex;
  margin-top: 1rem;
  input {
    width: 65px;
    margin-right: 1rem;
  }
`;
const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  button {
    width: fit-content;
    margin-left: 1rem;
  }
`;
const CompanyId = styled.div`
  ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  height: 48px;
`;
const IconButtonWrapper = styled.div`
  margin-left: 0.5rem;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const StyledLi = styled.li`
  color: var(--default-text);
  font-size: 14px;
  line-height: 21px;
  margin-bottom: 0.75rem;
`;

const StyledUl = styled.ul`
  padding: 1rem;
  margin: 0;
`;

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const AddFleetManagerDialog = ({ open, superUser, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const { t } = useTranslation('serviceProvider');
  const tLocalization = useTranslation('localization').t;
  const tSuperUser = useTranslation('superuser').t;
  const tActions = useTranslation('actions').t;
  const [issuerId, setIssuerId] = useState('');
  const [authId, setAuthId] = useState('');
  const [email, setEmail] = useState({ value: '', error: false });
  const [role, setRole] = useState({ value: '', error: false });
  const [language, setLanugage] = useState({ value: '', error: false });
  const [countryCode, setCountryCode] = useState({ value: '', error: false });
  const [companyIds, setCompanyIds] = useState({ value: [], error: false });
  const [page, setPage] = useState(2);
  const [confirmation, setConfirmation] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('requestError');

  const resetComponent = () => {
    setEmail({ value: '', error: false });
    setRole({ value: '', error: false });
    setLanugage({ value: '', error: false });
    setCompanyIds({ value: [], error: false });
    setCountryCode({ value: '', error: false });
    setPage(1);
    setConfirmation(false);
    setShowSuccess(false);
    setIsErrorVisible(false);
    setIsLoading(false);
  };

  useEffect(() => {
    if (open) {
      resetComponent();
    }
  }, [open]);

  const moveToNextPage = () => {
    let error = false;
    if (email.value.match(mailRegex)) {
      setEmail({
        ...email,
        error:
          prohibitedDomains.indexOf(email.value.split('@')[1].split('.')[0]) >
          -1,
      });
      error =
        prohibitedDomains.indexOf(email.value.split('@')[1].split('.')[0]) > -1;
    } else {
      setEmail({ ...email, error: true });
      error = true;
    }
    if (role.value === '') {
      setRole({ ...role, error: true });
      error = true;
    }
    if (language.value === '') {
      setLanugage({ ...language, error: true });
      error = true;
    }
    if (superUser) {
      if (countryCode.value === '') {
        setCountryCode({ ...countryCode, error: true });
        error = true;
      }
      if (companyIds.value.length === 0) {
        setCompanyIds({ ...companyIds, error: true });
        error = true;
      }
    }
    if (!error) {
      setPage(2);
    }
  };

  const addFleetManager = async () => {
    setIsLoading(true);
    try {
      await requester().post(
        `/fm_management${superUser ? '/as_superuser' : ''}`,
        superUser
          ? {
              role: 'fleetmanager_direct',
              email: email.value,
              language: language.value,
              function: role.value,
              country_code: countryCode.value,
              company_ids: companyIds.value,
            }
          : {
              email: email.value,
              language: language.value,
              function: role.value,
            },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      setIsErrorVisible(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (err) {
      setErrorMessage(
        t(err.response?.status === 409 ? 'mailExistsError' : 'requestError'),
      );
      setShowSuccess(false);
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  const addCompanyId = () => {
    companyIds.value.push(issuerId + authId);
    setIssuerId('');
    setAuthId('');
  };

  return (
    <DialogWrapper
      headline={t('addFleetmanagerHeadline')}
      open={open}
      description={
        page === 2 &&
        t('PermissionsDescripton', {
          fleetManagerMail: email.value,
        })
      }
      onClose={onClose}
      showSuccess={showSuccess}
      successMessage={t('fleetManagerInviteSuccess', {
        fleetManagerMail: email.value,
      })}
    >
      {page === 1 ? (
        <>
          <Row>
            <InputWrapper fullWidth>
              <TextField
                autoFocus
                newDriver
                error={email.error}
                onFocus={() => setEmail({ ...email, error: false })}
                data-cy="emailInput"
                value={email.value}
                placeholder={t('emailPlaceholder')}
                label={t('email')}
                name="email"
                onChange={(e) => {
                  setEmail({ ...email, value: e.target.value });
                }}
              />
              {email.error && <Error visible={'true'}>{t('emailError')}</Error>}
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper fullWidth>
              <Description error={role.error}>{t('role')}</Description>
              <Select
                big
                value={role.value}
                placeholder={t('rolePlaceholder')}
                error={role.error}
                onFocus={() => setRole({ ...role, error: false })}
                onChange={(e) => {
                  setRole({ ...role, value: e.target.value });
                }}
                displayEmpty
                renderValue={
                  role.value !== ''
                    ? undefined
                    : () => <Placeholder>{t('rolePlaceholder')}</Placeholder>
                }
              >
                {['admin', 'operative'].map((entry) => (
                  <StyledMenuItem
                    key={entry}
                    value={entry}
                    data-cy={`${entry}Select`}
                  >
                    {t(entry)}
                  </StyledMenuItem>
                ))}
              </Select>
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper fullWidth>
              <Description error={language.error}>
                {t('inviteLanguage')}
              </Description>
              <Select
                big
                error={language.error}
                value={language.value}
                onFocus={() => setLanugage({ ...language, error: false })}
                onChange={(e) => {
                  setLanugage({ ...language, value: e.target.value });
                }}
                displayEmpty
                renderValue={
                  language.value !== ''
                    ? undefined
                    : () => (
                        <Placeholder>
                          {t('inviteLanguagePlaceholder')}
                        </Placeholder>
                      )
                }
              >
                {Object.keys(languages).map((entry) => (
                  <StyledMenuItem
                    key={entry}
                    value={entry}
                    data-cy={`${entry}Select`}
                  >
                    <CountryFlag country={entry} />
                    {tLocalization(`${entry}Language`)}
                  </StyledMenuItem>
                ))}
              </Select>
            </InputWrapper>
          </Row>
          {superUser && (
            <>
              <Row>
                <InputWrapper fullWidth>
                  <Description error={countryCode.error}>
                    {t('countryCode')}
                  </Description>
                  <Select
                    big
                    error={countryCode.error}
                    value={countryCode.value}
                    onFocus={() =>
                      setCountryCode({ ...countryCode, error: false })
                    }
                    onChange={(e) => {
                      setCountryCode({
                        ...countryCode,
                        value: e.target.value,
                      });
                    }}
                    displayEmpty
                    renderValue={
                      countryCode.value !== ''
                        ? undefined
                        : () => (
                            <Placeholder>
                              {t('countryCodePlaceholder')}
                            </Placeholder>
                          )
                    }
                  >
                    {Object.keys(countries).map((entry) => (
                      <StyledMenuItem
                        key={entry}
                        value={entry}
                        data-cy={`${entry}Select`}
                      >
                        <CountryFlag country={entry} />
                        {tLocalization(`${entry}CountryName`)}
                      </StyledMenuItem>
                    ))}
                  </Select>
                </InputWrapper>
              </Row>
              <div>
                <div>
                  <Description error={companyIds.error}>
                    {t('companyIds')}
                  </Description>
                </div>
                {companyIds.value.length === 0 && (
                  <CompanyId error={companyIds.error}>
                    {t('addCompanyIdMessage')}
                  </CompanyId>
                )}
                {companyIds.value.map((entry) => (
                  <CompanyId key={entry}>
                    {entry}
                    <IconButtonWrapper>
                      <IconButton
                        onClick={() => {
                          setCompanyIds({
                            ...companyIds,
                            value: companyIds.value.filter((e) => e !== entry),
                          });
                        }}
                      >
                        <IndeterminateCheckBoxIcon />
                      </IconButton>
                    </IconButtonWrapper>
                  </CompanyId>
                ))}
              </div>
              <Row>
                <CompanyIdInputWrapper>
                  <TextField
                    value={issuerId}
                    onChange={(e) => {
                      const { value } = e.target;
                      if (value.match(/^\d+$/) || value === '') {
                        setIssuerId(value);
                      }
                    }}
                    type="text"
                    maxlength="6"
                    newDriver
                    disableMinWidth
                    onFocus={() =>
                      setCompanyIds({ ...companyIds, error: false })
                    }
                    label={tSuperUser('issuerId')}
                  />
                  <TextField
                    value={authId}
                    onChange={(e) => {
                      const { value } = e.target;
                      if (value.match(/^\d+$/) || value === '') {
                        setAuthId(value);
                      }
                    }}
                    type="text"
                    maxlength="6"
                    newDriver
                    disableMinWidth
                    onFocus={() =>
                      setCompanyIds({ ...companyIds, error: false })
                    }
                    label={tSuperUser('authId')}
                  />
                  <IconButtonWrapper>
                    <IconButton
                      disabled={
                        companyIds.value.indexOf(issuerId + authId) !== -1 ||
                        issuerId.length !== 6 ||
                        authId.length !== 6
                      }
                      onClick={addCompanyId}
                    >
                      <AddBoxIcon />
                    </IconButton>
                  </IconButtonWrapper>
                </CompanyIdInputWrapper>
              </Row>
            </>
          )}
          <ButtonWrapper>
            <Button onClick={moveToNextPage} variant="primary">
              {t('continue')}
            </Button>
          </ButtonWrapper>
        </>
      ) : (
        <>
          <StyledUl>
            {[...Array(9)].map((x, i) => {
              if (
                (role.value === 'operative' && i > 5) ||
                role.value === 'admin'
              ) {
                return (
                  <StyledLi key={t(`permission${i + 1}`)}>
                    {t(`permission${i + 1}`)}
                  </StyledLi>
                );
              }
              return <></>;
            })}
          </StyledUl>
          <CheckboxContainer>
            <Checkbox
              onClick={() => setConfirmation(!confirmation)}
              checked={confirmation}
            />
            <CheckboxText>{t('confirmationText')}</CheckboxText>
          </CheckboxContainer>
          {isErrorVisible && (
            <ErrorContainer>
              <Error>{t(errorMessage)}</Error>
            </ErrorContainer>
          )}
          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button onClick={() => setPage(1)} variant="secoundary">
                {tActions('back')}
              </Button>
              <Button
                disabled={!confirmation}
                onClick={addFleetManager}
                variant="primary"
              >
                {t('sendInvitation')}
              </Button>
            </ButtonWrapper>
          )}
        </>
      )}
    </DialogWrapper>
  );
};

AddFleetManagerDialog.propTypes = {
  open: PropTypes.bool,
  superUser: PropTypes.bool,
  onClose: PropTypes.func,
};

export default AddFleetManagerDialog;
