import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { serviceRequest } from '../../utils/requester';
import { languages } from '../../constants/localization';
import styled from 'styled-components';
import {
  Description,
  InputWrapper,
  Row,
  Error,
  InformationText,
  InformationContainer,
  InformationIcon,
} from '../evDriver/PageTemplate';
import { Button, Checkbox, TextField } from '..';
import { MenuItem } from '@material-ui/core';
import Select from '../Select';
import CountryFlag from '../evDriver/CountryFlag';
import CircularProgress from '../CircularProgress';
import DialogWrapper from '../DialogWrapper';
import infoIcon from '../../static/img/icons/Info_Icon.svg';

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const ErrorContainer = styled.div`
  margin-bottom: 1rem;
`;

const CheckboxText = styled.div`
  margin-left: 0.5rem;
  font-size: 14px;
`;
const CheckboxContainer = styled.div`
  color: black;
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
`;

const ButtonWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  button {
    width: fit-content;
    margin-left: 1rem;
  }
`;

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const Placeholder = styled.div`
  color: grey;
`;

const AddSupportAdminDialog = ({ open, onClose, isSupport = false }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const { t } = useTranslation('serviceProvider');
  const tLocalization = useTranslation('localization').t;
  const [email, setEmail] = useState({ value: '', error: false });
  const [confirmation, setConfirmation] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState('requestError');
  const [language, setLanugage] = useState({ value: '', error: false });

  delete languages.nl;

  const resetComponent = () => {
    setEmail({ value: '', error: false });
    setConfirmation(false);
    setShowSuccess(false);
    setIsErrorVisible(false);
    setIsLoading(false);
  };

  useEffect(() => {
    if (open) {
      resetComponent();
    }
  }, [open]);

  const addSupport = async () => {
    let error = false;

    if (language.value === '') {
      setLanugage({ ...language, error: true });
      error = true;
    }

    // if (email.value.match(mailRegex)) {
    //   if (email.value.split('@')[1].split('.')[0].includes('bp')) {
    //     setEmail({
    //       ...email,
    //       error:
    //         prohibitedDomains.indexOf(email.value.split('@')[1].split('.')[0]) >
    //         -1,
    //     });
    //     error =
    //       prohibitedDomains.indexOf(email.value.split('@')[1].split('.')[0]) >
    //       -1;
    //   } else {
    //     setEmail({
    //       ...email,
    //       error: 'notAralOrBp',
    //     });
    //     error = true;
    //   }
    // } else {
    //   setEmail({ ...email, error: true });
    //   error = true;
    // }

    if (error) {
      return;
    }

    setIsLoading(true);
    try {
      await serviceRequest().put('users/v1/users/', {
        email: email.value,
        role: 'support',
        language: language.value,
        function: 'admin',
      });
      setIsErrorVisible(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (err) {
      setErrorMessage(
        t(err.response?.status === 409 ? 'mailExistsError' : 'requestError'),
      );
      setShowSuccess(false);
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  return (
    <DialogWrapper
      headline={t('addFleetmanagerHeadline')}
      open={open}
      onClose={onClose}
      showSuccess={showSuccess}
      successMessage={t('fleetManagerInviteSuccess', {
        fleetManagerMail: email.value,
      })}
    >
      <>
        <Row>
          <InputWrapper fullWidth>
            <TextField
              autoFocus
              newDriver
              error={email.error}
              onFocus={() => setEmail({ ...email, error: false })}
              data-cy="emailInput"
              value={email.value}
              placeholder={t('emailPlaceholder')}
              label={t('email')}
              name="email"
              onChange={(e) => {
                setEmail({ ...email, value: e.target.value });
              }}
            />
            {email.error && (
              <Error visible={'true'}>{t('supportAdminEmailError')}</Error>
            )}
          </InputWrapper>
        </Row>
        <Row>
          <InputWrapper fullWidth>
            <Description error={language.error}>
              {t('inviteLanguage')}
            </Description>
            <Select
              big
              error={language.error}
              value={language.value}
              onFocus={() => setLanugage({ ...language, error: false })}
              onChange={(e) => {
                setLanugage({ ...language, value: e.target.value });
              }}
              displayEmpty
              renderValue={
                language.value !== ''
                  ? undefined
                  : () => (
                      <Placeholder>
                        {t('inviteLanguagePlaceholder')}
                      </Placeholder>
                    )
              }
            >
              {Object.keys(languages).map((entry) => (
                <StyledMenuItem
                  key={entry}
                  value={entry}
                  data-cy={`${entry}Select`}
                >
                  <CountryFlag country={entry} />
                  {tLocalization(`${entry}Language`)}
                </StyledMenuItem>
              ))}
            </Select>
          </InputWrapper>
        </Row>
        <Row>
          <InformationContainer>
            <InformationIcon>
              <img src={infoIcon} alt="success" />
            </InformationIcon>
            <InformationText>{t('salesAdminInviteInfo')}</InformationText>
          </InformationContainer>
        </Row>

        <CheckboxContainer>
          <Checkbox
            onClick={() => setConfirmation(!confirmation)}
            checked={confirmation}
          />
          <CheckboxText>{t('salesAdminInviteCheckboxText')}</CheckboxText>
        </CheckboxContainer>
        {isErrorVisible && (
          <ErrorContainer>
            <Error>{t(errorMessage)}</Error>
          </ErrorContainer>
        )}
        {isLoading ? (
          <Center>
            <CircularProgress />
          </Center>
        ) : (
          <ButtonWrapper>
            <Button
              disabled={!confirmation}
              onClick={addSupport}
              variant="primary"
            >
              {t('sendInvitation')}
            </Button>
          </ButtonWrapper>
        )}
      </>
    </DialogWrapper>
  );
};

AddSupportAdminDialog.propTypes = {
  open: PropTypes.bool,
  superUser: PropTypes.bool,
  onClose: PropTypes.func,
};

export default AddSupportAdminDialog;
