import { IconButton, Menu, MenuItem } from '@material-ui/core';
import { useState } from 'react';
import styled from 'styled-components';
import ActionDialog from './ActionDialog';
import MoreVertIcon from '@material-ui/icons/MoreVert';
import { useTranslation } from 'react-i18next';

const StyledMenuItem = styled(MenuItem)`
  font-size: 14px !important;
  &:hover {
    opacity: 0.7;
    background: none;
  }
`;

const FleetManagerActionsMenu = ({
  idpId,
  email,
  status,
  fmFunction,
  onClose,
  salesAdmin,
  support,
}) => {
  const [anchorEl, setAnchorEl] = useState(null);
  const [isActionDialogVisible, setIsActionDialogVisible] = useState(false);
  const [action, setAction] = useState(null);
  const { t } = useTranslation('serviceProvider');
  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = (e) => {
    setAnchorEl(null);
  };

  const open = Boolean(anchorEl);
  return (
    <div>
      <ActionDialog
        open={isActionDialogVisible}
        action={action}
        email={email}
        salesAdmin={salesAdmin}
        support={support}
        idpId={idpId}
        onClose={() => {
          setIsActionDialogVisible(false);
          window.location.reload();
          onClose();
        }}
      />
      <IconButton
        aria-controls="basic-menu"
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        onClick={handleClick}
        data-cy="Menu"
      >
        <MoreVertIcon />
      </IconButton>
      <Menu
        disableScrollLock
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        getContentAnchorEl={null}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        {(status === 'active' || status === 'invited') &&
          fmFunction === 'admin' &&
          !salesAdmin &&
          !support && (
            <StyledMenuItem
              disableRipple
              onClick={() => {
                setAction('demote');
                setIsActionDialogVisible(true);
                handleClose();
              }}
            >
              {t('demoteAction')}
            </StyledMenuItem>
          )}
        {(status === 'active' || status === 'invited') &&
          fmFunction === 'operative' && (
            <StyledMenuItem
              disableRipple
              onClick={() => {
                setAction('promote');
                setIsActionDialogVisible(true);
                handleClose();
              }}
            >
              {t('promoteAction')}
            </StyledMenuItem>
          )}
        {(status === 'active' || status === 'invited') && (
          <StyledMenuItem
            disableRipple
            onClick={() => {
              setAction('deactivate');
              setIsActionDialogVisible(true);
              handleClose();
            }}
          >
            {t('deactivateAction')}
          </StyledMenuItem>
        )}
        {status === 'inactive' && (
          <StyledMenuItem
            disableRipple
            onClick={() => {
              setAction('reinvite');
              setIsActionDialogVisible(true);
              handleClose();
            }}
          >
            {t('reinviteAction')}
          </StyledMenuItem>
        )}
      </Menu>
    </div>
  );
};

export default FleetManagerActionsMenu;
