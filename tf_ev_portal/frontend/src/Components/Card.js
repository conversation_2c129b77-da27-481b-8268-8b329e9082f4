import PropTypes from 'prop-types';
import { isMobile } from 'react-device-detect';
import styled from 'styled-components';

const Wrapper = styled.div`
  border-radius: ${(props) => (props.isMobile ? '' : '10px !important')};
  background: ${(props) => (props.isMobile ? '' : '#fff')};
  margin-bottom: '4rem';
  box-shadow: ${(props) =>
    props.isMobile ? '' : '0 0 40px 0 rgba(0, 0, 0, 0.16)'};
  padding: ${(props) => (props.padding ? props.padding : 0)};
`;

const Card = ({ padding, children }) => {
  return (
    <Wrapper padding={padding} isMobile={isMobile}>
      {children}
    </Wrapper>
  );
};

Card.defaultProps = {
  padding: '2rem',
};

Card.propTypes = {
  padding: PropTypes.string,
  children: PropTypes.oneOfType([
    PropTypes.element,
    PropTypes.arrayOf(PropTypes.element),
  ]).isRequired,
};

export default Card;
