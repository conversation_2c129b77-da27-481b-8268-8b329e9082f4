import { useTranslation } from 'react-i18next';
import { StatusIndicator } from './dashboard/RowTemplates';

const ReimbursementStatus = ({ reimbursementStatusId }) => {
  const { t } = useTranslation('directCustomer');
  return (
    <>
      {reimbursementStatusId === 2 ? (
        <StatusIndicator color="#090" background="rgba(153, 204, 0, 0.27)">
          {t('statusActive')}
        </StatusIndicator>
      ) : reimbursementStatusId === 3 ? (
        <StatusIndicator background="rgba(255, 196, 32, 0.49)" color="#ff7e00">
          {t('statusCanceled')}
        </StatusIndicator>
      ) : (
        <StatusIndicator background="#dbdbdb" color="#323338">
          {t('statusInactive')}
        </StatusIndicator>
      )}
    </>
  );
};

export default ReimbursementStatus;
