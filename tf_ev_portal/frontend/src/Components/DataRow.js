import { memo } from 'react';
import { useTranslation } from 'react-i18next';
import { formatDateTime, secondsToHms } from '../utils/helper';
import { StatusDiv } from './dashboard/DashboardTemplates';
import { Row, RowBox, RowContainer } from './dashboard/RowTemplates';

export const Status = ({ statusId }) => {
  const { t } = useTranslation('overview');
  const yellowStatus = [1, 6, 7, 8];
  const greenStatus = [11];
  const whiteStatus = [12];
  let statusColor = '#e22525';
  if (yellowStatus.indexOf(statusId) !== -1) {
    statusColor = '#fbfb12';
  } else if (greenStatus.indexOf(statusId) !== -1) {
    statusColor = '#3fe03f';
  } else if (whiteStatus.indexOf(statusId) !== -1) {
    statusColor = '#FFFFFF';
  }

  return (
    <StatusDiv
      title={t(`reimbursementStatus${statusId}`)}
      style={{ background: `${statusColor}` }}
    />
  );
};

const DataRow = ({ rowData, ...rest }) => {
  return (
    <Row {...rest}>
      <RowContainer>
        <RowBox center width={7}>
          <Status statusId={rowData.cdr_status_id} />
        </RowBox>
        <RowBox center width={16}>
          {rowData.token_visual_number}
        </RowBox>
        <RowBox center width={8}>
          {rowData.expiry_date.split('-')[1] +
            '/' +
            rowData.expiry_date.split('-')[0].slice(-2)}
        </RowBox>
        <RowBox center width={19}>
          {rowData.evse_id}
        </RowBox>
        <RowBox center width={16}>
          {formatDateTime(rowData.session_start)}
        </RowBox>
        <RowBox center width={16}>
          {formatDateTime(rowData.session_end)}
        </RowBox>
        <RowBox center width={10}>
          {rowData.cdr_total_energy}
        </RowBox>
        <RowBox center width={8}>
          {secondsToHms(rowData.total_time)}
        </RowBox>
      </RowContainer>
    </Row>
  );
};

export default memo(DataRow);
