import { useState } from 'react';
import { Box } from '@material-ui/core';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import Dialog from '../Dialog';
import { <PERSON><PERSON>ield, Button, Error } from '..';
import requester from '../../utils/requester';
import Center from '../helper/Center';
import CircularProgress from '../CircularProgress';
import styled from 'styled-components';

const DialogContainer = styled(Box)`
  padding: 2rem;
  width: 100%;
  min-width: 400px;
  box-sizing: border-box;
  text-align: center;
  white-space: pre-wrap;
  :img {
    width: 50px;
    height: 50px;
  }
`;

const Headline = styled.div`
  font-size: 19px;
  font-weight: bold;
`;

const CompanyListWrapper = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  border-bottom: solid 1px var(--trafineo-grau-50);
  margin: 1rem;
  padding-bottom: 1rem;
`;

const CompanyListItem = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.5rem;
`;

const CompanyListItemEntry = styled.div`
  padding: 0.25rem;
  display: flex;
`;

const AddCompany = styled.div`
  display: flex;
  justify-content: center;
  align-items: flex-end;
  margin: 1rem;
`;

const InputWrapper = styled.div`
  width: 100px;
`;

const DialogWrapper = styled.div`
  position: relative;
  width: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  width: 500px;
  input {
    width: 500px;
    background-color: white;
    ::placeholder {
      color: grey;
      opacity: 1;
    }
  }
  button {
    width: fit-content;
    margin-left: 0.5rem;
  }
`;

const ActionDescription = styled.div`
  margin: 2rem 0;
  font-size: 16px;
  line-height: 24px;
`;

const ButtonWrapper = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
`;

const ManageCompanyDialog = ({ open, data, onClose, needConfirmation }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [issuerId, setIssuerId] = useState('');
  const [authId, setAuthId] = useState('');
  const [displayConfrimation, setDisplayConfirmation] = useState(false);
  const [currentCompanyId, setCurrentCompanyId] = useState(null);
  const { t } = useTranslation('actions');
  const tLocalization = useTranslation('superuser').t;
  const tService = useTranslation('serviceProvider').t;
  const tDriver = useTranslation('evDriver').t;

  const isInputValid = () => authId.length === 6 && issuerId.length === 6;

  const resetComponent = () => {
    setIssuerId('');
    setIsErrorVisible(false);
    setAuthId('');
    onClose();
  };

  const addCompanyId = async () => {
    if (isInputValid()) {
      setIsLoading(true);
      try {
        await requester().post(
          `/authorities/fleetmanagers/${data.IdpId}/companies`,
          { companyId: `${issuerId}${authId}` },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        data.companies.push({
          companyId: `${issuerId}${authId}`,
        });
        setAuthId('');
        setIssuerId('');
        setIsErrorVisible(false);
        setDisplayConfirmation(false);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsErrorVisible(true);
        setDisplayConfirmation(false);
        setIsLoading(false);
      }
    }
  };

  const removeCompanyId = async (companyId) => {
    setIsLoading(true);
    try {
      await requester().delete(
        `/authorities/fleetmanagers/${data.IdpId}/companies/${companyId}`,
      );
      data.companies = data.companies.filter((e) => e.companyId !== companyId);
      setIsErrorVisible(false);
      setDisplayConfirmation(false);
      setIsLoading(false);
    } catch (error) {
      console.error(error);
      setIsErrorVisible(true);
      setDisplayConfirmation(false);
      setIsLoading(false);
    }
  };

  const showConfirmation = (companyId) => {
    if (!companyId) {
      companyId = `${issuerId}${authId}`;
      setDisplayConfirmation('add');
    } else {
      setDisplayConfirmation('remove');
    }
    setCurrentCompanyId(companyId);
  };

  const closeConfirmation = () => {
    setDisplayConfirmation(false);
    setCurrentCompanyId(null);
  };

  const CompanyList = () => {
    return (
      <CompanyListWrapper>
        {data.companies.map((e) => {
          return (
            <CompanyListItem key={e.companyId}>
              <CompanyListItemEntry>
                {e.companyId.slice(0, 6)}
              </CompanyListItemEntry>
              <CompanyListItemEntry>
                {e.companyId.slice(6, 12)}
              </CompanyListItemEntry>
              <Button
                onClick={() => {
                  needConfirmation
                    ? showConfirmation(e.companyId)
                    : removeCompanyId(e.companyId);
                }}
                variant="smallSec"
              >
                {t('remove')}
              </Button>
            </CompanyListItem>
          );
        })}
      </CompanyListWrapper>
    );
  };

  return (
    <Dialog open={open} onClose={resetComponent}>
      <DialogContainer data-cy="container">
        <Headline>{tLocalization('assignedComanyIds')}</Headline>
        <CompanyList />
        <Headline>{tLocalization('addCompanyId')}</Headline>
        <AddCompany>
          {(isLoading && (
            <Center>
              <CircularProgress />
            </Center>
          )) || (
            <>
              <Dialog open={displayConfrimation} onClose={closeConfirmation}>
                <DialogWrapper data-cy="container">
                  {displayConfrimation === 'remove' && (
                    <ActionDescription>
                      {t('supportRemoveCompany', {
                        companyId: currentCompanyId,
                      })}
                    </ActionDescription>
                  )}

                  {displayConfrimation === 'add' && (
                    <ActionDescription>
                      {t('supportAddCompany', {
                        companyId: currentCompanyId,
                      })}
                    </ActionDescription>
                  )}

                  <ButtonWrapper>
                    <Button onClick={closeConfirmation} variant="secondary">
                      {tService(`cancelButton`)}
                    </Button>
                    <Button
                      onClick={() =>
                        displayConfrimation === 'remove'
                          ? removeCompanyId(currentCompanyId)
                          : addCompanyId(currentCompanyId)
                      }
                      variant="primary"
                    >
                      {displayConfrimation === 'remove'
                        ? t('remove')
                        : t(`confirm`)}
                    </Button>
                  </ButtonWrapper>
                </DialogWrapper>
              </Dialog>
              <InputWrapper>
                <TextField
                  value={issuerId}
                  onChange={(e) => {
                    const { value } = e.target;
                    if (value.match(/^\d+$/)) {
                      setIssuerId(value);
                    }
                  }}
                  type="text"
                  maxlength="6"
                  tiny
                  label={tLocalization('issuerId')}
                />
              </InputWrapper>
              <InputWrapper>
                <TextField
                  value={authId}
                  onChange={(e) => {
                    const { value } = e.target;
                    if (value.match(/^\d+$/)) {
                      setAuthId(value);
                    }
                  }}
                  type="text"
                  maxlength="6"
                  tiny
                  label={tLocalization('authId')}
                />
              </InputWrapper>
              <Button
                onClick={() => {
                  needConfirmation ? showConfirmation() : addCompanyId();
                }}
                disabled={!isInputValid()}
                variant="smallMargin"
              >
                {tDriver('add')}
              </Button>
            </>
          )}
        </AddCompany>
        <Center>
          <Error visible={isErrorVisible} text={t('generalRequestError')} />
        </Center>
      </DialogContainer>
    </Dialog>
  );
};

ManageCompanyDialog.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.object,
  onClose: PropTypes.func,
};

export default ManageCompanyDialog;
