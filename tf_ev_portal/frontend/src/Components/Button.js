import { makeStyles } from '@material-ui/core/styles';
import { ButtonBase } from '@material-ui/core';
import PropTypes from 'prop-types';

const Button = ({
  variant,
  expand,
  disabled,
  customColor,
  fontSize,
  children,
  isInCardOverview,
  isMobile,
  ...rest
}) => {
  const useStyles = makeStyles({
    base: {
      textDecoration: 'none !important',
      height: 38,
      width: '100%',
      boxShadow: !expand ? 'var(--button-box-shadow)' : 'none',
      border: 'solid 1px var(--trafineo-rot-100)',
      color: 'var(--trafineo-rot-100)',
      padding: '0 1rem',
      outline: 'none !important',
      willChange: 'filter',
      borderRadius: 'var(--button-border-radius)',
      transition: 'filter 0.25s ease-in-out',
      '&:hover': {
        filter: 'brightness(90%)',
      },
      '&:focus': {
        filter: 'brightness(90%)',
      },
      whiteSpace: 'pre-wrap',
    },
    secondary: {
      color: 'var(--trafineo-rot-100)',
      backgroundColor: expand ? 'transparent' : '#ffffff',
    },
    next: {
      backgroundColor: 'var(--trafineo-rot-100)',
      color: '#ffffff',
      minWidth: 100,
      maxWidth: 200,
      width: 'auto',
      marginLeft: '1rem',
    },
    prev: {
      color: 'var(--trafineo-rot-100)',
      backgroundColor: '#ffffff',
      width: 100,
    },
    primary: {
      color: '#fff',
      backgroundColor: 'var(--trafineo-rot-100)',
    },
    tertiary: {
      backgroundColor: '#acadae',
      color: '#fff',
      border: '1px solid #acadae',
      boxShadow: '0 0 16px 0 rgba(172,173,174, 0.14)',
    },
    disabled: {
      background: '#e7e7e7 !important',
      color: '#acadae !important',
      border: expand ? '1px solid #acadae' : 'none',
      boxShadow: '0 0 16px 0 rgba(172,173,174, 0.14)',
      '&:hover': {
        filter: 'brightness(100%)',
      },
      cursor: 'disabled',
    },
    special: {
      border: '1px solid #2196f3',
      color: '#2196f3',
      boxShadow: '0 0 16px 0 rgba(33,150,243, 0.14)',
    },
    small: {
      width: 100,
      height: 24,
      fontSize: '12px !important',
      color: '#fff',
      backgroundColor: 'var(--trafineo-rot-100)',
    },
    link: {
      fontSize: '14px !important',
      color: '#0064cc',
      background: 'none !important',
      boxShadow: 'none !important',
      padding: 0,
      fontWeight: 'normal',
      border: 0,
      textDecoration: 'underline',
      width: 'max-content',
      margin: '0.25rem',
    },
    linkRed: {
      fontSize: '14px !important',
      color: '#FF3B30',
      background: 'none !important',
      boxShadow: 'none !important',
      padding: 0,
      fontWeight: 'normal',
      border: 0,
      textDecoration: 'underline',
      width: 'max-content',
      margin: '0.25rem',
    },
    smallMargin: {
      width: 100,
      marginBottom: '8px',
      height: '28px',
      fontSize: '12px !important',
      color: '#fff',
      backgroundColor: 'var(--trafineo-rot-100)',
    },
    smallSec: {
      width: 100,
      height: 24,
      fontSize: '12px !important',
      color: 'var(--trafineo-rot-100)',
      backgroundColor: '#fff',
    },
    cardOverview: {
      height: 'var(--button-height)',
    },
    mobile: {
      width: '50%',
    },
    grey: {
      height: '34px',
      boxShadow: 'none !important',
      borderRadius: '23.5px',
      border: 'solid 0.5px #707070',
      color: '#707070',
      fontSize: '12px',
      fontWeight: 'normal',
      '&:hover': {
        filter: 'brightness(150%)',
      },
    },
    none: {
      display: 'none',
    },
  });
  // variants: primary, secondary, tertiary, special, disabled
  const classes = useStyles();

  return (
    <ButtonBase
      className={`${classes.base} ${classes[variant]} ${
        isMobile && classes.mobile
      } ${disabled ? classes.disabled : ''} ${
        isInCardOverview ? classes.cardOverview : ''
      }`}
      style={
        customColor
          ? { border: `solid 1px ${customColor}`, color: customColor, fontSize }
          : { fontSize: '12px' }
      }
      {...rest}
      disabled={disabled}
    >
      {children}
    </ButtonBase>
  );
};

Button.defaultProps = {
  variant: 'secondary',
  disabled: false,
  customColor: '',
  fontSize: '13px',
};

Button.propTypes = {
  isInCardOverview: PropTypes.bool,
  expand: PropTypes.bool,
  variant: PropTypes.string,
  disabled: PropTypes.bool,
  isMobile: PropTypes.bool,
  customColor: PropTypes.string,
  fontSize: PropTypes.string,
  children: PropTypes.oneOfType([PropTypes.element, PropTypes.string])
    .isRequired,
};

export default Button;
