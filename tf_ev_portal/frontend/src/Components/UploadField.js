import { useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@material-ui/styles';
import classNames from 'classnames';
import Button from './Button';
import FolderIcon from '../static/img/icon-folder.svg';
import DownIcon from '../static/img/icon-down.svg';
import Circle from './CircularProgress';
import statusIcon from '../static/img/icons/Icon_Done.svg';
import logger from '../utils/logger';
import { isMobile } from 'react-device-detect';

const useStyles = makeStyles({
  disabled: {
    color: '#acadae',
    border: 'none !important',
    background: '#e7e7e7',
    boxShadow: '0 0 16px 0 rgba(172,173,174, 0.14)',
  },
  uploadContainer: {
    minWidth: isMobile ? 0 : 500,
    height: 100,
    borderWidth: '1px',
    borderStyle: 'dashed',
    borderColor: '#c4c4c5',
    borderRadius: 7,
  },
  inputLabel: {
    width: '100%',
    fontFamily: 'var(--font-family)',
    cursor: 'pointer',
    textAlign: 'left',
    paddingLeft: '0.5rem',
    fontWeight: 400,
  },
  inputDisabled: {
    width: 'calc(100% - 2rem)',
    cursor: 'disabled',
    padding: '1rem',
  },
  hiddenInput: {
    display: 'none',
  },
  fileNameContainer: {
    display: 'flex',
    padding: isMobile ? '1rem' : '0.25rem',
    justifyItems: 'center',
    alignItems: 'center',
    backgroundColor: '#f7f7f7',
    border: '1px solid #ebebeb',
    minWidth: isMobile ? 125 : 250,
  },
  folderIcon: {
    height: 16,
    display: 'inline-block',
    verticalAlign: 'bottom',
    marginRight: '0.5rem',
    marginLeft: '0.5rem',
  },
  folderIconDisabled: {
    opacity: 0.4,
    filter: 'alpha(opacity=40)' /* msie */,
  },
  filename: {
    width: isMobile ? '' : '100%',
    display: 'inline-block',
    fontSize: '13px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    textAlign: 'center',
    opacity: 0.7,
  },
  dragAndDrop: {
    width: '100%',
    fontSize: '13px',
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    textAlign: 'center',
    opacity: 0.7,
  },
  orLine: {
    width: '97%',
    textAlign: 'center',
    borderBottom: '1px solid #000',
    opacity: 0.7,
    lineHeight: '0.07em',
    margin: '10px 0 10px',
    '& span': {
      background: '#f7f7f7',
      fontSize: '13px',
      padding: '0 7px',
    },
  },
  orLineDisabled: {
    width: '97%',
    textAlign: 'center',
    borderBottom: '1px solid #C4C5C5',
    opacity: 0.7,
    lineHeight: '0.07em',
    margin: '10px 0 10px',
    '& span': {
      background: '#f7f7f7',
      fontSize: '13px',
      padding: '0 7px',
    },
  },
  uploadStatusContainer: {
    height: 38,
    width: '100%',
    display: 'flex',
    justifyContent: 'flex-end',
    alignItems: 'center',
    fontSize: '14px',
  },
  uploadStatusText: {
    color: 'var(--default-text);',
    width: isMobile ? '80px' : 'auto',
  },
  uploadStatusTextError: {
    color: 'var(--error-color)',
  },
  uploadStatusIcon: {
    height: '25px',
    marginLeft: '0.5rem',
  },
  uploadWrapper: {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '100%',
  },
});

/**
 * UploadField offers 2 ways for selecting the file to be uploaded: Drag and Drop and a File Input Field.
 * Only files with valid file type and size can be selected. When the file is selected, its name is visible
 * in the input field and it can be uploaded by hitting the upload button.
 */

const DEFAULT_MAX_FILE_SIZE = 1048576;

const UploadField = ({
  onUpload,
  uploadText,
  fileTypes,
  fileNames,
  selectedFileName,
  disabled,
  maxFileSize,
  multiFile,
  fileErrorMessage,
}) => {
  const classes = useStyles();
  const { t } = useTranslation('actions');
  const [uploadState, setUploadState] = useState('select');
  const [file, setFile] = useState(null);

  disabled = false;

  const uploadFileMulti = async (fileToUpload) => {
    if (fileToUpload) {
      setUploadState('uploading');
      try {
        await onUpload(fileToUpload);

        setUploadState('select');
        setFile(null);
      } catch (err) {
        setUploadState('select');
        setFile(null);
      }
    }
  };

  const uploadFile = async () => {
    if (file) {
      setUploadState('uploading');
      try {
        await onUpload(file);
        setUploadState('done');
      } catch (err) {
        setUploadState('select');
        setFile(null);
      }
    }
  };

  const handleDragOver = (e) => {
    if (!disabled) {
      e.preventDefault();
      e.stopPropagation();
      if (
        e.dataTransfer?.types?.length === 1 &&
        e.dataTransfer.types[0] === 'Files'
      ) {
        e.dataTransfer.dropEffect = 'copy';
      }
    }
  };

  const validateFile = (selectedFile) => {
    if (!fileTypes[selectedFile.type]) {
      if (!selectedFile.type) {
        const type = selectedFile.name.split('.')[1];
        if (type !== 'heic' && type !== 'heif') {
          logger().error('incorrect filetyoe');
          setUploadState('errorfiletype');
          setFile(null);
          return;
        }
      } else {
        logger().error('incorrect filetyoe');
        setUploadState('errorfiletype');
        setFile(null);
        return;
      }
    }
    if (multiFile && fileNames[selectedFile.name]) {
      logger().error('same file uploaded');
      setUploadState('errorSameFile');
      setFile(null);
      return;
    }
    if (!(selectedFile.size <= maxFileSize)) {
      logger().error('File too large');
      setUploadState('errorfilesize');
      setFile(null);
      return;
    }
    setUploadState('select');
    setFile(selectedFile);
    if (multiFile) {
      uploadFileMulti(selectedFile);
    }
    return;
  };

  const handleDrop = (e) => {
    if (!disabled) {
      e.preventDefault();
      e.stopPropagation();
      const droppedFile = e.dataTransfer?.files?.[0];

      if (droppedFile) {
        validateFile(droppedFile);
      }
    }
  };

  const selectFile = (e) => {
    const selectedFile = e.target?.files?.[0];
    validateFile(selectedFile);
  };

  let button;
  switch (uploadState) {
    default:
      button = multiFile ? (
        <></>
      ) : (
        <Button
          data-cy="fileButton"
          disabled={!file}
          variant="next"
          onClick={uploadFile}
        >
          {uploadText}
        </Button>
      );
      break;
    case 'errorfilesize':
      button = (
        <div data-cy="errorfilesize" className={classes.uploadStatusContainer}>
          <span className={classes.uploadStatusTextError}>
            {multiFile ? fileErrorMessage : t('uploadFileTooLarge')}
          </span>
        </div>
      );
      break;
    case 'errorSameFile':
      button = (
        <div data-cy="errorSameFile" className={classes.uploadStatusContainer}>
          <span className={classes.uploadStatusTextError}>
            {t('errorSameFile')}
          </span>
        </div>
      );
      break;
    case 'errorfiletype':
      button = (
        <div data-cy="errorfiletype" className={classes.uploadStatusContainer}>
          <span className={classes.uploadStatusTextError}>
            {t('uploadFileInvalidFileType')}
          </span>
        </div>
      );
      break;
    case 'uploading':
      button = (
        <div data-cy="uploading" className={classes.uploadStatusContainer}>
          <Circle />
        </div>
      );
      break;
    case 'done':
      button = (
        <div
          data-cy="uploadSuccessful"
          className={classes.uploadStatusContainer}
        >
          <span className={classes.uploadStatusText}>
            {t('uploadSuccessful')}
          </span>
          <img
            src={statusIcon}
            alt="success"
            className={classes.uploadStatusIcon}
          />
        </div>
      );
      break;
  }

  return (
    <div
      className={classes.uploadContainer}
      onDrop={handleDrop}
      onDragOver={handleDragOver}
    >
      <div className={classes.uploadWrapper}>
        <div
          className={classNames(
            classes.fileNameContainer,
            disabled && classes.disabled,
          )}
        >
          <label
            className={
              (!disabled && classes.inputLabel) || classes.inputDisabled
            }
          >
            <input
              data-cy="fileInput"
              disabled={disabled}
              type="file"
              accept={Object.keys(fileTypes).join(',')}
              onChange={(e) => selectFile(e)}
              onClick={(event) => {
                event.target.value = null;
              }}
              className={classes.hiddenInput}
            />
            {!isMobile && (
              <>
                <div className={classes.dragAndDrop}>
                  {t('dragAndDrop')}
                  <img
                    src={DownIcon}
                    alt="Folder-Icon"
                    className={classNames(
                      classes.folderIcon,
                      disabled && classes.folderIconDisabled,
                    )}
                  />
                </div>
                <div
                  className={classNames(
                    classes.orLine,
                    disabled && classes.orLineDisabled,
                  )}
                  disabled={disabled}
                >
                  <span>{t('or')}</span>
                </div>
              </>
            )}
            <div className={classes.filename}>
              {selectedFileName || file?.name || t('openFileExplorer')}
              <img
                src={FolderIcon}
                alt="Folder-Icon"
                className={classNames(
                  classes.folderIcon,
                  disabled && classes.folderIconDisabled,
                )}
              />
            </div>
          </label>
        </div>
        <div>{button}</div>
      </div>
    </div>
  );
};

UploadField.defaultProps = {
  onUpload: () => {},
  uploadText: '',
  selectedFileName: '',
  disabled: false,
  maxFileSize: DEFAULT_MAX_FILE_SIZE,
};

UploadField.propTypes = {
  onUpload: PropTypes.func,
  uploadText: PropTypes.string,
  fileTypes: PropTypes.object.isRequired,
  selectedFileName: PropTypes.string,
  disabled: PropTypes.bool,
  fileNames: PropTypes.object,
  maxFileSize: PropTypes.number,
};

export default UploadField;
