import { useRef, useEffect, useState } from 'react';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Document, Page } from 'react-pdf';
import CircularProgress from './CircularProgress';
import styled from 'styled-components';

import AralLogo from '../static/img/Aral_Logo_M40_4C_transp.png';
import { isMobile } from 'react-device-detect';

const BradnIconPopUp = styled.div`
  position: absolute;
  width: 1070px;
  text-align: right;
  z-index: 99;
  margin-top: 80px;
`;

const BrandIconDisplay = styled.div`
  position: absolute;
  display: inline-flex;
  flex-direction: row-reverse;
  width: 1100px;
  top: ${(props) =>
    props.isMobile ? (props.isLandscape ? '4.3%' : '215px') : '17vh'};
`;

const BrandIcon = styled.img`
  height: 125px;
  width: 125px;
`;

const Wrapper = styled.div`
  position: relative;
`;

const PdfRenderer = ({ src, inDialog }) => {
  const useStyles = makeStyles({
    loadingWraper: {
      color: 'var(--trafineo-rot-100)',
      height: inDialog ? '100%' : 'calc(100vh - 175px)',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      position: 'relative',
    },
    document: {
      '& canvas': {
        margin: '0 auto',
      },
    },
  });

  const ref = useRef(null);

  const { t } = useTranslation('actions');
  const [pageNumber, setPageNumber] = useState();
  const [file, setFile] = useState(src);

  const onDocumentLoadSuccess = ({ numPages }) => {
    setPageNumber(numPages);
  };

  const classes = useStyles();

  const getOrientation = () => window.screen.orientation.type;

  const useScreenOrientation = () => {
    const [orientation, setOrientation] = useState(getOrientation());

    const updateOrientation = (event) => {
      setOrientation(getOrientation());
    };

    useEffect(() => {
      window.addEventListener('orientationchange', updateOrientation);
      return () => {
        window.removeEventListener('orientationchange', updateOrientation);
      };
    }, []);

    return orientation;
  };

  let isLandscape = useScreenOrientation().includes('landscape');

  useEffect(() => {
    if (src !== '' && src && src !== file) {
      setFile(src);
    }
  }, [src, file]);

  const disableDoubleView = (divsToHide, annotationHide) => {
    for (var i = 0; i < divsToHide.length; i++) {
      divsToHide[i].style.visibility = 'hidden';
      divsToHide[i].style.display = 'none';
    }
    for (var l = 0; l < annotationHide.length; l++) {
      annotationHide[l].style.visibility = 'hidden';
      annotationHide[l].style.display = 'none';
    }
  };

  setTimeout(function () {
    const divsToHide = document.querySelectorAll(
      '.react-pdf__Page__textContent textLayer',
    );

    const annotationHide = document.querySelectorAll(
      '.react-pdf__Page__annotations annotationLayer',
    );
    disableDoubleView(divsToHide, annotationHide);
  }, 5000);

  if (src === '') {
    return (
      <div className={classes.loadingWraper}>
        <CircularProgress />
      </div>
    );
  }

  const Pages = () => {
    if (!pageNumber) {
      return <></>;
    }
    const pages = [];
    if (src === '') {
      return (
        <div className={classes.loadingWraper}>
          <CircularProgress />
        </div>
      );
    }
    for (let i = 1; i <= pageNumber; i += 1) {
      pages.push(
        <Page
          scale={2}
          pageNumber={i}
          renderAnnotationLayer={false}
          renderTextLayer={false}
        />,
      );
    }
    return pages;
  };
  return (
    <Wrapper ref={ref}>
      <Document
        className={classes.document}
        renderMode="svg"
        file={`/${src}`}
        error={
          <div className={classes.loadingWraper}>
            <div>{t('generalRequestError')}</div>
          </div>
        }
        loading={
          <div className={classes.loadingWraper}>
            <CircularProgress />
          </div>
        }
        onLoadSuccess={onDocumentLoadSuccess}
        options={{ workerSrc: 'pdf.worker.js' }}
      >
        {/* Icons only exists here because react-pdf interprets the icon in the actual PDF as text and makes it black. The original PDFs are used for the download itself */}
        {src.includes('privacy') &&
          !src.includes('hint') &&
          window.location.pathname.includes('changePassword') && (
            <BradnIconPopUp isMobile={isMobile} isLandscape={isLandscape}>
              <BrandIcon src={AralLogo} alt="Aral-Logo" />
            </BradnIconPopUp>
          )}
        <Pages />
        {src.includes('privacy') &&
          !src.includes('hint') &&
          !window.location.pathname.includes('changePassword') && (
            <BrandIconDisplay isMobile={isMobile} isLandscape={isLandscape}>
              <BrandIcon src={AralLogo} alt="Aral-Logo" />
            </BrandIconDisplay>
          )}
      </Document>
    </Wrapper>
  );
};

PdfRenderer.propTypes = {
  src: PropTypes.string.isRequired,
};

export default PdfRenderer;
