import styled from 'styled-components';
import Button from '@material-ui/core/Button';
import { useTranslation } from 'react-i18next';

import ArrowBackIcon from '@material-ui/icons/ArrowBack';
import { isMobile } from 'react-device-detect';

const ButtonContainer = styled.div`
  display: flex;
  margin: ${isMobile ? '0.5rem 0 0 1rem' : '1rem 0'};
  button {
    text-transform: none;
    color: var(--trafineo-rot-100);
    font-size: 12px;
    height: 38px;
    border: none;
    text-align: left;
    padding: 0;
    .MuiButton-label {
      justify-content: left;
    }
    &:hover {
      opacity: 0.7;
      background: none;
    }
  }
`;

const BackButton = ({ backFuction }) => {
  const { t } = useTranslation('actions');
  return (
    <ButtonContainer>
      <Button
        disableRipple
        variant="outlined"
        onClick={backFuction ? backFuction : () => window.history.back()}
        startIcon={<ArrowBackIcon />}
      >
        {t('prev')}
      </Button>
    </ButtonContainer>
  );
};

export default BackButton;
