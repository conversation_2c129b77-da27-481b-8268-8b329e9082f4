import { Box } from '@mui/material';
import jwtDecode from 'jwt-decode';
import PropTypes from 'prop-types';
import { useContext, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';
import { Md5 } from 'ts-md5';
import { userContext } from '../ContextProvider';
import requester from '../utils/requester';
import Button from './Button';
import CircularProgress from './CircularProgress';
import { ButtonWrapper, Error } from './evDriver/PageTemplate';
import SuccessDialog from './SuccessDialog';
import TagManager from 'react-gtm-module';
import styled from 'styled-components';

const Spacer = styled.div`
  margin: 0;
  padding: 0;
  height: 1px;
  width: 5px;
`;

const EditWrapper = ({
  type,
  onCancel,
  beforeSafe,
  redirect,
  disabled,
  data,
  children,
  needsApproval,
  noButton = false,
}) => {
  const { t: ta } = useTranslation('actions');
  const { t } = useTranslation('evDriver');
  const [isLoading, setIsLoading] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [error, setError] = useState(undefined);
  const { user } = useContext(userContext);

  let email = '';
  try {
    email = jwtDecode(user.access_token).preferred_username;
  } catch (e) {
    email = '';
  }

  const handleSuccess = async () => {
    setError(undefined);
    setIsDialogVisible(true);
    setIsLoading(false);
  };
  const handleSave = async () => {
    if (beforeSafe() && data) {
      setIsLoading(true);
      setError(undefined);
      try {
        await requester()({
          method: 'patch',
          url: '/Ev_Driver_Data/Patch_Ev_Driver_Data',
          data: data,
        });
        setIsDialogVisible(true);
      } catch (e) {
        console.error(e);
        setError(ta('generalRequestError'));
      }
      setIsLoading(false);
    }
  };

  const handleError = (error) => {
    if (error.response?.data?.errorCode === 'tariffStartDateOverlapping') {
      setError(ta(error.response?.data?.errorCode));
    } else {
      setError(
        ta(
          error.response && error.response.status === 406
            ? 'infectedFileError'
            : 'generalRequestError',
        ),
      );
    }
    setIsLoading(false);
  };

  const handleCancel = () => {
    onCancel();
  };

  const handleApproval = async () => {
    const { files, tariff } = data;
    const processHash = Md5.hashStr(new Date().getTime() + email);
    let requestCount = 0;
    setIsLoading(true);
    const sendDataForApproval = async () => {
      const dataForApproval = {
        correctness_confirmation: true,
        portal_process_id: processHash,
        tariff: tariff,
      };
      if (type === 'tariff') {
        TagManager.dataLayer({
          dataLayer: {
            event: 'tariff_submit',
            tariff_submit: {
              number_of_tariffs: tariff.tariff_elements.length,
              phase: 'Update',
            },
          },
        });
      }

      try {
        await requester()({
          method: 'post',
          url: '/Ev_Driver_Data/Post_Ev_Driver_Data',
          data: dataForApproval,
        });
        handleSuccess();
      } catch (e) {
        handleError(e);
        return;
      }
    };

    const uploadFile = async (file) => {
      try {
        var bodyFormData = new FormData();
        bodyFormData.append('file', file.blobData);
        bodyFormData.append('upload_type_id', 2);
        bodyFormData.append('portal_process_id', processHash);
        bodyFormData.append('filename', encodeURIComponent(file.name));
        await requester()({
          method: 'post',
          url: '/FileUpload',
          data: bodyFormData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        requestCount = requestCount + 1;
        if (requestCount === files.length) {
          sendDataForApproval();
        } else {
          uploadFile(files[requestCount]);
        }
      } catch (err) {
        handleError(err);
      }
    };
    if (files.length > 0) {
      uploadFile(files[0]);
    }
  };

  return (
    <Box>
      {isDialogVisible && (
        <SuccessDialog
          isVisible
          onClose={() =>
            redirect
              ? window.location.replace(redirect)
              : window.location.reload()
          }
          text={!needsApproval ? t('submitSuccess') : t('successTextApproval')}
        />
      )}
      {children}
      <Box mb="1rem">{error && <Error>{error}</Error>}</Box>

      <ButtonWrapper isMobile={isMobile}>
        {isLoading ? (
          <CircularProgress />
        ) : (
          <>
            {onCancel && (
              <>
                <Button onClick={handleCancel} variant="secondary">
                  {t('cancel')}
                </Button>
                <Spacer />
              </>
            )}

            <Button
              disabled={disabled}
              onClick={needsApproval ? handleApproval : handleSave}
              variant={noButton ? 'none' : 'primary'}
            >
              {t('save')}
            </Button>
          </>
        )}
      </ButtonWrapper>
    </Box>
  );
};

export default EditWrapper;

EditWrapper.defaultProps = {
  beforeSafe: undefined,
  disabled: false,
  data: undefined,
  redirect: undefined,
  needsApproval: false,
};

EditWrapper.propTypes = {
  disabled: PropTypes.bool,
  needsApproval: PropTypes.bool,
  beforeSafe: PropTypes.func,
  data: PropTypes.object,
  redirect: PropTypes.string,
};
