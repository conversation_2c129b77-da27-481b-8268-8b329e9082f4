import styled from 'styled-components';
import MuiSelect from '@material-ui/core/Select';
import { makeStyles } from '@material-ui/core/styles';
import PropTypes from 'prop-types';
import { ExpandMore } from '@material-ui/icons';
import classNames from 'classnames';

const BigExpandIcon = styled(ExpandMore)`
  color: var(--default-text);
  transform: scale(1.3);
`;

const useStyles = makeStyles({
  root: {
    border: 'none ',
    fontSize: '12px',
    outline: 'none',
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: 'solid 1px var(--default-text)',
    },
    '& .MuiSelect-select': {
      outline: 'none',
      padding: '0 2rem',
      '&:focus': {
        backgroundColor: '#ffffff',
      },
    },
    '& fieldset': {
      borderRadius: 'var(--button-border-radius)',
      border: 'solid 1px var(--default-text)',
      outline: 'none !important',
    },
    '& .MuiSelect-selectMenu': {
      borderRadius: 'var(--button-border-radius)',
      backgroundColor: '#ffffff',
      display: 'flex',
      justifyContent: 'center',
      minWidth: 60,
      alignItems: 'center',
      height: '38px',
    },
  },
  list: {
    padding: '0 ',
    '& option': {
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      boxSizing: 'border-box',
      height: '36px',
      borderBottom: 'solid 1px var(--default-text)',
      fontSize: '12px',
      '&:last-child': {
        border: 'none',
      },
      '&:hover': {
        backgroundColor: 'var(--trafineo-rot-20)',
        color: 'var(--light-background-text-color)',
        cursor: 'pointer',
      },
    },
  },
  paper: {
    boxShadow: '0 0 20px 0 rgba(0, 0, 0, 0.16)',
    boxSizing: 'border-box',
    border: 'solid 1px var(--default-text)',
    padding: 0,
    backgroundColor: '#ffffff',
    borderRadius: 0,
    marginTop: 8,
  },
});

const useBigStyles = makeStyles({
  root: {
    opacity: 0.7,
    fontSize: '14px',
    width: '100%',
    '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
      border: 'solid 1px var(--default-text)',
    },
    '& .MuiSelect-select': {
      padding: '0 0.5rem',
    },
    '& fieldset': {
      borderRadius: '0',
      boxShadow: 'none',
      border: 'solid 1px #EBEBEB',

      '&:focus': {
        border: '1px solid yellow !important',
      },
    },
    '& .MuiSelect-selectMenu': {
      justifyContent: 'flex-start',
      height: '46px',
    },
  },
  list: {
    '& option': {
      justifyContent: 'flex-start',
      height: '46px',
      fontSize: '14px',
    },
  },
});

const Select = ({ big, children, ...props }) => {
  const classes = useStyles();
  const bigClasses = useBigStyles();

  return (
    <MuiSelect
      IconComponent={big ? BigExpandIcon : ExpandMore}
      MenuProps={{
        classes: {
          list: classNames(classes.list, big && bigClasses.list),
          paper: classes.paper,
        },
        anchorOrigin: {
          vertical: 'bottom',
          horizontal: 'left',
        },
        transformOrigin: {
          vertical: 'top',
          horizontal: 'left',
        },
        getContentAnchorEl: null,
        disableScrollLock: true,
      }}
      className={classNames(classes.root, big && bigClasses.root)}
      variant="outlined"
      {...props}
    >
      {children}
    </MuiSelect>
  );
};

Select.defaultProps = {
  big: false,
};

Select.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
  big: PropTypes.bool,
};

export default Select;
