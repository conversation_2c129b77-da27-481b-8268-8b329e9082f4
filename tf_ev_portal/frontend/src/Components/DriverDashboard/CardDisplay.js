import { Link } from 'react-router-dom';
import styled from 'styled-components';
import CardStepper from './CardStepper';
import { useTranslation } from 'react-i18next';
import { userContext } from '../../ContextProvider';
import { useContext } from 'react';
import jwtDecode from 'jwt-decode';

const AccountLink = styled(Link)`
  color: black;
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  ::after {
    content: ' >';
  }
`;

const StyledLink = styled(Link)`
  position: absolute;
  bottom: 26px;
  left: 0px;
  color: var(--primary);
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  ::after {
    content: ' >';
  }
`;

const Wrapper = styled.div`
  position: relative;
  height: 100%;
`;

const AccountWrapper = styled.div`
  margin: 1rem 0;
`;

const CardDisplay = () => {
  const { t } = useTranslation('evDriver');
  const { user } = useContext(userContext);

  return (
    <Wrapper>
      <AccountWrapper>
        <AccountLink to="/myinformation/personalinformation">
          {t('updateAccountInformationLink')}
        </AccountLink>
      </AccountWrapper>
      <CardStepper />
      {jwtDecode(user.access_token).provider?.toLowerCase() === 'trafineo' && (
        <StyledLink to="/myinformation/mycards">{t('myCards')}</StyledLink>
      )}
    </Wrapper>
  );
};
export default CardDisplay;
