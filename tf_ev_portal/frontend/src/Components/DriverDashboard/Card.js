import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { StatusIndicator } from '../dashboard/RowTemplates';
import { isMobile } from 'react-device-detect';
import { getBranding } from '../../utils/helper';

import cardAral from '../../static/img/cardAral.png';
import cardBP from '../../static/img/cardBP.png';
import { useEffect, useRef, useState } from 'react';
import { useWindowHeight, useWindowWidth } from '@react-hook/window-size';

const CardItem = styled.div`
  background: ${(props) => (props.isTrafineo ? 'var(--primary)' : '')};
  width: 100%;
  min-width: ${(props) => (props.isTrafineo ? '175px' : '')};
  position: relative;
  aspect-ratio: 16/9;
  padding: 0;
`;

const CardImage = styled.img`
  width: 100%;
  position: absolute;
  z-index: 1;
  aspect-ratio: 16/9;
`;

const CardWrapper = styled.div`
  padding-right: ${(props) => (props.isMobile ? '0px' : '10px')};
`;

const CardLabel = styled.div`
  display: ${(props) => (props.isHidden ? 'none' : 'inline-block')};
  text-transform: uppercase;
  width: ${(props) => (props.width ? props.width : '50%')};
  color: white;
  font-size: 14px;
  line-height: 16px;
  margin: 0.25rem 0;
  z-index: 2;
  text-shadow: ${(props) => (props.isLast ? '' : '0.5px 0.5px 0.5px black')};
  top: -16px;
  position: ${(props) => (props.isLast ? 'relative' : 'static')};
`;

const CardLabelTrafineo = styled.div`
  text-transform: uppercase;
  color: white;
  font-size: 11px;
  line-height: 16px;
  margin: 0.25rem 0;
`;

const CardContent = styled.div`
  padding: ${(props) =>
    props.isMobile ? '30% 0.5rem 0.5rem 0.5rem' : '30% 0.5rem 0.5rem 0.5rem'};
  z-index: 2;
  position: relative;
`;

const StatusWrapper = styled.div`
  display: ${(props) => (props.hidden ? 'none' : '')};
`;

const Triangle = styled.div`
  position: absolute;
  width: 60px;
  height: 59px;
  background: conic-gradient(
    at 50% 50%,
    white 45deg,
    transparent 0,
    transparent 225.5deg,
    white 0
  );
  border-top: 0.5px var(--primary) solid;
  border-left: 0.5px var(--primary) solid;
`;

const Card = ({ card }) => {
  const { t: tDirectCustomer } = useTranslation('directCustomer');
  const { t } = useTranslation('evDriver');

  const [width, setWidth] = useState(0);
  const elementRef = useRef(null);

  const windowWidth = useWindowWidth();
  const windowHeight = useWindowHeight();

  useEffect(() => {
    const handleResize = () => {
      setWidth(elementRef.current.offsetWidth);
    };
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [windowWidth, windowHeight]);

  return (
    <CardWrapper ref={elementRef} isMobile={isMobile}>
      {getBranding() === 'trafineo' && (
        <CardItem isTrafineo>
          <Triangle />
          <div
            style={{
              padding: '60px 0.5rem 0.5rem 0.5rem',
            }}
          >
            <CardLabelTrafineo>{t('cardNumber')}</CardLabelTrafineo>
            <div style={{ color: 'white' }}>{card.card_number}</div>
            <CardLabelTrafineo>{t('status')}</CardLabelTrafineo>
            <StatusWrapper>
              {!card.card_status ? (
                <StatusIndicator
                  margin
                  autoMargin
                  background="#dbdbdb"
                  color="#323338"
                >
                  {tDirectCustomer('statusInactive')}
                </StatusIndicator>
              ) : card.card_expiring_soon ? (
                <StatusIndicator
                  background="rgba(255, 196, 32)"
                  color="#7b341e"
                >
                  {tDirectCustomer('expiring_soon')}
                </StatusIndicator>
              ) : (
                <StatusIndicator
                  autoMargin
                  margin
                  color="#024c02"
                  background="rgba(153, 204, 0)"
                >
                  {tDirectCustomer('statusActive')}
                </StatusIndicator>
              )}
            </StatusWrapper>
          </div>
        </CardItem>
      )}

      {getBranding() !== 'trafineo' && (
        <CardItem>
          <CardImage
            src={getBranding() === 'aral' ? cardAral : cardBP}
            alt="cardBackground"
          />
          <CardContent isMobile={isMobile}>
            <CardLabel
              isHidden={width < 230}
              width={width > 229 && width < 260 ? '100%' : '35%'}
            >
              {t('validUntil')}{' '}
              {card.card_expiry_date.split('-')[1] +
                '/' +
                card.card_expiry_date.split('-')[0].slice(-2)}
            </CardLabel>
            <CardLabel isLast isHidden={width < 260} width={'65%'}>
              {!card.card_status ? (
                <StatusIndicator
                  margin
                  autoMargin
                  background="#dbdbdb"
                  color="#323338"
                >
                  {tDirectCustomer('statusInactive')}
                </StatusIndicator>
              ) : card.card_expiring_soon ? (
                <StatusIndicator
                  background="rgba(255, 196, 32)"
                  color="#7b341e"
                >
                  {tDirectCustomer('expiring_soonFilter')}
                </StatusIndicator>
              ) : (
                <StatusIndicator
                  autoMargin
                  margin
                  color="#024c02"
                  background="rgba(153, 204, 0)"
                >
                  {tDirectCustomer('statusActive')}
                </StatusIndicator>
              )}
            </CardLabel>
            <CardLabel isHidden={width < 160} width={'100%'}>
              {card.card_number}
            </CardLabel>
          </CardContent>
        </CardItem>
      )}
    </CardWrapper>
  );
};

export default Card;
