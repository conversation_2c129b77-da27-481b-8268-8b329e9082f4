import { Fade, Skeleton } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import { getBranding } from '../../utils/helper';
import PropTypes from 'prop-types';

const Table = styled.div`
  margin: 1.5rem 0 1rem 0;
`;

const TableCol = styled.div`
  width: ${(props) => props.width};
  padding-right: 0.5rem;
`;
const MobileCol = styled.div`
  ${(props) => props.bold && 'font-weight: bold'};
  padding: 0.25rem 0;
`;

const TableRow = styled.div`
  display: flex;
  align-items: center;
  font-size: 14px;
  ${(props) => props.bold && 'font-weight: bold'};
  padding: 0.6rem 0;
  color: var(--default-text);
`;
const MobileRow = styled.div`
  display: flex;
  align-items: flex-start;
  font-size: 14px;
  padding: 0.5rem;
  flex-direction: column;
  color: var(--default-text);
`;

const StyledLink = styled(Link)`
  color: var(--primary);
  text-decoration: underline;
  ${(props) => props.isMobile && 'margin-left: 0.5rem'};
  font-size: 16px;
  line-height: 24px;
  ::after {
    content: ' >';
  }
`;

const EmptyImgWrapper = styled.div`
  width: 100%;
  display: flex;
  box-sizing: border-box;
  padding: 1rem;
  justify-content: center;
  align-items: center;
  height: ${(props) => (props.isMobile ? '200px' : ' 100%;  ')};
  img {
    max-width: 80%;
  }
`;

const DriverDashboardTable = ({
  isLoading,
  data,
  emptyImgAral,
  emptyImgBp,
  config,
  linkTarget,
  isMobile,
}) => {
  const { t } = useTranslation('evDriver');

  return (
    <>
      {data?.length === 0 ? (
        <EmptyImgWrapper isMobile={isMobile}>
          <img
            src={getBranding() === 'bp' ? emptyImgBp : emptyImgAral}
            alt="noItemFound"
          />
        </EmptyImgWrapper>
      ) : isMobile ? (
        data && (
          <Table>
            {config.slice(1).map((e) => (
              <MobileRow>
                <MobileCol key={e.label} bold>
                  {e.label}
                </MobileCol>
                <MobileCol>
                  {e.valueFunction(data ? data[0][e.value] : '')}
                </MobileCol>
              </MobileRow>
            ))}
          </Table>
        )
      ) : (
        <Table>
          <TableRow bold>
            {config.map((e) => (
              <TableCol key={e.label} width={e.width}>
                {e.label}
              </TableCol>
            ))}
          </TableRow>
          {isLoading
            ? [...Array(5)].map((i) => (
                <TableRow key={i}>
                  {config.map((e) => (
                    <TableCol width={e.width}>
                      <Skeleton
                        variant={e.circular ? 'circular' : 'text'}
                        animation="pulse"
                        width={e.circular ? '37px' : '100%'}
                        height={e.circular ? '37px' : '30px'}
                      />
                    </TableCol>
                  ))}
                </TableRow>
              ))
            : data?.slice(0, 5).map((data, i) => (
                <Fade key={i} timeout={i * 200} in={!isLoading}>
                  <TableRow>
                    {config.map((e) => (
                      <TableCol width={e.width}>
                        {e.valueFunction(data[e.value])}
                      </TableCol>
                    ))}
                  </TableRow>
                </Fade>
              ))}
        </Table>
      )}
      {data?.length !== 0 && (
        <StyledLink isMobile={isMobile} to={linkTarget}>
          {t('SeeAllLink')}
        </StyledLink>
      )}
    </>
  );
};

DriverDashboardTable.defaultProps = {
  isLoading: false,
  data: undefined,
  emptyImgAral: '',
  emptyImgBp: '',
  config: [],
  linkTarget: '',
  isMobile: false,
};

DriverDashboardTable.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.array,
  emptyImgAral: PropTypes.string,
  emptyImgBp: PropTypes.string,
  config: PropTypes.arrayOf(
    PropTypes.shape({
      width: PropTypes.string,
      label: PropTypes.string,
      value: PropTypes.string,
      valueFunction: PropTypes.func,
    }),
  ),
  linkTarget: PropTypes.string,
  isMobile: PropTypes.bool,
};

export default DriverDashboardTable;
