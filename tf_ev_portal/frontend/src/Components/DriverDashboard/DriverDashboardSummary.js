import energyAral from '../../static/img/DriverDashboard/Illu-energy-aral.svg';
import energyBp from '../../static/img/DriverDashboard/Illu-energy-bp.svg';
import numberBp from '../../static/img/DriverDashboard/Illu-number-bp.svg';
import numberAral from '../../static/img/DriverDashboard/Illu-number-aral.svg';
import timeBp from '../../static/img/DriverDashboard/Illu-time-bp.svg';
import timeAral from '../../static/img/DriverDashboard/Illu-time-aral.svg';
import PropTypes from 'prop-types';
import { getBranding } from '../../utils/helper';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { Fade, MobileStepper, Skeleton } from '@mui/material';
import SwipeableViews from 'react-swipeable-views';
import { autoPlay } from 'react-swipeable-views-utils';
import { useState } from 'react';
import Timeout from '../Timeout';
import TimeoutWrapper from '../TimeoutWrapper';

const SummaryItem = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
  width: ${(props) => (props.ismobile ? '33.3%' : '100%')};
`;

const IconWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 1rem;
  img {
    height: 50px;
    width: 50px;
  }
`;
const Item = ({ e, isLoading, error }) => {
  if (isLoading || error) {
    return (
      <>
        <SummaryItem key={e.label}>
          <IconWrapper>
            <Skeleton
              variant="circular"
              animation="pulse"
              width="50px"
              height="50px"
            />
          </IconWrapper>
          <Skeleton variant="text" animation="pulse" width={65} height={33} />
          <Skeleton
            style={{
              marginTop: '0.5rem',
            }}
            variant="text"
            animation="pulse"
            width={90}
            height={24}
          />
        </SummaryItem>
      </>
    );
  } else {
    return (
      <>
        <SummaryItem key={e.label}>
          <IconWrapper>{e.icon}</IconWrapper>
          <Fade in={!isLoading}>
            <div
              style={{
                fontSize: '24px',
                fontWeight: 'bold',
                lineHeight: '33px',
                textAlign: 'center',
              }}
            >
              {e.amount}
            </div>
          </Fade>
          <Fade in={!isLoading}>
            <div
              style={{
                fontSize: '15px',
                lineHeight: '24px',
                fontWeight: 'bold',
                marginTop: '0.5rem',
                textAlign: 'center',
              }}
            >
              {e.label}
            </div>
          </Fade>
        </SummaryItem>
      </>
    );
  }
};

const DriverDashboardSummary = ({ data, isLoading, isMobile, error }) => {
  const { t } = useTranslation('evDriver');
  const AutoPlaySwipeableViews = autoPlay(SwipeableViews);
  const [activeStep, setActiveStep] = useState(0);

  const config = [
    {
      icon: (
        <img
          src={getBranding() === 'bp' ? numberBp : numberAral}
          alt="summaryTotalTransactions"
        />
      ),
      amount: data.totalTransactions,
      label: t('summaryTotalTransactions'),
    },
    {
      icon: (
        <img
          src={getBranding() === 'bp' ? energyBp : energyAral}
          alt="summaryTotalEnergy"
        />
      ),
      amount: `${Math.round(data.totalEnergy * 100) / 100} kWh`,
      label: t('summaryTotalEnergy'),
    },
    {
      icon: (
        <img
          src={getBranding() === 'bp' ? timeBp : timeAral}
          alt="summaryTotalTime"
        />
      ),
      amount: data.totalTime,
      label: t('summaryTotalTime'),
    },
  ];

  const handleStepChange = (step) => {
    setActiveStep(step);
  };
  if (isMobile) {
    return (
      <>
        <AutoPlaySwipeableViews
          axis="x"
          index={activeStep}
          onChangeIndex={handleStepChange}
          enableMouseEvents
        >
          {config.map((e, i) => (
            <div style={{ display: 'flex', justifyContent: 'center' }}>
              <Item e={e} isLoading={isLoading} />
            </div>
          ))}
        </AutoPlaySwipeableViews>
        <MobileStepper
          style={{ justifyContent: 'center' }}
          steps={config.length}
          position="static"
          activeStep={activeStep}
        />
      </>
    );
  }

  return (
    <TimeoutWrapper>
      {error && <Timeout />}

      <div
        style={{
          display: 'flex',
          flexDirection: 'flex-direction: row',
        }}
      >
        <>
          {config.map((e, i) => (
            <Item
              key={i}
              e={e}
              isLoading={isLoading}
              isMobile={isMobile}
              error={error}
            />
          ))}
        </>
      </div>
    </TimeoutWrapper>
  );
};
DriverDashboardSummary.defaultProps = {
  isLoading: false,
  data: [],
  isMobile: false,
};

DriverDashboardSummary.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      totalTransactions: PropTypes.number,
      totalEnergy: PropTypes.number,
      totalTime: PropTypes.number,
    }),
  ),
  isMobile: PropTypes.bool,
};

export default DriverDashboardSummary;
