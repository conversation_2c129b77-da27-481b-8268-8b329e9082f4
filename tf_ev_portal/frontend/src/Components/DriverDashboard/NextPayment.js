import paymentAral from '../../static/img/DriverDashboard/reimbursementDashboard-aral.svg';
import paymentBp from '../../static/img/DriverDashboard/reimbursementDashboard-bp.svg';
import paymentTrafineo from '../../static/img/DriverDashboard/reimbursementDashboard-trafineo.svg';
import PropTypes from 'prop-types';
import { getBranding } from '../../utils/helper';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { Fade, Skeleton } from '@mui/material';
import TimeoutWrapper from '../TimeoutWrapper';
import Timeout from '../Timeout';

const SummaryItem = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
  width: ${(props) => (props.ismobile ? '33.3%' : '100%')};
`;

const IconWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0.5rem;
  height: 150px;
  img {
    height: 180px;
    width: 180px;
  }
`;
const Item = ({ e, isLoading, error }) => {
  if (isLoading || error) {
    return (
      <SummaryItem key={e.label}>
        <IconWrapper>
          <Skeleton
            variant="circular"
            animation="pulse"
            width="50px"
            height="50px"
          />
        </IconWrapper>
        <Skeleton variant="text" animation="pulse" width={65} height={33} />
        <Skeleton
          style={{
            marginTop: '0.5rem',
          }}
          variant="text"
          animation="pulse"
          width={90}
          height={24}
        />
      </SummaryItem>
    );
  } else {
    return (
      <SummaryItem key={e.label}>
        <IconWrapper>{e.icon}</IconWrapper>
        <Fade in={!isLoading}>
          <div
            style={{
              fontSize: '24px',
              fontWeight: 'bold',
              lineHeight: '33px',
            }}
          >
            {e.amount ? e.amount + '€' : 0 + '€'}
          </div>
        </Fade>
        <Fade in={!isLoading}>
          <div
            style={{
              fontSize: '15px',
              lineHeight: '24px',
              fontWeight: 'bold',
              marginTop: '0.5rem',
              textAlign: 'center',
            }}
          >
            {e.label}
          </div>
        </Fade>
      </SummaryItem>
    );
  }
};

const NextPayment = ({ amount, isLoading, isMobile, error }) => {
  const { t } = useTranslation('evDriver');

  const config = [
    {
      icon: (
        <img
          src={
            getBranding() === 'bp'
              ? paymentBp
              : getBranding() === 'aral'
              ? paymentAral
              : paymentTrafineo
          }
          alt="readyForReimbursement"
        />
      ),
      amount: amount,
      label: t('readyForReimbursement'),
    },
  ];
  return (
    <TimeoutWrapper>
      {error && <Timeout />}
      <div
        style={{
          display: 'flex',
          flexDirection: 'flex-direction: row',
        }}
      >
        {config.map((e, i) => (
          <Item
            key={i}
            e={e}
            isLoading={isLoading}
            isMobile={isMobile}
            error={error}
          />
        ))}
      </div>
    </TimeoutWrapper>
  );
};
NextPayment.defaultProps = {
  isLoading: false,
  data: [],
};

NextPayment.propTypes = {
  isLoading: PropTypes.bool,
  data: PropTypes.arrayOf(
    PropTypes.shape({
      totalTransactions: PropTypes.number,
      totalEnergy: PropTypes.number,
      totalTime: PropTypes.number,
    }),
  ),
  isMobile: PropTypes.bool,
};

export default NextPayment;
