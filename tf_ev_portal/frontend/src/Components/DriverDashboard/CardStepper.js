import { useState, useContext, useEffect } from 'react';
import { evDriverContext } from '../../ContextProvider';
import SwipeableViews from 'react-swipeable-views';
import { MobileStepper } from '@mui/material';
import Card from './Card';
import { isMobile } from 'react-device-detect';
import Button from '@mui/material/Button';
import KeyboardArrowLeft from '@mui/icons-material/KeyboardArrowLeft';
import KeyboardArrowRight from '@mui/icons-material/KeyboardArrowRight';
import styled from 'styled-components';

const styles = {
  root: {
    padding: isMobile ? '0 0 0 0' : '0 20px 0 0',
    maxWidth: '100%',
  },
};

const StyledArrowButton = styled(Button)`
  min-width: fit-content !important;
  :hover {
    background-color: transparent;
    opacity: 0.7;
  }
  color: ${(props) =>
    props.isHidden ? 'transparent !important' : '#fff !important'};
`;

const StyledStepper = styled(MobileStepper)`
  margin: ${(props) =>
    props.isMobile ? '0 0 0 0 !important' : '-5px 0 0 0 !important'};
  height: 20px !important;
  justify-content: center !important;
  background: transparent !important;
  .MuiMobileStepper-dotActive {
    background: var(--trafineo-rot-100) !important;
  }
  .MuiSvgIcon-fontSizeMedium {
    color: var(--trafineo-rot-100) !important;
  }
  .Mui-disabled {
    visibility: hidden !important;
  }
`;

const CardStepper = () => {
  const {
    evDriverData: { cards },
  } = useContext(evDriverContext);

  const [activeStep, setActiveStep] = useState(0);
  const [filteredCards, setFilteredCards] = useState([]);

  const handleStepChange = (step) => {
    setActiveStep(step);
  };

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  useEffect(() => {
    setFilteredCards(
      cards
        .filter((e) => e.card_status)
        .sort(
          (a, b) => new Date(b.card_expiry_date) - new Date(a.card_expiry_date),
        ),
    );
  }, [cards]);

  if (filteredCards.length === 1) {
    return <Card card={filteredCards[0]} />;
  }
  if (filteredCards.length > 1) {
    return (
      <>
        <SwipeableViews
          style={styles.root}
          axis="x"
          index={activeStep}
          onChangeIndex={handleStepChange}
          enableMouseEvents
        >
          {filteredCards.map((card) => (
            <Card card={card} />
          ))}
        </SwipeableViews>
        <StyledStepper
          isMobile={isMobile}
          steps={filteredCards.length}
          position="static"
          activeStep={activeStep}
          nextButton={
            <StyledArrowButton
              size="small"
              disableRipple
              onClick={handleNext}
              isHidden={activeStep === filteredCards.length - 1}
              disabled={activeStep === filteredCards.length - 1}
            >
              <KeyboardArrowRight />
            </StyledArrowButton>
          }
          backButton={
            <StyledArrowButton
              size="small"
              disableRipple
              onClick={handleBack}
              isHidden={activeStep === 0}
              disabled={activeStep === 0}
            >
              <KeyboardArrowLeft />
            </StyledArrowButton>
          }
        />
      </>
    );
  }
  //return inactive card, when no active cards are present
  if (cards.length !== 0) {
    return (
      <Card
        card={
          cards.sort(
            (a, b) =>
              new Date(b.card_expiry_date) - new Date(a.card_expiry_date),
          )[0]
        }
      />
    );
  }
  return <></>;
};
export default CardStepper;
