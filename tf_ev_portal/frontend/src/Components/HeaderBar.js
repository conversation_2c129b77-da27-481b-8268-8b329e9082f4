import { useContext, useState } from 'react';
import styled from 'styled-components';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import JwtDecode from 'jwt-decode';
import PropTypes from 'prop-types';
import Trafineo<PERSON>ogo from '../static/img/<EMAIL>';
import BpLogo from '../static/img/BPP_Rlbg.svg';
import Aral<PERSON>ogo from '../static/img/Aral_Logo_M40_4C_transp.png';
import LanguageIcon from '@material-ui/icons/Language';
import PersonIcon from '@material-ui/icons/Person';
import PeopleIcon from '@material-ui/icons/People';
import PaymentIcon from '@mui/icons-material/Payment';
import ExitToAppIcon from '@material-ui/icons/ExitToApp';
import GridViewIcon from '@mui/icons-material/GridView';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { userContext } from '../ContextProvider';
import { getBranding } from '../utils/helper';
import Button from '@material-ui/core/Button';
import Menu from '@material-ui/core/Menu';
import MenuItem from '@material-ui/core/MenuItem';
import { ListItemIcon } from '@material-ui/core';
import MobileNav from './MobileNav';
import { isMobile } from 'react-device-detect';
import EuroIcon from '@material-ui/icons/Euro';
import EvStationIcon from '@material-ui/icons/EvStation';
import SettingsIcon from '@mui/icons-material/Settings';
import ActivitiesDialog from './ActivitiesDialog';
import Green_Energy from '../static/img/icons/Green_energy.svg';

const StyledIcon = styled.img`
  height: 20px;
  width: 20px;
  color: rgba(0, 0, 0, 0.54) !important;
`;

export const Bar = styled.div`
  height: var(--header-height);
  background: var(--header-background);
  ${(props) =>
    props.onboardingMode
      ? ''
      : 'border-bottom: 10px solid var(--trafineo-rot-100);'};
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  z-index: 1000;
  width: 100%;
`;

export const BrandIcon = styled.img`
  height: var(--header-img-height);
  width: var(--header-img-width);
  ${(props) => (props.homeChargingAustria ? '' : 'cursor: pointer;')};
`;
export const BrandIconWrapper = styled.div`
  padding: ${isMobile ? '0 10px' : '0 20px'};
  display: flex;
`;
export const MenuWrapper = styled.div`
  margin-left: auto;
  display: flex;
`;
export const NavWrapper = styled.div`
  display: flex;
`;
export const MenuItemWrapper = styled.div`
  padding-right: 20px;
  display: flex;
  button {
    text-transform: none;
    font-size: 14px;
    &:hover {
      opacity: 0.7;
      background: none;
    }
  }
`;
export const NavItemWrapper = styled.div`
  padding-left: 20px;
  display: flex;
  a {
    text-decoration: none;
  }
  button {
    color: var(--default-text);
    text-transform: none;
    font-size: 14px;
    &:hover {
      opacity: 0.7;
      background: none;
    }
  }
`;
export const StyledMenuItem = styled(MenuItem)`
  font-size: 14px !important;
  &:hover {
    opacity: 0.7;
    background: none;
  }
`;
export const StyledListItemIcon = styled(ListItemIcon)`
  min-width: 30px !important;
  padding-bottom: 1px;
`;

const {
  REACT_APP_KEYCLOAK_SUPERUSER_ROLE,
  REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE,
  REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE,
  REACT_APP_KEYCLOAK_DRIVER_ROLE,
  REACT_APP_KEYCLOAK_APP_DRIVER_ROLE,
  REACT_APP_KEYCLOAK_SALESADMIN_ROLE,
  REACT_APP_KEYCLOAK_SUPPORT_ROLE,
} = process.env;

export const HeaderBar = ({
  onLanguageSelect,
  homeChargingAustria,
  onLogout,
  language,
  isDriverRegistered,
  onboardingMode,
}) => {
  const { t } = useTranslation('headerbar');
  const tEvDriver = useTranslation('evDriver').t;
  const tOverview = useTranslation('overview').t;
  const { user } = useContext(userContext);
  const navigate = useNavigate();
  const [isActivitiesDialogVisible, setIsActivitiesDialogVisible] =
    useState(false);
  let role = '';
  try {
    role = JwtDecode(user.access_token).role;
  } catch (e) {
    role = '';
  }

  let email = '';
  try {
    email = JwtDecode(user.access_token).preferred_username;
  } catch (e) {
    email = '';
  }

  const LangSelect = () => {
    const [anchorEl, setAnchorEl] = useState(null);

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };
    const handleSelect = (e) => {
      const { value } = e.currentTarget.dataset;
      onLanguageSelect(value);
      setAnchorEl(null);
    };

    const langMap = {
      de: 'german',
      en: 'english',
      nl: 'dutch',
    };

    const handleClose = (e) => {
      setAnchorEl(null);
    };
    const open = Boolean(anchorEl);

    return (
      <MenuItemWrapper>
        <Button
          disableRipple
          aria-controls="basic-menu"
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          startIcon={<LanguageIcon />}
          endIcon={<ExpandMoreIcon />}
        >
          {tEvDriver(langMap[language])}
        </Button>
        <Menu
          disableScrollLock
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          getContentAnchorEl={null}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          {language !== 'de' && (
            <StyledMenuItem data-value="de" onClick={handleSelect}>
              {tEvDriver('german')}
            </StyledMenuItem>
          )}
          {language !== 'en' && (
            <StyledMenuItem data-value="en" onClick={handleSelect}>
              {tEvDriver('english')}
            </StyledMenuItem>
          )}
          {language !== 'nl' && !homeChargingAustria && (
            <StyledMenuItem data-value="nl" onClick={handleSelect}>
              {tEvDriver('dutch')}
            </StyledMenuItem>
          )}
        </Menu>
      </MenuItemWrapper>
    );
  };

  const UserMenu = () => {
    const [anchorEl, setAnchorEl] = useState(null);

    const handleClick = (event) => {
      setAnchorEl(event.currentTarget);
    };

    const handleClose = (e) => {
      setAnchorEl(null);
    };
    const open = Boolean(anchorEl);

    return (
      <MenuItemWrapper>
        {isActivitiesDialogVisible && (
          <ActivitiesDialog
            open={isActivitiesDialogVisible}
            onClose={() => {
              setIsActivitiesDialogVisible(false);
            }}
          />
        )}

        <Button
          data-cy="emailButton"
          disableRipple
          aria-controls="basic-menu"
          aria-haspopup="true"
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
          startIcon={<PersonIcon />}
          endIcon={<ExpandMoreIcon />}
        >
          {email}
        </Button>
        <Menu
          disableScrollLock
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          getContentAnchorEl={null}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'center',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'center',
          }}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          <StyledMenuItem
            onClick={() => {
              navigate(
                `/myinformation${
                  isDriverRegistered ? '/personalinformation' : ''
                }`,
              );
              handleClose();
            }}
          >
            <StyledListItemIcon>
              <PersonIcon fontSize="small" />
            </StyledListItemIcon>
            {tEvDriver('myInformation')}
          </StyledMenuItem>
          {(role.toLowerCase() ===
            REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE ||
            role.toLowerCase() ===
              REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE ||
            role.toLowerCase() === REACT_APP_KEYCLOAK_SALESADMIN_ROLE ||
            role.toLowerCase() === REACT_APP_KEYCLOAK_SUPPORT_ROLE) && (
            <StyledMenuItem
              onClick={() => {
                navigate('/myteam');
                handleClose();
              }}
            >
              <StyledListItemIcon>
                <PeopleIcon fontSize="small" />
              </StyledListItemIcon>
              {t('myTeam')}
            </StyledMenuItem>
          )}
          {(role.toLowerCase() ===
            REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE ||
            role.toLowerCase() ===
              REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE) && (
            <StyledMenuItem
              onClick={() => {
                navigate('/payouts');
                handleClose();
              }}
            >
              <StyledListItemIcon>
                <PaymentIcon fontSize="small" />
              </StyledListItemIcon>
              {tOverview('payouts')}
            </StyledMenuItem>
          )}
          {(role.toLowerCase() ===
            REACT_APP_KEYCLOAK_FLEETMANAGER_DIRECT_ROLE ||
            role.toLowerCase() ===
              REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE) && (
            <StyledMenuItem
              onClick={() => {
                navigate('/sustainability');
                handleClose();
              }}
            >
              <StyledListItemIcon>
                <StyledIcon src={Green_Energy} alt="greenEnergy" />
              </StyledListItemIcon>
              {t('sustainability')}
            </StyledMenuItem>
          )}
          {(role.toLowerCase() === REACT_APP_KEYCLOAK_APP_DRIVER_ROLE ||
            role.toLowerCase() === REACT_APP_KEYCLOAK_DRIVER_ROLE) &&
            isDriverRegistered && (
              <StyledMenuItem
                onClick={() => {
                  navigate('/settings');
                  handleClose();
                }}
              >
                <StyledListItemIcon>
                  <SettingsIcon fontSize="small" />
                </StyledListItemIcon>
                {tEvDriver('settings')}
              </StyledMenuItem>
            )}
          <StyledMenuItem data-value="en" onClick={() => onLogout()}>
            <StyledListItemIcon>
              <ExitToAppIcon fontSize="small" />
            </StyledListItemIcon>
            {t('logout')}
          </StyledMenuItem>
        </Menu>
      </MenuItemWrapper>
    );
  };

  return (
    <Bar onboardingMode={onboardingMode}>
      <BrandIconWrapper>
        {(homeChargingAustria && (
          <BrandIcon
            homeChargingAustria={homeChargingAustria}
            src={
              getBranding() === 'bp'
                ? BpLogo
                : getBranding() === 'aral'
                ? AralLogo
                : TrafineoLogo
            }
            alt="Trafineo-Logo"
          />
        )) || (
          <Link to={user ? '/' : '/login'}>
            <BrandIcon
              src={
                getBranding() === 'bp'
                  ? BpLogo
                  : getBranding() === 'aral'
                  ? AralLogo
                  : TrafineoLogo
              }
              alt="Trafineo-Logo"
            />
          </Link>
        )}
      </BrandIconWrapper>
      {(role.toLowerCase() === REACT_APP_KEYCLOAK_SUPERUSER_ROLE ||
        role.toLowerCase() === REACT_APP_KEYCLOAK_SUPPORT_ROLE) && (
        <NavWrapper>
          <NavItemWrapper>
            <Link to="/">
              <Button disableRipple aria-controls="nav-menu">
                {t('fleetOverview')}
              </Button>
            </Link>
          </NavItemWrapper>
          {role.toLowerCase() === REACT_APP_KEYCLOAK_SUPERUSER_ROLE && (
            <NavItemWrapper>
              <Link to="/home-charging">
                <Button disableRipple aria-controls="nav-menu">
                  {t('homeCharging')}
                </Button>
              </Link>
            </NavItemWrapper>
          )}

          <NavItemWrapper>
            <Link to="/onboarding-dashboard">
              <Button disableRipple aria-controls="nav-menu">
                {t('onboarding-dashboard')}
              </Button>
            </Link>
          </NavItemWrapper>
        </NavWrapper>
      )}
      {isDriverRegistered && !isMobile && (
        <NavWrapper>
          <NavItemWrapper>
            <Link to="/">
              <Button
                startIcon={<GridViewIcon />}
                disableRipple
                aria-controls="nav-menu"
              >
                {tEvDriver('driverDashboard')}
              </Button>
            </Link>
          </NavItemWrapper>
          <NavItemWrapper>
            <Link to="/charging_sessions">
              <Button
                startIcon={<EvStationIcon />}
                disableRipple
                aria-controls="nav-menu"
              >
                {tOverview('myChargingSessions')}
              </Button>
            </Link>
          </NavItemWrapper>
          <NavItemWrapper>
            <Link to="/reimbursements">
              <Button
                startIcon={<EuroIcon />}
                disableRipple
                aria-controls="nav-menu"
              >
                {tOverview('myReimbursements')}
              </Button>
            </Link>
          </NavItemWrapper>
        </NavWrapper>
      )}

      <MenuWrapper>
        {isMobile ? (
          <>
            <MobileNav
              email={email}
              logout={onLogout}
              isDriverRegistered={isDriverRegistered}
              langSelect={<LangSelect />}
            />
          </>
        ) : (
          <>
            <LangSelect />
            {user && <UserMenu />}
          </>
        )}
      </MenuWrapper>
    </Bar>
  );
};

HeaderBar.propTypes = {
  onLanguageSelect: PropTypes.func.isRequired,
  onLogout: PropTypes.func.isRequired,
  homeChargingAustria: PropTypes.bool.isRequired,
  onboardingMode: PropTypes.bool,
  language: PropTypes.string.isRequired,
};

export default HeaderBar;
