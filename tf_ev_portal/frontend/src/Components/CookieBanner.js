import CookieConsent, { Cookies } from 'react-cookie-consent';
import TagManager from 'react-gtm-module';

import { isMobile } from 'react-device-detect';
import { Row } from '../Components/evDriver/PageTemplate';
import { Button } from '../Components';
import { useNavigate } from 'react-router-dom';

import { useWindowHeight, useWindowWidth } from '@react-hook/window-size';

import { useState, useEffect, useRef, useCallback } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';

const Textcontainer = styled.div`
  color: #fff;
`;

const ButtonRow = styled(Row)`
  padding-top: 2rem;
  > * {
    margin-left: 5px !important;
    margin-right: 5px !important;
  }
`;

const CookieBanner = () => {
  const observedButton = useRef(null);

  const { t } = useTranslation('actions');
  const navigate = useNavigate();

  const [width, setWidth] = useState();
  const [height, setHeight] = useState();

  const windowWidth = useWindowWidth();
  const windowHeight = useWindowHeight();

  useEffect(() => {
    const handleResize = () => {
      if (observedButton.current) {
        setWidth(observedButton.current.offsetWidth);
        setHeight(observedButton.current.offsetHeight);
      }
    };
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [windowWidth, windowHeight]);

  const handleAcceptCookie = useCallback(() => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'consent_save',
        consent: {
          interactionPhase: 'initial',
          interactionType: 'accept all',
          status: {
            essential: true,
            performance: true,
          },
        },
      },
    });
  }, []);

  const handleDeclineCookie = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'consent_save',
        consent: {
          interactionPhase: 'initial',
          interactionType: 'decline all',
          status: {
            essential: true,
            performance: false,
          },
        },
      },
    });
    //remove google analytics cookies
    Cookies.remove('_ga');
    Cookies.remove('_gat');
    Cookies.remove('_gid');
  };

  return (
    <CookieConsent
      overlay
      location="bottom"
      cookieName="reimbursementCookie"
      buttonText={t('cookieAccept')}
      declineButtonText={t('cookieDecline')}
      enableDeclineButton
      containerClasses="alert alert-warning col-lg-12"
      contentClasses="text-capitalize"
      style={{
        background: 'var(--trafineo-grau-100)',
        display: 'block',
        marginBottom: isMobile ? '0' : '40px',
      }}
      buttonStyle={{
        background: 'transparent',
        color: 'transparent',
        border: 'none',
        outline: 'none',
        display: 'block',
        cursor: 'pointer',
        position: 'absolute',
        zIndex: '4',
        bottom: '17px',
        width: isMobile ? width || '29%' : width || '32%',
        height: isMobile ? height || '12%' : height || '38px',
        left: '4px',
      }}
      declineButtonStyle={{
        background: 'transparent',
        color: 'transparent',
        border: 'none',
        cursor: 'pointer',
        position: 'absolute',
        zIndex: '4',
        bottom: '16px',
        width: isMobile ? width || '29%' : width || '32%',
        height: isMobile ? height || '12%' : height || '38px',
        right: '4px',
      }}
      onAccept={handleAcceptCookie}
      onDecline={handleDeclineCookie}
      expires={150}
    >
      <Textcontainer
        dangerouslySetInnerHTML={{
          __html: t('cookieText'),
        }}
      ></Textcontainer>
      <ButtonRow>
        <Button
          variant={'primary'}
          style={{ height: isMobile ? 'auto' : '38', minHeight: '38px' }}
        >
          {t('cookieAccept')}
        </Button>
        <Button
          variant={'tertiary'}
          style={{ height: isMobile ? 'auto' : '38', minHeight: '38px' }}
          onClick={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'consent_save',
                consent: {
                  interactionPhase: 'initial',
                  interactionType: 'selective consent',
                },
              },
            });
            navigate('/cookie-settings', {
              state: {
                prevPathname: window.location.pathname,
                key: window.location.search,
              },
            });
          }}
        >
          {t('cookieManage')}
        </Button>
        <Button
          innerRef={observedButton}
          variant={'secondary'}
          style={{ height: isMobile ? 'auto' : '38', minHeight: '38px' }}
        >
          {t('cookieDecline')}
        </Button>
      </ButtonRow>
    </CookieConsent>
  );
};

export default CookieBanner;
