import MuiMenuItem from '@material-ui/core/MenuItem';
import PropTypes from 'prop-types';
import styled from 'styled-components';

const StyledMuiMenuItem = styled(MuiMenuItem)`
  display: flex;
  align-items: center;
  box-sizing: border-box;
  height: 46px;
  border-bottom: solid 1px var(--default-text);
  font-size: 14px;
  :last-child {
    border: none;
  }
  :hover {
    background-color: var(--trafineo-rot-20) !important;
    cursor: pointer;
  }
`;

const MenuItem = ({ children, ...props }) => {
  return <StyledMuiMenuItem {...props}>{children}</StyledMuiMenuItem>;
};

MenuItem.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
};

export default MenuItem;
