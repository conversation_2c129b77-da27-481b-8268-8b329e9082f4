import { Grid } from '@material-ui/core';
import PropTypes from 'prop-types';
import Center from './helper/Center';
import CircularProgress from './CircularProgress';
import styled from 'styled-components';

const InfoValue = styled.p`
  margin: 2px 0;
  text-align: right;
`;

const GeneralInfoContainer = styled.div`
  width: ${(props) => (props.superUser ? '400px' : '350px')};
  position: absolute;
  top: ${(props) => (props.superUser ? '40px' : '-60px')};
  right: ${(props) => (props.superUser ? '16px' : '0')};
  ${(props) => (props.superUser ? 'padding: 7px 4px;' : '')};
`;

const InfoText = styled.p`
  margin: 2px 0;
  margin-bottom: 10px;
`;

const GeneralInfoWrapper = styled.div`
  border: solid 1px var(--trafineo-grau-50);
  background-color: var(--trafineo-grau-20);
  text-align: left;
`;

const InfoGrid = styled(Grid)`
  padding: 2px 10px;
  opacity: 0.74;
  :nth-child(odd) {
    background-color: var(--trafineo-grau-50);
  }
`;

const GeneralInfo = ({ title, fields, loading, superUser }) => {
  return (
    <GeneralInfoContainer superUser={superUser}>
      {loading && (
        <Center>
          <CircularProgress />
        </Center>
      )}
      {!loading && (
        <GeneralInfoWrapper>
          <Grid container>
            {title && (
              <Grid item xs={12}>
                <InfoText margin>
                  <b>{title}</b>
                </InfoText>
              </Grid>
            )}
            {fields &&
              fields.map((field, i) => (
                <InfoGrid container key={i}>
                  <Grid item xs={9}>
                    <InfoText>{field.label}:</InfoText>
                  </Grid>
                  <Grid item xs={3}>
                    <InfoValue>
                      <b>{field.value}</b>
                    </InfoValue>
                  </Grid>
                </InfoGrid>
              ))}
          </Grid>
        </GeneralInfoWrapper>
      )}
    </GeneralInfoContainer>
  );
};

GeneralInfo.defaultProps = {
  title: '',
  fields: [],
  loading: false,
};

GeneralInfo.propTypes = {
  title: PropTypes.string,
  fields: PropTypes.arrayOf(
    PropTypes.shape({
      label: PropTypes.string,
      value: PropTypes.oneOfType([PropTypes.number, PropTypes.string]),
    }),
  ),
  loading: PropTypes.bool,
};

export default GeneralInfo;
