import { IconButton, Tooltip } from '@mui/material';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import iconEdit from '../static/img/icon-edit.svg';
import ReplayIcon from '@material-ui/icons/Replay';
import AddCircleIcon from '@material-ui/icons/AddCircle';

const StyledIconButton = styled(IconButton)`
  color: #676879;
  width: 30px;
  height: 30px;
  img {
    width: 20px;
  }
  :disabled {
    display: none;
  }
`;

export const EditButton = ({ onClick, disabled }) => {
  const { t } = useTranslation('evDriver');
  return (
    <Tooltip title={t('modify')}>
      <StyledIconButton disabled={disabled} onClick={onClick}>
        <img alt={t('modify')} src={iconEdit}></img>
      </StyledIconButton>
    </Tooltip>
  );
};

export const RevertButton = ({ onClick, disabled }) => {
  const { t } = useTranslation('evDriver');
  return (
    <Tooltip title={t('revert')}>
      <StyledIconButton
        data-cy="revertTariff"
        disabled={disabled}
        onClick={onClick}
      >
        <ReplayIcon />
      </StyledIconButton>
    </Tooltip>
  );
};

export const AddButton = ({ onClick, disabled }) => {
  const { t } = useTranslation('evDriver');
  return (
    <Tooltip title={t('add')}>
      <StyledIconButton disabled={disabled} onClick={onClick}>
        <AddCircleIcon />
      </StyledIconButton>
    </Tooltip>
  );
};
