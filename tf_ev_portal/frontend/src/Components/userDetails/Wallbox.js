import { Box } from '@material-ui/core';
import { useTranslation } from 'react-i18next';

const Wallbox = ({ user }) => {
  const { t } = useTranslation('directCustomer');

  return (
    <Box fontSize="14px">
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('evse_id')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {user.wallbox?.evse_id || '-'}
        </Box>
      </Box>
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('location_of_wallbox')}
        </Box>
        {user.wallbox?.address ? (
          <>
            <Box color="#666666" py="0.5rem">
              {`${user.wallbox.address.street} ${user.wallbox.address.number}`}
            </Box>
            {user.wallbox.address.additional_information && (
              <Box color="#666666" py="0.5rem">
                {`${user.wallbox.address.additional_information}`}
              </Box>
            )}
            <Box color="#666666" py="0.5rem">
              {user.wallbox.address.country
                ? `${user.wallbox.address.postcode} ${user.wallbox.address.city} ${user.wallbox.address.country}`
                : `${user.wallbox.address.postcode} ${user.wallbox.address.city} ${user.wallbox.location_of_wallbox}`}
            </Box>
          </>
        ) : (
          '-'
        )}
      </Box>
    </Box>
  );
};

export default Wallbox;
