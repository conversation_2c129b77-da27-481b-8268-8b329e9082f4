import { useContext } from 'react';
import { Trans, useTranslation } from 'react-i18next';
import { userContext } from '../../ContextProvider';
import { isReimbursementActive } from '../../utils/helper';
import CardStatus from '../CardStatus';
import ButtonSwitch from '../dashboard/ButtonSwitch';
import { HeadlineBox, TableContainer } from '../dashboard/DashboardTemplates';
import { Row, RowBox, RowContainer, TitleRow } from '../dashboard/RowTemplates';
import ReimbursementStatus from '../ReimbursementStatus';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import Hint from '../Hint';
import { StatusIndicator } from '../dashboard/RowTemplates';
import { isMobile } from 'react-device-detect';
import { useWindowWidth } from '@react-hook/window-size';
import jwtDecode from 'jwt-decode';
import Button from '../Button';
import DialogRouter from '../dashboard/DialogRouter';
import { dialogContext } from '../../ContextProvider';

const StledA = styled(Link)`
  color: var(--trafineo-rot-100);
  font-weight: bold;
`;

const ButtonWrapper = styled.div`
  width: 40%;
  margin-top: 15px;
`;

const Cards = ({ tokens, userMode }) => {
  const { user } = useContext(userContext);
  const { t } = useTranslation('directCustomer');
  const { t: tDriver } = useTranslation('evDriver');
  const {
    REACT_APP_KEYCLOAK_SUPERUSER_ROLE,
    REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE,
  } = process.env;
  const { role } = useContext(userContext);
  const isSuperUser = role.toLowerCase() === REACT_APP_KEYCLOAK_SUPERUSER_ROLE;
  const isIndirectFleetmanager =
    role.toLowerCase() === REACT_APP_KEYCLOAK_FLEETMANAGER_INDIRECT_ROLE;
  let cellWidths = !userMode
    ? [2.5, 37.5, 20, 20, 20]
    : [2.5, 32.5, 15, 25, 25];

  const { dialogData, setDialogData } = useContext(dialogContext);

  const windowWidth = useWindowWidth();

  if (isMobile && windowWidth <= 700) {
    cellWidths = [2.5, 47.5, 0, 25, 25];
  }

  return (
    <>
      <DialogRouter onDialogClose={() => window.location.reload()} />
      <TableContainer>
        <TitleRow style={{ height: '48px' }}>
          <HeadlineBox style={{ height: '48px', margin: 0 }}>
            <RowBox bold width={cellWidths[0]}></RowBox>
            <RowBox bold width={cellWidths[1]}>
              {t('token_visual_number')}
            </RowBox>
            {(!isMobile || windowWidth > 700) && (
              <RowBox bold width={cellWidths[2]}>
                {tDriver('validUntil')}
              </RowBox>
            )}
            <RowBox bold width={cellWidths[3]}>
              {t('card_status')}
            </RowBox>
            <RowBox bold width={cellWidths[4]}>
              {t('reimbursement_status_id')}
            </RowBox>
            {!userMode && <RowBox bold width={cellWidths[4]}></RowBox>}
          </HeadlineBox>
        </TitleRow>
        <div>
          {tokens.map((row, i) => {
            return (
              <Row key={i} style={{ height: '48px' }}>
                <RowContainer>
                  <RowBox bold width={cellWidths[0]}></RowBox>
                  <RowBox width={cellWidths[1]}>
                    {userMode ? row.card_number : row.token_visual_number}
                    {isSuperUser && row.issuer?.toLowerCase() === 'dcs' && (
                      <StatusIndicator
                        margin
                        background="#dbdbdb"
                        color="#323338"
                      >
                        DCS
                      </StatusIndicator>
                    )}
                  </RowBox>
                  {(!isMobile || windowWidth > 700) && (
                    <RowBox width={cellWidths[2]}>
                      {row.card_expiry_date
                        ? row.card_expiry_date?.split('-')[1] +
                          '/' +
                          row.card_expiry_date?.split('-')[0].slice(-2)
                        : row.expiry_date?.split('-')[1] +
                          '/' +
                          row.expiry_date?.split('-')[0].slice(-2)}
                    </RowBox>
                  )}
                  <RowBox width={cellWidths[3]}>
                    <CardStatus
                      card_status={row.card_status}
                      expiring_soon={row.expiring_soon}
                    />
                  </RowBox>
                  <RowBox width={cellWidths[4]}>
                    <ReimbursementStatus
                      reimbursementStatusId={row.reimbursement_status_id}
                    />
                  </RowBox>
                  {!userMode && (
                    <RowBox width={cellWidths[4]}>
                      {isReimbursementActive(row.reimbursement_status_id) && (
                        <ButtonSwitch
                          cancelReimbursementButton
                          data={{
                            expiry_date: row.expiry_date,
                            token_visual_number: row.token_visual_number,
                            reimbursement_status_id:
                              row.reimbursement_status_id,
                          }}
                        />
                      )}
                    </RowBox>
                  )}
                </RowContainer>
              </Row>
            );
          })}
        </div>
      </TableContainer>
      {!isSuperUser && !userMode && !isIndirectFleetmanager && (
        <Hint>
          <Trans t={t} i18nKey="cardOverviewLink">
            You can assign a new cards in the
            <StledA name="dashboard" to="/?tab=cards">
              card overview
            </StledA>
          </Trans>
        </Hint>
      )}

      {jwtDecode(user.access_token).provider?.toLowerCase() === 'trafineo' &&
        userMode && (
          <ButtonWrapper>
            <Button
              variant={'primary'}
              onClick={() =>
                setDialogData({
                  ...dialogData,
                  type: 'addCard',
                  open: true,
                })
              }
            >
              {t('addCard')}
            </Button>
          </ButtonWrapper>
        )}
    </>
  );
};

export default Cards;
