import { Box } from '@material-ui/core';
import Tariff from '../Tariff';

const Tariffs = ({ user }) => {
  return user.tariffs ? (
    <Box>
      {user.tariffs
        .sort(function (a, b) {
          return (
            new Date(b.tariff_elements[0].restrictions[0].start_date) -
            new Date(a.tariff_elements[0].restrictions[0].start_date)
          );
        })
        .map((tariff, i) => {
          return <Tariff index={i} editDisabled tariff={tariff} />;
        })}
    </Box>
  ) : (
    <></>
  );
};

export default Tariffs;
