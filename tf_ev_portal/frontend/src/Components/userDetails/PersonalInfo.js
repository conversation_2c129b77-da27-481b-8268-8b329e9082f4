import { Box } from '@material-ui/core';
import { useTranslation } from 'react-i18next';
import IconSwitch from '../dashboard/IconSwitch';

const PersonalInfo = ({ user }) => {
  const { t } = useTranslation('directCustomer');
  const {
    firstname,
    lastname,
    email_address_ev_driver,
    driver_approval_status,
  } = user;
  const driverStatus = driver_approval_status
    ? driver_approval_status.map(
        ({ driver_approval_status_name }) => driver_approval_status_name,
      )
    : [null];
  return (
    <Box fontSize="14px">
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('firstname')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {firstname || '-'}
        </Box>
      </Box>
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('lastname')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {lastname || '-'}
        </Box>
      </Box>
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('email_address_ev_driver')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {email_address_ev_driver || '-'}
        </Box>
      </Box>
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('platform')}
        </Box>
        <Box color="#666666" py="0.5rem">
          <IconSwitch status={driverStatus} />
        </Box>
      </Box>
    </Box>
  );
};

export default PersonalInfo;
