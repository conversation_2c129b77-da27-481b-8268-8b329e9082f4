import { Box } from '@material-ui/core';
import { useTranslation } from 'react-i18next';

const Vehicle = ({ user }) => {
  const { t } = useTranslation('directCustomer');
  return (
    <Box fontSize="14px">
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('licence_plate')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {user.vehicle?.licence_plate || '-'}
        </Box>
      </Box>
      <Box mb="0.5rem">
        <Box fontWeight="bold" py="0.5rem">
          {t('vehicle_model')}
        </Box>
        <Box color="#666666" py="0.5rem">
          {user.vehicle?.make
            ? user.vehicle?.make === 'other'
              ? t('other')
              : `${user.vehicle?.make} ${user.vehicle?.model}`
            : '-'}
        </Box>
      </Box>
    </Box>
  );
};

export default Vehicle;
