import { Box, Tab, Tabs } from '@mui/material';
import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import Activities from '../Activities';
import PersonalInfo from './PersonalInfo';
import Vehicle from './Vehicle';
import Wallbox from './Wallbox';

const StyledTab = styled(Tab)`
  text-transform: none !important;
  font-size: 14px !important;
  padding: 0 1rem !important;
  &.Mui-selected {
    color: black !important;
    font-weight: bold;
  }
`;

function a11yProps(index) {
  return {
    id: `info-tab-${index}`,
    'aria-controls': `info-tabpanel-${index}`,
  };
}

const Info = ({ user }) => {
  const { t } = useTranslation('directCustomer');
  const [informationNav, setInformationNav] = useState(0);
  const informationTabs = [
    ['personalInformation', (user) => <PersonalInfo user={user} />],
    ['vehicle', (user) => <Vehicle user={user} />],
    ['wallbox', (user) => <Wallbox user={user} />],
    ['activity', (user) => <Activities driverData={user} />],
  ];
  return (
    <Box>
      <Box mb="1rem" sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={informationNav}
          onChange={(event, newValue) => setInformationNav(newValue)}
          aria-label="informationTabs"
        >
          {informationTabs.map((arr, i) => {
            const e = arr[0];
            return (
              <StyledTab
                key={e}
                disableRipple
                value={i}
                label={t(e)}
                {...a11yProps(i)}
              />
            );
          })}
        </Tabs>
      </Box>
      <Box pl="1rem">{informationTabs[informationNav][1](user)}</Box>
    </Box>
  );
};

export default Info;
