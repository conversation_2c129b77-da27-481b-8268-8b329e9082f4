import PropTypes from 'prop-types';
import styled from 'styled-components';
import BackButton from './BackButton';

const Container = styled.div`
  ${(props) => props.isMobile && 'display: flex'};
`;
const Wrapper = styled.div`
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-width: ${(props) => props.minWidth};

  margin: 10px auto 4rem auto;
  padding: ${(props) => (props.isMobile ? '0' : '0 2rem')};
  box-sizing: border-box;
  ${(props) =>
    props.card || props.centered ? 'width: 700px' : 'max-width: 1500px'};

  margin-bottom: ${(props) =>
    props.dashboard
      ? props.isMobile || !props.hasFooter
        ? '0px'
        : '40px'
      : ''};
  padding-bottom: ${(props) => (props.dashboard ? '20px' : '')};
`;

const Content = styled.div`
  text-align: left;
  max-width: ${(props) => props.maxWidth};
  padding: ${(props) =>
    props.isMobile ? '0 1rem' : props.card ? '2rem' : '0'};
  display: flex;
  flex-direction: column;
  ${(props) => props.card && ' border-radius: 10px;  background: #fff;'}
  box-shadow: ${(props) =>
    !props.card || props.isMobile ? 'none' : '0 0 40px 0 rgb(0 0 0 / 10%)'};
`;

export const Title = styled.h1`
  font-family: var(--font-family);
  font-size: ${(props) => (props.isMobile ? '25px' : '33px')};
  justify-content: ${(props) => (props.isMobile ? 'center' : 'flex-start')};
  margin: ${(props) =>
    props.backButton
      ? props.isMobile
        ? ' 0.5rem 0 1.5rem 0'
        : ' 0.5rem 0 2.5rem 0'
      : props.isMobile
      ? '1.5rem 0'
      : '2.5rem 0'};
  font-weight: bold;
  font-stretch: normal;
  display: flex;
  align-items: center;
  font-style: normal;
  letter-spacing: 0.07px;
  color: #000;
`;
const Description = styled.div`
  margin-bottom: 2rem;
  font-size: 16px;
  line-height: 24px;
  text-align: left;
`;

const PageWrapper = ({
  title,
  children,
  description,
  minWidth,
  maxWidth,
  backButton,
  backFuction,
  card,
  isMobile,
  hasFooter,
  icon,
  centered,
  dashboard,
  helpText,
}) => {
  return (
    <Container isMobile={isMobile}>
      <Wrapper
        isMobile={isMobile}
        dashboard={dashboard}
        card={card}
        centered={centered}
        minWidth={minWidth}
        hasFooter={hasFooter}
      >
        {backButton && <BackButton backFuction={backFuction} />}
        {helpText && <p>{helpText}</p>}
        <Title backButton={backButton} isMobile={isMobile}>
          {title}
          {icon}
        </Title>
        <Content card={card} maxWidth={maxWidth} isMobile={isMobile}>
          {description && <Description>{description}</Description>}
          <div>{children}</div>
        </Content>
      </Wrapper>
    </Container>
  );
};

PageWrapper.defaultProps = {
  title: '',
  description: null,
  minWidth: '0px',
  maxWidth: null,
  backButton: false,
  card: false,
  centered: false,
  isMobile: false,
  hasFooter: true,
  dashboard: false,
  icon: null,
};

PageWrapper.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  minWidth: PropTypes.string,
  maxWidth: PropTypes.string,
  backButton: PropTypes.bool,
  card: PropTypes.bool,
  centered: PropTypes.bool,
  backFuction: PropTypes.func,
  isMobile: PropTypes.bool,
  hasFooter: PropTypes.bool,
  dasboard: PropTypes.bool,
  icon: PropTypes.element,
};

export default PageWrapper;
