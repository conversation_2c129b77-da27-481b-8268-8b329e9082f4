import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { Box } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useNavigate } from 'react-router-dom';
import TariffIcon from '../static/img/icon-tariff.svg';
import { isDateValid } from '../utils/helper';
import { evDriverContext } from '../ContextProvider';
import { isMobile } from 'react-device-detect';
import FileDownload from './FileDownload';
import { AddButton, EditButton, RevertButton } from './ActionButtons';

/*
This is a Legacy Component,
the <Tariff/> component should be used when reworking the Dashboard and My Profile Section
*/

const LegacyTariffs = ({
  tariffs,
  pendingTariff,
  idpId,
  canEdit,
  files,
  onRevert,
  dataApproval,
  newTheme,
  editDisabled,
  canSelectTariffType,
  hideIcon,
  currentTariff,
  reminder,
  comment,
}) => {
  const useStyles = makeStyles(
    currentTariff
      ? {
          imgContainer: {
            paddingLeft: '0.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
          },
          bold: {
            fontWeight: 'bold',
            textAlign: 'left',
          },
          tariffDate: {
            fontWeight: 'bold',
          },
          icon: {
            left: '10px',
            top: '44px',
            width: '13px',
            position: 'absolute',
          },
          borderBox: {
            border: 'solid 1px var(--trafineo-grau-70)',
            padding: '0.5rem',
          },
          borderBoxNew: {
            padding: '1rem',
          },
          borderBoxRow: {
            display: 'flex',
            padding: '0 0 0.5rem 0',
            '& p': {
              fontWeight: 'normal',
              fontSize: '14px',
              lineHeight: '21px',
              color: 'var(--default-text)',
              margin: 0,
            },
          },
          borderBoxRowTitle: {
            display: 'flex',
            padding: '0.5rem 0',
            '& p': {
              fontSize: '14px',
              fontWeight: 'normal',
              color: 'black',
              margin: 0,
              lineHeight: '12px',
            },
          },
          borderBoxRowModify: {
            display: 'flex',
            padding: '0.5rem 0',
            '& p': {
              fontSize: '14px',
              margin: 0,
              fontWeight: 'bold',
              lineHeight: '21px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            },
          },
          offPeak: {
            marginTop: '1rem',
          },
          offPeakRow: {
            marginLeft: '1rem',
          },
          modified: {
            '& p': {
              color: 'var(--trafineo-rot-100)',
            },
          },
          modifiedOld: {
            border: 'solid 2px var(--trafineo-rot-100)',
          },
          buttonWrapper: {
            marginLeft: 'auto',
            display: 'flex',
            '& button': {
              margin: '0.25rem 0',
            },
          },
          actionContainer: {
            boxSizing: 'border-box',
            position: 'relative',
            margin: '0 auto',
            display: 'flex',
            flexDirection: 'column',
          },
          actionContainerNewTariff: {},
          value: {
            marginLeft: '0.25rem !important',
          },
        }
      : {
          imgContainer: {
            paddingLeft: '0.5rem',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            cursor: 'pointer',
          },
          bold: {
            fontWeight: 'bold !important',
            textAlign: 'left',
          },
          tariffDate: {
            fontWeight: 'bold !important',
          },
          icon: {
            left: '10px',
            top: '40px',
            width: '13px',
            position: 'absolute',
          },
          iconNew: {
            left: '10px',
            top: '45px',
            width: '13px',
            position: 'absolute',
          },
          borderBox: {
            border: 'solid 1px var(--trafineo-grau-70)',
            padding: '0.5rem',
          },
          borderBoxNew: {
            padding: '1rem',
          },
          borderBoxRow: {
            display: 'flex',
            padding: canEdit ? '0.5rem 0' : '0.5rem',
            textAlign: 'left',
            '& p': {
              fontSize: '14px',
              lineHeight: '21px',
              fontWeight: 400,
              margin: 0,
            },
          },
          reminderRow: {
            flexDirection: isMobile ? 'column' : 'row',
            display: 'flex',
            padding: canEdit ? '0.5rem 0' : '0.5rem',
            textAlign: 'left',
            '& p': {
              fontSize: '14px',
              lineHeight: '21px',
              fontWeight: 400,
              margin: 0,
            },
          },
          borderBoxRowTitle: {
            display: 'flex',
            padding: canEdit ? '0.5rem 0' : '0.5rem',
            '& p': {
              fontSize: '14px',
              fontWeight: 'bold',
              margin: 0,
              lineHeight: '21px',
            },
          },
          borderBoxRowModify: {
            display: 'flex',
            padding: '0.5rem 0',
            '& p': {
              fontSize: '14px',
              margin: 0,
              fontWeight: 'bold',
              lineHeight: '21px',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            },
          },
          offPeak: {
            marginTop: '1rem',
          },
          offPeakRow: {
            marginLeft: '1rem',
          },
          modified: {
            '& p': {
              color: 'var(--trafineo-rot-100)',
            },
          },
          modifiedOld: {
            border: 'solid 2px var(--trafineo-rot-100)',
          },
          buttonWrapper: {
            marginLeft: 'auto',
            display: 'flex',
            '& button': {
              margin: '0.25rem 0',
            },
          },
          actionContainer: {
            borderBottom: '1px solid var(--default-text);',
            boxSizing: 'border-box',
            maxWidth: '700px',
            position: 'relative',
            backgroundColor: '#ffffff',
            padding: '1.5rem 0.5rem 1.5rem 2em',
            display: 'flex',
            flexDirection: 'column',
            '&:first-child': {
              borderTop: '1px solid #666666',
            },
          },
          actionContainerNewTariff: {},
          value: {
            marginLeft: '0.25rem !important',
          },
          valueMobile: {
            marginTop: '0.25rem !important',
          },
        },
  );
  const classes = useStyles();
  const { t } = useTranslation('evDriver');
  const disabled = pendingTariff || editDisabled;
  const pendingTariffId =
    pendingTariff?.tariff_elements[0].functional_tariff_id;
  const navigate = useNavigate();
  const { evDriverDispatch } = useContext(evDriverContext);

  const SingleTariff = ({
    comment,
    tariffToRender,
    index,
    isPending,
    tariffModified,
    reminder,
    isGreen,
    environmentImpact,
  }) => {
    return (
      <Box
        data-cy="electricityTariff"
        className={`${
          tariffs
            ? classes.actionContainer
            : newTheme
            ? classes.borderBoxNew
            : classes.borderBox
        } ${
          tariffModified
            ? newTheme
              ? classes.modified
              : classes.modifiedOld
            : ''
        }`}
      >
        {!hideIcon && (
          <img
            src={TariffIcon}
            alt="Tariff"
            className={newTheme ? classes.iconNew : classes.icon}
          />
        )}
        {!currentTariff && (
          <Box
            flexDirection="row"
            className={
              canEdit ? classes.borderBoxRowModify : classes.borderBoxRowTitle
            }
          >
            {tariffs ? (
              <p>{`${t('tariffCount')} ${index + 1}:`}</p>
            ) : (
              <p>{t('electricyTarif')}</p>
            )}
            {canEdit && (
              <div className={classes.buttonWrapper}>
                <EditButton
                  disabled={!tariffModified && disabled}
                  onClick={() => {
                    evDriverDispatch({
                      type: 'tariffType',
                      value: {
                        tariffType: canSelectTariffType ? null : 'ONETARIFF',
                      },
                    });
                    navigate(
                      canSelectTariffType
                        ? `/tariffselection?id=${index}`
                        : `/electricityTariff?id=${index}`,
                      { replace: true },
                    );
                  }}
                />
                {tariffModified && (
                  <RevertButton
                    onClick={onRevert}
                    variant="smallSec"
                    data-cy="revertTariff"
                  />
                )}
              </div>
            )}
          </Box>
        )}

        <Box className={classes.borderBoxRow}>
          <p data-cy="tariffDate" className={classes.tariffDate}>
            {t('tariffValidFrom')}
            {':'}
          </p>
          <p className={classes.value}>
            {isDateValid(new Date(tariffToRender[0].restrictions[0].start_date))
              ? new Intl.DateTimeFormat('de-DE', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                }).format(
                  new Date(tariffToRender[0].restrictions[0].start_date),
                )
              : '-'}
          </p>
        </Box>
        <Box className={classes.borderBoxRow}>
          <Box>
            <Box display="flex">
              <p data-cy="tariffPrice" className={classes.bold}>
                {t('tariffPrice')}
              </p>
              <p className={classes.value}>
                {tariffToRender[0].price_components[0].price}
                {' EUR / kWh'}
              </p>
            </Box>
            {tariffToRender.length > 1 && (
              <>
                <Box display="flex">
                  <p data-cy="tariffOffPeakPrice" className={classes.bold}>
                    {t('tariffOffPeakPrice')}
                  </p>
                  <p className={classes.value}>
                    {tariffToRender[1].price_components[0].price}
                    {' EUR / kWh'}
                  </p>
                </Box>
                <Box className={classes.offPeak}>
                  <p className={classes.bold}>
                    {t('tariffValidityPeriodOffPeak')}
                  </p>
                  <Box className={classes.offPeakRow}>
                    <p>
                      {t('tariffMondayToFriday')}{' '}
                      {t('tariffFromTo', {
                        from: tariffToRender[1].restrictions[0].start_time,
                        to: tariffToRender[1].restrictions[0].end_time,
                      })}
                    </p>
                  </Box>
                  <Box className={classes.offPeakRow}>
                    <p>
                      {t('tariffSaturdaySunday')}{' '}
                      {tariffToRender.length > 2
                        ? t('tariffAllDay')
                        : t('tariffFromTo', {
                            from: tariffToRender[1].restrictions[0].start_time,
                            to: tariffToRender[1].restrictions[0].end_time,
                          })}
                    </p>
                  </Box>
                </Box>
              </>
            )}
          </Box>
        </Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="greenEnergy" className={classes.bold}>
            {t('greenEnergy')}
          </p>
          <p className={classes.value}>
            {isGreen === null
              ? t('greenTariffNotProvided')
              : isGreen === false
              ? t('falseGreenTariff')
              : environmentImpact
              ? t('falseGreenTariff')
              : t('trueGreenTariff')}
          </p>
        </Box>
        {(isGreen || environmentImpact) && (
          <Box className={classes.borderBoxRow}>
            <p data-cy="emissionRate" className={classes.bold}>
              {t('emissionRate')}
            </p>
            <p className={classes.value}>
              {isGreen
                ? '0.00 g/kWh'
                : environmentImpact + ' g/kWh'
                ? environmentImpact + ' g/kWh'
                : 'generalRequestError'}
            </p>
          </Box>
        )}

        {reminder !== undefined && (
          <Box className={classes.reminderRow}>
            <p data-cy="reminder" className={classes.tariffDate}>
              {t('reminder')}
              {':'}
            </p>
            <p className={isMobile ? classes.valueMobile : classes.value}>
              {reminder > 0
                ? t('reminderActive', { months: reminder })
                : t('reminderInactive')}
            </p>
          </Box>
        )}
        {isPending && files && (
          <Box className={classes.reminderRow}>
            <div
              style={{
                display: 'flex',
                flexDirection: 'column',
                width: '100%',
              }}
            >
              <p data-cy="files" className={classes.tariffDate}>
                {t('files')}
                {':'}
              </p>
              <FileDownload idpId={idpId} files={files} />
            </div>
          </Box>
        )}
        <Box className={classes.reminderRow}>
          <p data-cy="comments" className={classes.bold}>
            {t('Comments')}
          </p>
          <p className={classes.value}>{tariffToRender.comment || comment}</p>
        </Box>
      </Box>
    );
  };

  const NewTariff = () => {
    return (
      <Box
        className={`${classes.actionContainer} ${classes.actionContainerNewTariff}`}
      >
        <img
          src={TariffIcon}
          alt="Tariff"
          className={newTheme ? classes.iconNew : classes.icon}
        />
        <Box
          flexDirection="row"
          display="flex"
          alignItems="center"
          className={classes.borderBoxRow}
        >
          <p className={classes.bold}>{`${t('tariffCount')} ${
            tariffs.length + 1
          }:`}</p>
          <div className={classes.buttonWrapper}>
            <AddButton
              onClick={() => {
                onRevert();
                navigate(
                  canSelectTariffType
                    ? '/tariffselection'
                    : '/electricityTariff',
                );
              }}
              disabled={disabled}
            />
          </div>
        </Box>
        <Box>
          <Box className={classes.borderBoxRow}>
            <p>{t('none')}</p>
          </Box>
        </Box>
      </Box>
    );
  };

  const TariffList = () => {
    return tariffs.map((tariff, i) => {
      const currentTariffId = tariff.tariff_elements[0].functional_tariff_id;
      const isTariffModified = pendingTariffId === currentTariffId;
      return (
        <SingleTariff
          index={i}
          tariffModified={isTariffModified}
          isPending={isTariffModified}
          comment={comment}
          tariffToRender={
            isTariffModified
              ? pendingTariff.tariff_elements
              : tariff.tariff_elements
          }
        />
      );
    });
  };

  if (!tariffs && !pendingTariff) {
    return <></>;
  }

  if (currentTariff) {
    const currentTariffElement = tariffs.filter((e) => e.current_tariff)[0];
    if (currentTariffElement) {
      return (
        <SingleTariff
          tariffToRender={currentTariffElement.tariff_elements}
          comment={comment}
        />
      );
    }
    return '-';
  }

  if (!tariffs && pendingTariff) {
    return (
      <SingleTariff
        tariffModified={dataApproval}
        isPending
        reminder={reminder}
        tariffToRender={pendingTariff.tariff_elements}
        isGreen={
          pendingTariff?.energy_mix?.is_green_energy
            ? pendingTariff?.energy_mix?.is_green_energy
            : pendingTariff?.energy_mix?.is_green_energy === false
            ? false
            : null
        }
        environmentImpact={
          pendingTariff?.energy_mix?.environ_impact?.find(
            (e) => e.category === 'CARBON_DIOXIDE',
          ).amount
        }
        comment={comment}
      />
    );
  }
  return (
    <>
      <TariffList />
      {(pendingTariffId === null && (
        <SingleTariff
          isPending
          tariffModified
          index={tariffs.length}
          tariffToRender={pendingTariff.tariff_elements}
          comment={comment}
        />
      )) ||
        (canEdit && <NewTariff />)}
    </>
  );
};

export default LegacyTariffs;
