import { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import requester from '../../utils/requester';
import styled from 'styled-components';
import { Dialog, FormControlLabel, Switch } from '@material-ui/core';
import infoIcon from '../../static/img/icons/Info_Icon.svg';

import {
  Error,
  InformationContainer,
  InformationIcon,
  InformationText,
  Row,
} from '../evDriver/PageTemplate';
import { Button } from '..';
import SuccessMessage from '../SuccessMessage';
import CircularProgress from '../CircularProgress';

import CloseIcon from '../myTeam/CloseIcon';

const DialogWrapper = styled.div`
  position: relative;
  width: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  width: 500px;
  input {
    width: 500px;
    background-color: white;
    ::placeholder {
      color: grey;
      opacity: 1;
    }
  }
  button {
    width: fit-content;
    margin-left: 0.5rem;
  }
`;

const ActionDescription = styled.div`
  margin: 2rem 0;
  font-size: 18px;
  line-height: 24px;
  font-weight: bold;
`;
const ButtonWrapper = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
`;

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const ErrorContainer = styled.div`
  margin-bottom: 1rem;
`;

const StyledSwitch = styled(Switch)`
  .MuiSwitch-colorSecondary.Mui-checked {
    color: var(--trafineo-rot-100);
  }
  .MuiSwitch-colorSecondary.Mui-checked + .MuiSwitch-track {
    background-color: var(--trafineo-rot-100);
  }
`;

const StyledFormControlLabel = styled(FormControlLabel)`
  span {
    font-size: 14px !important;
  }
`;

const PrincipleDialog = ({
  open,
  onClose,
  currentPrinciple,
  pendingActions,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [principle, setPrinciple] = useState(currentPrinciple === 'foureye');
  const mixedPrinciple = currentPrinciple === 'mixed';
  const { t } = useTranslation('directCustomer');
  const tServiceProvider = useTranslation('serviceProvider').t;
  const resetComponent = () => {
    setShowError(false);
    setIsLoading(false);
    setShowSuccess(false);
    setPrinciple(currentPrinciple === 'foureye');
  };

  useEffect(() => {
    if (open) {
      resetComponent();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [open]);

  const action = useCallback(async () => {
    try {
      const rsp = await requester().patch(
        `/authorities/company`,
        {
          principle: principle === true ? 'foureye' : 'twoeye',
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      return rsp;
    } catch (e) {
      console.error(e);
      return null;
    }
  }, [principle]);

  const performAction = async () => {
    setIsLoading(true);
    const rspData = await action();
    setShowError(rspData ? false : true);
    setShowSuccess(rspData ? true : false);
    setIsLoading(false);
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <CloseIcon onClick={onClose} />
      {showSuccess ? (
        <SuccessMessage message={t('managePrincipleSucess')} />
      ) : (
        <DialogWrapper>
          <ActionDescription>{t('managePrinciple')}</ActionDescription>
          {pendingActions === 0 && mixedPrinciple && (
            <InformationContainer style={{ marginBottom: '1.5rem' }}>
              <InformationIcon>
                <img src={infoIcon} alt="success" />
              </InformationIcon>
              <InformationText>
                {t('mixedPrincipleDescription')}
              </InformationText>
            </InformationContainer>
          )}
          <Row>
            <StyledFormControlLabel
              control={
                <StyledSwitch
                  disabled={pendingActions > 0}
                  checked={principle}
                  onChange={() => setPrinciple(!principle)}
                  name="principle"
                />
              }
              label={t('principleDescription')}
            />
          </Row>
          <Row>
            {pendingActions === 0 ? (
              <>
                <InformationContainer>
                  <InformationIcon>
                    <img src={infoIcon} alt="success" />
                  </InformationIcon>
                  <InformationText>{t('principleExplanation')}</InformationText>
                </InformationContainer>
              </>
            ) : (
              <InformationContainer>
                <InformationIcon>
                  <img src={infoIcon} alt="success" />
                </InformationIcon>
                <InformationText>
                  {t('principlePendingActions', {
                    pendingActions: pendingActions,
                  })}
                </InformationText>
              </InformationContainer>
            )}
          </Row>
          {showError && (
            <ErrorContainer>
              <Error>{tServiceProvider('requestError')}</Error>
            </ErrorContainer>
          )}
          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button onClick={onClose} variant="secondary">
                {tServiceProvider('cancelButton')}
              </Button>
              <Button
                disabled={
                  currentPrinciple !== 'mixed' &&
                  (currentPrinciple === 'foureye') === principle
                }
                onClick={performAction}
                variant="primary"
              >
                {t('save')}
              </Button>
            </ButtonWrapper>
          )}
        </DialogWrapper>
      )}
    </Dialog>
  );
};

PrincipleDialog.propTypes = {
  open: PropTypes.bool,
  action: PropTypes.string,
  email: PropTypes.string,
  id: PropTypes.number,
  onClose: PropTypes.func,
};

export default PrincipleDialog;
