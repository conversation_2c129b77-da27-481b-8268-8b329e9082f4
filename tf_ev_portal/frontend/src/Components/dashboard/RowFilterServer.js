import polygon from '../../static/img/polygon.svg';
import polygonEmpty from '../../static/img/polygonEmpty.svg';
import styled from 'styled-components';

export const Filter = styled.span`
  width: 9px;
  height: 6px;
  margin: 0.125rem 0.25rem;
`;

export const StyledImg = styled.img`
  ${(props) => (props.flip === true ? 'transform: rotateX(180deg)' : '')};
`;

export const FilterButton = styled.button`
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  font-weight: bold;
  padding: 0;
  font-size: 14px !important;
`;
const RowFilterServer = ({ sortAttr, text, sort, setSort }) => {
  const sortSelected = sort.attr === sortAttr;
  const order = sortSelected ? (sort.order === 'desc' ? 'asc' : 'desc') : 'asc';
  return (
    <FilterButton
      onClick={() => {
        setSort({
          attr: sortAttr,
          order: order,
        });
      }}
      type="button"
    >
      {text}
      <Filter>
        <StyledImg
          flip={sortSelected && sort.order === 'desc'}
          src={sortSelected ? polygon : polygonEmpty}
          alt="sort"
        />
      </Filter>
    </FilterButton>
  );
};

export default RowFilterServer;
