import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Menu, MenuItem } from '@material-ui/core';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import Button from '../Button';
import UploadDialog from './UploadDialog';
import requester from '../../utils/requester';
import logger from '../../utils/logger';
import { downloadExcel } from '../../utils/helper';
import ManageCompanyDialog from '../superUser/ManageCompanyDialog';
import approvalStatus from '../../constants/approvalStatus';
import Checkbox from '../Checkbox';
import CancelButton from '../CancelButton';
import PublishIcon from '@material-ui/icons/Publish';
import GetAppIcon from '@material-ui/icons/GetApp';
import FilterListIcon from '@material-ui/icons/FilterList';
import TextField from '../TextField';
import PrincipleDialog from './PrincipleDialog';
import { isEqual } from 'lodash';
import TagManager from 'react-gtm-module';

export const DashboardFilterContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
  width: 100%;
`;

export const QuickFilterButton = styled.button`
  cursor: pointer;
  color: ${(props) => props.textColor};
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  height: 38px;
  flex-grow: 0;
  margin-right: 1rem;
  padding: 6px 23px 6px 6px;
  opacity: ${(props) => (props.active ? '1' : '0.6')};
  border-radius: 50px;
  border: solid 0.5px;
  border-color: ${(props) => props.textColor};
  background-color: ${(props) => props.bgColor};
  &:hover {
    filter: brightness(90%);
  }
`;

export const QuickFilterContainer = styled.div`
  display: flex;
  flex-direction: row;
  align-items: flex-end;
  margin-bottom: 2rem;
  width: 100%;
`;

export const QuickFilterCount = styled.div`
  min-width: 22px;
  height: 22px;
  padding: 0.125rem;
  color: white;
  margin-right: 12px;
  border-radius: 50px;
  justify-content: center;
  align-items: center;
  display: flex;
  background-color: ${(props) => props.bgColor};
`;

const ButtonWrapper = styled.div`
  position: relative;
`;

const ButtonContainer = styled.div`
  width: fit-content;
  margin-left: 0.5rem;
  & svg {
    height: 14px;
    width: 14px;
    margin-right: 3px;
  }
`;

const AllCardsButton = styled.button`
  cursor: pointer;
  color: black;
  display: flex;
  justify-content: center;
  align-items: center;
  width: auto;
  font-size: 14px;
  height: 100px;
  flex-grow: 0;
  padding: 1rem;
  margin-right: 2rem;
  border-radius: 10px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.04);
  border: solid 0.5px lightgray;
  background-color: #fff;
  background-color: white;
  &:hover {
    filter: brightness(90%);
  }
`;

const AllCardsCount = styled.div`
  width: 50px;
  height: 50px;
  color: white;
  font-size: 16px;
  font-weight: bold;
  border-radius: 30px;
  justify-content: center;
  align-items: center;
  display: flex;
  background-color: var(--trafineo-rot-100);
`;

const AllCardsLabel = styled.div`
  width: 200px;
`;

const QuickFilterText = styled.div`
  font-size: 14px;
  font-weight: bold;
  margin-bottom: 1rem;
`;

const QuickFilters = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const ActionsContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex-direction: row;
  justify-content: flex-end;
  width: 100%;
`;

const QuickFiltersRow = styled.div`
  display: flex;
  width: 100%;
`;

const StyledMenuItem = styled(MenuItem)`
  font-size: 14px;
  padding: 0.25rem;
  &:hover {
    opacity: 0.7;
    background: none !important;
  }
`;

const StyledMenu = styled(Menu)``;

const MenuWrapper = styled.div`
  padding: 1rem;
`;

const FilterTitle = styled.div`
  font-size: ${(props) => (props.headline ? '16px' : '14px')};
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  line-height: 1.79;
  letter-spacing: normal;
  text-align: left;
  color: #000;
  margin-bottom: 0.5rem;
`;

const Hr = styled.div`
  width: 100%;
  margin: 1rem 0;
  border: 0.5px solid var(--trafineo-rot-100);
`;

const CheckboxText = styled.div`
  margin-left: ${(props) => (props.noMargin ? '0' : '0.5rem')};
  font-size: 14px;
  font-weight: normal;
  font-stretch: normal;
  font-style: normal;
  line-height: 0.79;
  letter-spacing: normal;
  text-align: left;
  color: var(--default-text);
`;
const ApplyButtonWrapper = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 1rem;
  align-items: center;
  & button {
    width: fit-content;
  }
`;
export const CustomFilter = ({
  customFilters,
  setCustomFilters,
  resetCustomFilters,
  resetFilter,
  disabled = false,
}) => {
  const { t } = useTranslation('directCustomer');
  const [anchorEl, setAnchorEl] = useState(null);
  const [filters, setFilters] = useState(customFilters);

  useEffect(() => {
    setFilters(customFilters);
  }, [customFilters]);

  const applyFilters = () => {
    setCustomFilters(filters);
    resetFilter();
    handleClose();
  };

  const handleClick = (event) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };
  const open = Boolean(anchorEl);

  if (!filters) {
    return '';
  }

  const isFilterActive =
    Object.entries(customFilters.status).filter((e) => {
      return e[1];
    }).length +
      Object.entries(customFilters.card).filter((e) => {
        return e[1];
      }).length +
      Object.entries(customFilters.reimbursement).filter((e) => {
        return e[1];
      }).length >
    0;

  return (
    <>
      <div style={{ position: 'relative' }}>
        <Button
          disabled={disabled}
          disableRipple
          aria-controls="basic-menu"
          aria-haspopup="true"
          variant={'grey'}
          style={{
            boxShadow: 'none',
            height: '34px',
            borderColor: isFilterActive ? 'var(--trafineo-rot-100)' : '#707070',
          }}
          aria-expanded={open ? 'true' : undefined}
          onClick={handleClick}
        >
          <FilterListIcon fontSize="small" />
          {'Filter'}
        </Button>
        {isFilterActive && <CancelButton onClick={resetCustomFilters} />}
      </div>

      <StyledMenu
        anchorEl={anchorEl}
        open={open}
        disableScrollLock
        onClose={handleClose}
        getContentAnchorEl={null}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'center',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'center',
        }}
        MenuListProps={{
          'aria-labelledby': 'basic-button',
        }}
      >
        <MenuWrapper>
          <FilterTitle headline>{t('filterHeadline')}</FilterTitle>
          <CheckboxText noMargin>{t('filterDescription')}</CheckboxText>
          <Hr />
          <FilterTitle>{t('reimbursementFilters')}</FilterTitle>
          {Object.keys(filters.reimbursement).map((e) => {
            return (
              <StyledMenuItem
                onClick={() => {
                  setFilters({
                    ...filters,
                    reimbursement: {
                      ...filters.reimbursement,
                      [e]: !filters.reimbursement[e],
                    },
                  });
                }}
                key={e}
                disableRipple
              >
                <Checkbox
                  checked={filters.reimbursement[e]}
                  boxSize="small"
                ></Checkbox>
                <CheckboxText>{t(`${e}Filter`)}</CheckboxText>
              </StyledMenuItem>
            );
          })}
          <Hr />
          <FilterTitle>{t('cardFilters')}</FilterTitle>
          {Object.keys(filters.card).map((e) => {
            return (
              <StyledMenuItem
                key={e}
                onClick={() => {
                  setFilters({
                    ...filters,
                    card: {
                      ...filters.card,
                      [e]: !filters.card[e],
                    },
                  });
                }}
                disableRipple
              >
                <Checkbox checked={filters.card[e]} boxSize="small"></Checkbox>
                <CheckboxText>{t(`${e}Filter`)}</CheckboxText>
              </StyledMenuItem>
            );
          })}
          <Hr />
          <FilterTitle>{t('statusFilters')}</FilterTitle>
          {Object.keys(filters.status).map((e) => {
            return (
              <StyledMenuItem
                onClick={() => {
                  setFilters({
                    ...filters,
                    status: {
                      ...filters.status,
                      [e]: !filters.status[e],
                    },
                  });
                }}
                key={e}
                disableRipple
              >
                <Checkbox
                  checked={filters.status[e]}
                  boxSize="small"
                ></Checkbox>
                <CheckboxText>{t(`${e}Filter`)}</CheckboxText>
              </StyledMenuItem>
            );
          })}
          <ApplyButtonWrapper>
            {
              <Button
                disabled={isEqual(filters, customFilters)}
                variant="primary"
                onClick={applyFilters}
              >
                {t('applyFilters')}
              </Button>
            }
          </ApplyButtonWrapper>
        </MenuWrapper>
      </StyledMenu>
    </>
  );
};

const DashboardFilter = ({
  filter,
  setFilter,
  data,
  setData,
  superUser,
  fleetManagerId,
  provider,
  onboardingMode,
  fmFunction,
  principle,
}) => {
  const { t } = useTranslation('directCustomer');
  const [changeCompanyData, setChangeCompanyData] = useState(null);
  const [isUploadDialogOpen, setIsUploadDialogOpen] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [isPrincipleDialogVisible, setIsPrincipleDialogVisible] =
    useState(false);
  const [searchInput, setSearchInput] = useState('');

  const onDownloadClick = () => {
    const rows = JSON.parse(JSON.stringify(data)).map((row) => {
      row.driver_approval_status = undefined;
      return row;
    });
    downloadExcel(`Overview_Reimbursement.xlsx`, rows);
  };

  const onClose = (isUploaded) => {
    if (isUploaded) {
      window.location.reload();
    } else {
      setIsUploadDialogOpen(!isUploadDialogOpen);
    }
  };
  const onSearch = (e) => {
    const input = e.target.value;
    setSearchInput(input);
    const filteredCards = JSON.parse(JSON.stringify(data)).filter((card) => {
      card.company_id = card.companies[0].company_ids[0].company_id;
      card.email = card.contact.email;
      let found = false;
      try {
        ['company_id', 'email'].forEach((el) => {
          if (!found) {
            found = card[el]
              ? card[el]
                  .toString()
                  .toLowerCase()
                  .indexOf(input.trim().toLowerCase()) !== -1
              : false;
          }
          return found;
        });
      } catch {
        return found;
      }
      return found;
    });
    setFilter(null);
    setData(filteredCards);
  };

  const quickFiltersOnboarding = [
    {
      label: t('pending'),
      value: data.filter((e) => e.registration_status === 'pending').length,
      onClick: () => data.filter((e) => e.registration_status === 'pending'),
      bgColor: '#ebf9ff',
      textColor: '#31a2d8',
    },
    {
      label: t('approved'),
      value: data.filter((e) => e.registration_status === 'approved').length,
      onClick: () => data.filter((e) => e.registration_status === 'approved'),
      bgColor: 'rgba(153, 204, 0, 0.27)',
      textColor: '#090',
    },
    {
      label: t('rejected'),
      value: data.filter((e) => e.registration_status === 'rejected').length,
      onClick: () => data.filter((e) => e.registration_status === 'rejected'),
      bgColor: 'rgba(255, 175, 170, 0.3)',
      textColor: '#fe5349',
    },
  ];

  const quickFilters = [
    {
      label: t('totalCards'),
      value: data.length,
      onClick: () => data,
    },

    {
      label: t('activeCards'),
      value: data.filter((e) => e.card_status).length,
      onClick: () => data.filter((e) => e.card_status),
      bgColor: 'rgba(153, 204, 0, 0.27)',
      textColor: '#090',
    },
    {
      label: t('expiringSoonCards'),
      value: data.filter((e) => e.expiring_soon && e.card_status).length,
      onClick: () => data.filter((e) => e.expiring_soon && e.card_status),
      bgColor: 'rgba(245, 219, 136, 0.5)',
      textColor: '#ff7e00',
    },
    {
      label: t('activeReimbursements'),
      value: data.filter((e) => e.reimbursement_status_id === 2).length,
      onClick: () => data.filter((e) => e.reimbursement_status_id === 2),
      bgColor: '#c0e4f4',
      textColor: '#31a2d8',
    },
    {
      label: t('pendingActions'),
      value: data.filter((e) => {
        return (
          e.driver_approval_status?.filter(
            (el) =>
              el.driver_approval_status_name ===
                approvalStatus.initialDataEntered ||
              el.driver_approval_status_name ===
                approvalStatus.tariffModified ||
              el.driver_approval_status_name ===
                approvalStatus.bankingModified ||
              el.driver_approval_status_name === approvalStatus.wallboxModified,
          ).length > 0
        );
      }).length,
      onClick: () =>
        data.filter((e) => {
          return (
            e.driver_approval_status?.filter(
              (el) =>
                el.driver_approval_status_name ===
                  approvalStatus.initialDataEntered ||
                el.driver_approval_status_name ===
                  approvalStatus.tariffModified ||
                el.driver_approval_status_name ===
                  approvalStatus.bankingModified ||
                el.driver_approval_status_name ===
                  approvalStatus.wallboxModified,
            ).length > 0
          );
        }),
      bgColor: '#9370db40',
      textColor: '#9370db',
    },
  ];

  const getCompanyData = async () => {
    try {
      const { fleetManagers } = (
        await requester().get('/authorities/fleetmanagers')
      ).data;
      return fleetManagers;
    } catch (err) {
      logger().error(`Couldn't get overview data from api.\n${err.message}`);
      return [];
    }
  };
  const manageCompanyId = async () => {
    const companyData = await getCompanyData();
    setChangeCompanyData(
      companyData.filter((company) => company.IdpId === fleetManagerId)[0],
    );
    setIsDialogVisible(true);
  };
  const managePrinciple = async () => {
    setTimeout(() => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_manage_principle`,
            interaction_type: 'open',
          },
        },
      });
    }, 200);
    setIsPrincipleDialogVisible(true);
  };

  return (
    <DashboardFilterContainer>
      <QuickFilterContainer>
        <PrincipleDialog
          open={isPrincipleDialogVisible}
          pendingActions={
            data.filter((e) => {
              return (
                e.driver_approval_status?.filter(
                  (el) =>
                    el.driver_approval_status_name ===
                      approvalStatus.initialDataEntered ||
                    el.driver_approval_status_name ===
                      approvalStatus.tariffModified ||
                    el.driver_approval_status_name ===
                      approvalStatus.bankingModified ||
                    el.driver_approval_status_name ===
                      approvalStatus.wallboxModified,
                ).length > 0
              );
            }).length
          }
          currentPrinciple={principle}
          onClose={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'popup_interaction',
                popup: {
                  name: `popup_manage_principle`,
                  interaction_type: 'close',
                },
              },
            });
            window.location.reload();
          }}
        />

        <ManageCompanyDialog
          open={isDialogVisible}
          data={changeCompanyData}
          onClose={() => {
            setIsDialogVisible(false);
          }}
        />
        {!onboardingMode && (
          <AllCardsButton
            onClick={() => {
              setFilter(null);
              setSearchInput('');
              setData(data);
            }}
          >
            <AllCardsCount>{quickFilters[0].value}</AllCardsCount>
            <AllCardsLabel>{t(quickFilters[0].label)}</AllCardsLabel>
          </AllCardsButton>
        )}
        <QuickFilters>
          <QuickFilterText>{t('quickFilters')}</QuickFilterText>
          <QuickFiltersRow>
            {(onboardingMode ? quickFiltersOnboarding : quickFilters).map(
              (e, i) => {
                if (onboardingMode ? true : i > 0) {
                  return (
                    <ButtonWrapper key={e.label}>
                      <QuickFilterButton
                        textColor={e.textColor}
                        bgColor={e.bgColor}
                        active={filter === e.label}
                        onClick={() => {
                          if (filter === e.label) {
                            setFilter(null);
                            setSearchInput('');
                            setData(data);
                          } else {
                            setFilter(e.label);
                            setSearchInput('');
                            setData(e.onClick());
                          }
                        }}
                      >
                        <QuickFilterCount bgColor={e.textColor}>
                          {e.value}
                        </QuickFilterCount>
                        {t(e.label)}
                      </QuickFilterButton>
                      {filter === e.label && (
                        <CancelButton
                          color={e.textColor}
                          onClick={() => {
                            setFilter(null);
                            setSearchInput('');
                            setData(data);
                          }}
                        />
                      )}
                    </ButtonWrapper>
                  );
                }
                return <></>;
              },
            )}
          </QuickFiltersRow>
        </QuickFilters>
      </QuickFilterContainer>
      <ActionsContainer>
        <ButtonContainer>
          <TextField
            search
            value={searchInput}
            type="text"
            onChange={onSearch}
          />
        </ButtonContainer>
        {!onboardingMode && (
          <>
            {superUser && (provider === 'aral' || provider === 'bp') && (
              <ButtonContainer>
                <Button onClick={manageCompanyId} variant="grey">
                  {t('manageCompany')}
                </Button>
              </ButtonContainer>
            )}
            {!superUser && fmFunction === 'admin' && (
              <ButtonContainer>
                <Button onClick={managePrinciple} variant="grey">
                  {t('managePrinciple')}
                </Button>
              </ButtonContainer>
            )}
            <ButtonContainer>
              <Button onClick={onDownloadClick} variant="grey">
                <GetAppIcon />
                {t('downloadData')}
              </Button>
            </ButtonContainer>
            {!superUser && (
              <ButtonContainer>
                <Button
                  data-cy="uploadDialogButton"
                  onClick={() => {
                    setIsUploadDialogOpen(true);
                  }}
                  variant="grey"
                >
                  <PublishIcon />
                  {t('uploadData')}
                </Button>
              </ButtonContainer>
            )}
          </>
        )}

        <ButtonContainer>
          <CustomFilter />
        </ButtonContainer>
      </ActionsContainer>
      <UploadDialog
        title={t('uploadCompanyData')}
        open={isUploadDialogOpen}
        onClose={onClose}
      />
    </DashboardFilterContainer>
  );
};

DashboardFilter.defaultProps = {
  data: [],
};

DashboardFilter.propTypes = {
  data: PropTypes.arrayOf(
    PropTypes.shape({
      card_number: PropTypes.string,
    }),
  ),
  disabled: PropTypes.bool,
};

export default DashboardFilter;
