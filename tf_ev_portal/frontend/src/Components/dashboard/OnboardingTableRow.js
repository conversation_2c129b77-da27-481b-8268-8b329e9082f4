/* eslint-disable react/jsx-curly-newline */
import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import styled from 'styled-components';
import { IconButton, Tooltip } from '@material-ui/core';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';

import KeyboardArrowDown from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUp from '@material-ui/icons/KeyboardArrowUp';

import {
  InfoBox,
  Row,
  RowBox,
  RowContainer,
  StatusIndicator,
} from './RowTemplates';
import ApproveDialog from './ApproveDialog';
import { formatDateTime } from '../../utils/helper';

const StyledCloseIcon = styled(CloseIcon)`
  height: 1rem !important;
  width: 1rem !important;
  margin-bottom: -3px;
`;

const RowExpandContainer = styled.div`
  display: flex;
  font-size: 14px;
  width: 100%;
  flex-grow: 2;
  background-color: #fafafa;
  position: relative;
  z-index: 0;
  margin-bottom: 1;
  box-shadow: 0 0px 2px 0 rgba(0, 0, 0, 0.25);
  padding-bottom: 1rem;
`;

const ActionButtonContainer = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
`;

const Green = styled.div`
  svg {
    color: #99cc00;
  }
`;
const Red = styled.div`
  svg {
    color: red;
  }
`;

const InfoHeadline = styled.div`
  margin: 0.75rem 0 !important;
  color: black;
  font-weight: bold;
`;
const InfoLabel = styled.span`
  font-weight: bold;
`;
const InfoRow = styled.div`
  margin: 0 0 0.75rem 0 !important;
`;

const MailRow = styled.div`
  color: ${(props) => (props.valid ? 'var(--default-text);' : 'red')};
  margin: 0 0 0.25rem 0 !important;
`;

const OnboardingTableRow = ({ rowData }) => {
  const { t } = useTranslation('superuser');
  const [expanded, setExpanded] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [action, setAction] = useState(null);

  const {
    id,
    country_code,
    companies,
    customer_type,
    cards_sum,
    principle,
    registration_status,
    emailadresses,
    contact,
    valid,
    created_date,
    keyaccountmanager,
  } = rowData;

  const RowExpand = () => {
    return (
      <RowExpandContainer>
        <InfoBox width={0.5}></InfoBox>
        <InfoBox width={20}>
          <InfoHeadline>
            {t('contactInformation')}
            {':'}
          </InfoHeadline>
          <InfoRow>
            <InfoLabel>{t('name')}: </InfoLabel>
            {contact.firstname || '-'}
          </InfoRow>
          <InfoRow>
            <InfoLabel>{t('lastname')}: </InfoLabel>
            {contact.lastname || '-'}
          </InfoRow>
          <InfoRow>
            <InfoLabel>{t('email')}: </InfoLabel>
            {contact.email || '-'}
          </InfoRow>
          <InfoRow>
            <InfoLabel>{t('companyName')}: </InfoLabel>
            {contact.company_name || '-'}
          </InfoRow>
          <InfoRow>
            <InfoLabel>{t('phoneNumber')}: </InfoLabel>
            {contact.phonenumber || '-'}
          </InfoRow>
        </InfoBox>
        <InfoBox width={25}>
          <InfoHeadline>
            {t('onboardedMails')}
            {':'}
          </InfoHeadline>
          {emailadresses.map((e) => (
            <MailRow valid={e.valid}>
              {e.email} {e.valid ? null : <StyledCloseIcon />}
            </MailRow>
          ))}
          <InfoHeadline>
            {t('onboardedIds')}
            {':'}
          </InfoHeadline>
          {companies[0].company_ids.map((e) => {
            return (
              <MailRow valid={e.valid}>
                {e.company_id} {e.valid ? null : <StyledCloseIcon />}
              </MailRow>
            );
          })}
        </InfoBox>
        {keyaccountmanager && (
          <InfoBox width={50}>
            <InfoHeadline>
              {t('keyAccountEmail')}
              {':'}
            </InfoHeadline>
            <MailRow valid={true}>{keyaccountmanager.email}</MailRow>
          </InfoBox>
        )}
      </RowExpandContainer>
    );
  };

  const StatusCol = ({ status }) => {
    switch (status) {
      case 'pending':
        return (
          <ActionButtonContainer>
            <Red>
              <Tooltip title={t('rejectAction')}>
                <IconButton
                  data-cy="rejectAction"
                  onClick={() => {
                    setAction('reject');
                    setIsDialogVisible(true);
                  }}
                >
                  <CloseIcon />
                </IconButton>
              </Tooltip>
            </Red>
            <Green>
              <Tooltip title={t('approveAction')}>
                <IconButton
                  data-cy="approveAction"
                  onClick={() => {
                    setAction('approve');
                    setIsDialogVisible(true);
                  }}
                >
                  <CheckIcon />
                </IconButton>
              </Tooltip>
            </Green>
          </ActionButtonContainer>
        );
      case 'approved':
        return (
          <StatusIndicator
            margin
            color="#090"
            background="rgba(153, 204, 0, 0.27)"
          >
            {t('approved')}
          </StatusIndicator>
        );
      case 'rejected':
        return (
          <StatusIndicator
            margin
            color="#fe5349"
            background="rgba(255, 175, 170, 0.3)"
          >
            {t('rejected')}
          </StatusIndicator>
        );
      default:
        return <div>-</div>;
    }
  };

  return (
    <Row data-cy="tableRow">
      <ApproveDialog
        open={isDialogVisible}
        onClose={() => window.location.reload()}
        email={contact.email}
        id={id}
        action={action}
      />
      <RowContainer expanded={expanded}>
        <RowBox width={2.5}></RowBox>
        <RowBox width={22.5}>{contact.email}</RowBox>
        <RowBox width={7.5}>{customer_type}</RowBox>
        <RowBox center width={10}>
          {companies[0].company_ids[0].company_id}
        </RowBox>
        <RowBox center width={5}>
          {cards_sum}
        </RowBox>
        <RowBox center width={8.5}>
          {country_code}
        </RowBox>
        <RowBox center width={7.5}>
          {principle}
        </RowBox>
        <RowBox center width={10}>
          {valid ? (
            <StatusIndicator
              margin
              color="#090"
              background="rgba(153, 204, 0, 0.27)"
            >
              {t('valid')}
            </StatusIndicator>
          ) : (
            <StatusIndicator
              margin
              color="#fe5349"
              background="rgba(255, 175, 170, 0.3)"
            >
              {t('invalid')}
            </StatusIndicator>
          )}
        </RowBox>
        <RowBox center width={10}>
          <StatusCol status={registration_status} />
        </RowBox>
        <RowBox center width={11.5}>
          {formatDateTime(created_date)}
        </RowBox>
        <RowBox center width={5}>
          <Tooltip title={t('expandButton')}>
            <IconButton
              data-cy="expandButton"
              onClick={() => setExpanded(!expanded)}
            >
              {expanded ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
            </IconButton>
          </Tooltip>
        </RowBox>
      </RowContainer>
      {expanded && <RowExpand />}
    </Row>
  );
};

export default OnboardingTableRow;
