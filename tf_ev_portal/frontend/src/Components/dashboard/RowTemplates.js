import { makeStyles } from '@material-ui/styles';
import { isMobile } from 'react-device-detect';
export const Row = ({ children, ...rest }) => {
  const useStyles = makeStyles({
    row: {
      position: 'relative',
      flexDirection: 'column',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      background: 'white',
      animationName: 'FadeIn',
      animationDuration: 'var(--fadein-duration)',
      transitionTimingFunction: 'linear',
      '&:nth-child(odd)': {
        backgroundColor: '#ebf2eb',
      },
    },
  });
  const classes = useStyles();
  return (
    <div {...rest} className={classes.row}>
      {children}
    </div>
  );
};

export const TitleRow = ({ children, ...rest }) => {
  const useStyles = makeStyles({
    row: {
      position: 'relative',
      flexDirection: 'column',
      width: '100%',
      display: 'flex',
      justifyContent: 'center',
      background: 'white',
    },
  });
  const classes = useStyles();
  return (
    <div {...rest} className={classes.row}>
      {children}
    </div>
  );
};

export const RowContainer = ({ children, expanded, ...rest }) => {
  let height = '';

  if (!isMobile) {
    height = '70px';
  }
  const useStyles = makeStyles({
    rowContainer: {
      height: height,
      display: 'flex',
      fontSize: '12px',
      flexGrow: 2,
      zIndex: 1,
      boxShadow: expanded ? '0 2px 2px 0 rgba(0, 0, 0, 0.25)' : '',
    },
  });
  const classes = useStyles();
  return (
    <div {...rest} className={classes.rowContainer}>
      {children}
    </div>
  );
};

export const RowBox = ({
  bold,
  center,
  width,
  background,
  color,
  children,
}) => {
  const useStyles = makeStyles({
    rowBox: {
      textAlign: center ? 'center' : 'left',
      display: 'flex',
      justifyContent: center ? 'center' : 'flex-start',
      alignItems: 'center',
      boxSizing: 'border-box',
      wordBreak: 'break-word',
      fontSize: '14px',
      fontWeight: bold ? 'bold' : 'normal',
      flexDirection: 'row',
      width: `${width}%`,
      background: background || 'transparent',
      color,
    },
  });
  const classes = useStyles();
  return <div className={classes.rowBox}>{children}</div>;
};

export const StatusIndicator = ({
  autoMargin,
  margin,
  background,
  color,
  children,
}) => {
  const useStyles = makeStyles({
    statusIndicator: {
      height: 'auto',
      padding: '3px 6px',
      borderRadius: 6,
      minWidth: 70,
      textAlign: 'center',
      marginLeft: margin ? '5px' : '0px',
      marginRight: autoMargin ? 'auto' : 'none',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      boxSizing: 'border-box',
      fontSize: '11px',
      flexDirection: 'column',
      background: background,
      color,
    },
  });
  const classes = useStyles();
  return <div className={classes.statusIndicator}>{children}</div>;
};

export const ColoredAmount = ({
  autoMargin,
  margin,
  background,
  color,
  children,
  ...rest
}) => {
  const useStyles = makeStyles({
    statusIndicator: {
      height: 'auto',
      padding: '3px 6px',
      borderRadius: 6,
      minWidth: 20,
      textAlign: 'center',
      marginLeft: margin ? '5px' : '0px',
      marginRight: autoMargin ? 'auto' : 'none',
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      boxSizing: 'border-box',
      fontSize: '11px',
      flexDirection: 'column',
      background: background,
      cursor: 'pointer',
      color,
    },
  });
  const classes = useStyles();
  return (
    <div {...rest} className={classes.statusIndicator}>
      {children}
    </div>
  );
};

export const InfoBox = ({ right, width, children }) => {
  const useStyles = makeStyles({
    infoBox: {
      width: `${width}%`,
      fontWeight: 'normal',
      textAlign: 'left',
      color: 'var(--default-text);',
      display: 'flex',
      padding: '0.5rem',
      marginLeft: right ? 'auto' : 0,
      flexDirection: 'column',
      '& > div': {
        margin: '0.25rem',
      },
    },
  });
  const classes = useStyles();
  return <div className={classes.infoBox}>{children}</div>;
};
