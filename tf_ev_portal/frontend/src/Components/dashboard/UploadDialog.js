import { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import XLSX from 'xlsx';
import PropTypes from 'prop-types';
import { Dialog } from '..';
import ErrorField from '../Error';
import UploadField from '../UploadField';
import requester from '../../utils/requester';
import logger from '../../utils/logger';

const Container = styled.div`
  padding: 2rem;
`;
const Headline = styled.h2`
  text-align: center;
  margin-top: 0;
`;
const ErrorContainer = styled.div`
  white-space: pre-wrap;
`;

const marshalContent = (content) => {
  const validateIsNumeric = ['card_number'];

  // disregard header row
  validateIsNumeric.forEach((key) =>
    content.forEach((e) => {
      if (e[key] && !/^\d*$/.test(e[key])) {
        throw key;
      }
    }),
  );

  return {
    card_informations: content,
  };
};

const validateMail = (content) => {
  const invalidMails = [];
  const key = 'email_address_ev_driver';

  content.forEach((e) => {
    if (e[key] && !/^\S+@\S+\.\S+$/.test(e[key])) {
      invalidMails.push(e.card_number);
    }
  });
  return invalidMails;
};

const UploadDialog = ({ title, open, onClose }) => {
  const { t } = useTranslation('actions');
  const tDirectCustomer = useTranslation('directCustomer').t;
  const [isRequestErrorVisible, setIsRequestErrorVisible] = useState(false);
  const [syntaxErrors, setSyntaxErrors] = useState([]);
  const [formatError, setFormatError] = useState('');
  const [isUploaded, setIsUploaded] = useState(false);
  const readAsBinary = 'readAsBinaryString' in FileReader;

  const onUpload = (file) => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = async () => {
        const content = reader.result;
        let wb;
        if (readAsBinary) {
          wb = XLSX.read(content, { type: 'binary' });
        } else {
          //    Code for IE11
          const fixdata = (data) => {
            let o = '';
            let l = 0;
            const w = 10240;
            for (; l < data.byteLength / w; l += 1)
              o += String.fromCharCode.apply(
                null,
                new Uint8Array(data.slice(l * w, l * w + w)),
              );
            o += String.fromCharCode.apply(
              null,
              new Uint8Array(data.slice(l * w)),
            );
            return o;
          };
          wb = XLSX.read(btoa(fixdata(content)), { type: 'base64' });
        }
        const wsname = wb.SheetNames[0];
        const ws = wb.Sheets[wsname];
        let parsed = XLSX.utils.sheet_to_json(ws);
        try {
          //  reset ErrorMessage
          setSyntaxErrors([]);
          const invalidMails = validateMail(parsed);
          if (invalidMails.length > 0) {
            setSyntaxErrors(invalidMails);
            reject();
            return;
          }
          parsed = marshalContent(parsed);
          setFormatError('');
        } catch (err) {
          setFormatError(err);
          reject(err);
          return;
        }
        try {
          await requester().post(
            '/Additional_Information/Post_DirectChannel',
            parsed,
          );
          setIsRequestErrorVisible(false);
          setIsUploaded(true);
          resolve();
          setTimeout(() => {
            window.location.reload();
          }, 2000);
        } catch (err) {
          logger().error(err.message);
          setIsRequestErrorVisible(true);
          reject(err);
        }
      };
      if (readAsBinary) {
        reader.readAsBinaryString(file);
      } else {
        reader.readAsArrayBuffer(file);
      }
    });
  };

  useEffect(() => {
    if (open) {
      setIsRequestErrorVisible(false);
      setSyntaxErrors([]);
      setFormatError('');
      setIsUploaded(false);
    }
  }, [open]);

  const onCloseDialog = () => onClose(isUploaded);

  return (
    <Dialog {...{ open, onClose: onCloseDialog }}>
      <Container>
        <Headline>
          <b>{title}</b>
        </Headline>
        <UploadField
          fileTypes={{
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': true,
            ' application/vnd.ms-excel': true,
          }}
          onUpload={onUpload}
          uploadText={t('upload')}
        />
        <ErrorContainer>
          <ErrorField
            data-cy="formatError"
            visible={!!formatError}
            text={tDirectCustomer('csvFormatError', { formatError })}
          />
          <ErrorField
            data-cy="requestError"
            visible={isRequestErrorVisible}
            text={tDirectCustomer('csvUploadError')}
          />
          <ErrorField
            data-cy="emailSyntaxError"
            visible={syntaxErrors.length > 0}
            text={`${tDirectCustomer(
              'emailInvalidForCardnumber',
            )}\n ${syntaxErrors.join('\n')}`}
          />
        </ErrorContainer>
      </Container>
    </Dialog>
  );
};

UploadDialog.defaultProps = {
  title: '',
  open: false,
  onClose: () => {},
};

UploadDialog.propTypes = {
  title: PropTypes.string,
  open: PropTypes.bool,
  onClose: PropTypes.func,
};

export default UploadDialog;
