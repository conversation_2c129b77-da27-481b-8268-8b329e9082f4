import styled from 'styled-components';

export const ImageWrapper = styled.div`
  margin-left: 0.5rem;
  img {
    width: 44px;
  }
`;

export const TableContainer = styled.div`
  height: auto;
  width: 100%;
`;
export const ButtonWrapper = styled.div`
  width: 300px;
  margin: 1rem 0;
`;
export const StatusDiv = styled.div`
  height: 15px;
  width: 15px;
  border-radius: 40px;
  border: 2px solid lightgray;
  cursor: pointer;
`;

export const LoadingCircleContainer = styled.div`
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
`;

export const Wrapper = styled.div`
  display: flex;
  justify-content: 'center';
`;

export const Container = styled.div`
  min-width: 1230px;
`;

export const HeadlineBox = styled.div`
  height: 40px;
  box-sizing: border-box;
  flex-grow: 2;
  white-space: pre-wrap;
  margin: 15px 0 0 0;
  display: flex;
  font-weight: 700;
  font-size: '12px';
`;

export const Headline = styled.div`
  font-family: var(--font-family);
  margin-bottom: 1rem;
  font-size: 33px;
  font-weight: bold;
  font-stretch: normal;
  font-style: normal;
  letter-spacing: 0.07px;
  text-align: left;
  color: #000;
`;

export const HeadlineContainer = styled.div`
  display: flex;
  margin: 3rem 0 3rem;
  height: 44px;
`;
