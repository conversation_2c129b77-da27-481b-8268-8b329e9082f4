import polygon from '../../static/img/polygon.svg';
import polygonEmpty from '../../static/img/polygonEmpty.svg';
import styled from 'styled-components';

export const Filter = styled.span`
  width: 9px;
  height: 6px;
  margin: 0.125rem 0.25rem;
`;

export const StyledImg = styled.img`
  ${(props) => (props.flip === true ? 'transform: rotateX(180deg)' : '')};
`;

export const FilterButton = styled.button`
  background: transparent;
  border: none;
  outline: none;
  cursor: pointer;
  font-weight: bold;
  padding: 0;
  font-size: 14px !important;
`;
const RowFilter = ({
  sortAttr,
  text,
  invert,
  sort,
  setSort,
  data,
  setData,
}) => {
  const sortSelected = sort.attr === sortAttr;
  const orderBy = sortSelected
    ? sort.order === 'desc'
      ? 'asc'
      : 'desc'
    : 'asc';
  return (
    <FilterButton
      onClick={() => {
        function compareAsc(a, b) {
          if (a[sortAttr] > b[sortAttr]) return 1;
          if (b[sortAttr] > a[sortAttr]) return -1;
          return 0;
        }
        function compareDes(a, b) {
          if (a[sortAttr] < b[sortAttr]) return 1;
          if (b[sortAttr] < a[sortAttr]) return -1;
          return 0;
        }
        let sorted;
        if (invert) {
          sorted = JSON.parse(JSON.stringify(data)).sort(
            orderBy === 'asc' ? compareDes : compareAsc,
          );
        } else {
          sorted = JSON.parse(JSON.stringify(data)).sort(
            orderBy === 'asc' ? compareAsc : compareDes,
          );
        }

        setData(sorted);
        setSort({
          attr: sortAttr,
          order: orderBy,
        });
      }}
      type="button"
    >
      {text}
      <Filter>
        <StyledImg
          flip={sortSelected && sort.order === 'desc'}
          src={sortSelected ? polygon : polygonEmpty}
          alt="sort"
        />
      </Filter>
    </FilterButton>
  );
};

export default RowFilter;
