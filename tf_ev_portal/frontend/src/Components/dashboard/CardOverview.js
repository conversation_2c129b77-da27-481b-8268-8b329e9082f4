import { useCallback, useEffect, useMemo, useState, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import logger from '../../utils/logger';
import requester from '../../utils/requester';
import CircularProgress from '../CircularProgress';
import debouce from 'lodash.debounce';

import Center from '../helper/Center';
import CardTableRow from './CardTableRow';
import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from './DashboardTemplates';
import DialogRouter from './DialogRouter';
import RowFilterServer from './RowFilterServer';
import { RowBox, TitleRow } from './RowTemplates';
import CancelButton from '../CancelButton';
import TextField from '../TextField';
import { Box } from '@mui/system';
import Button from '../Button';
import GetAppIcon from '@material-ui/icons/GetApp';
import { downloadExcel } from '../../utils';
import {
  DashboardFilterContainer,
  QuickFilterButton,
  QuickFilterContainer,
  QuickFilterCount,
} from './DashboardFilter';

const CardOverview = ({ setDashboardUser }) => {
  const { t } = useTranslation('directCustomer');
  const [counts, setCounts] = useState({
    total: 0,
    assigned: 0,
    unassigned: 0,
    active: 0,
    inactive: 0,
    expiring_soon: 0,
  });
  const [cards, setCards] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState([]);
  const [sort, setSort] = useState({
    attr: '',
    order: '',
  });
  const searchInput = useRef(null);
  const [pageSize, setPageSize] = useState(
    window.innerHeight >= 1175 ? 20 : 10,
  );
  const navigate = useNavigate();

  const handleSearch = (e) => {
    setSearchTerm(e.target.value.trim());
  };

  const debouncedSearch = useMemo(() => {
    return debouce(handleSearch, 750);
  }, []);

  useEffect(() => {
    return () => {
      debouncedSearch.cancel();
    };
  });

  const { id } = useParams();
  const superUser = id;

  //reset scroll to prevent infinite scrolling on page change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const handleScroll = () => {
      const bottom =
        Math.ceil(window.innerHeight + window.scrollY) >=
        document.documentElement.scrollHeight;

      if (bottom) {
        if (cards.length > pageSize) {
          setPageSize(pageSize + 10);
        }
      }
    };
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [setPageSize, cards, pageSize]);

  const getData = useCallback(async (params) => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get('/Information_Overview/card_overview', {
          params: new URLSearchParams(params),
        })
      ).data;
      setDashboardUser(rsp.user);
      setIsLoading(false);
      return rsp;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(
          `Timeout of request for /Information_Overview/card_overview.`,
        );
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return {};
      }
    }
  }, []);

  const getParams = (id, filter, search, sort) => {
    const params = [];
    if (id) {
      params.push(['fleetmanager_id', id]);
    }
    if (filter.length > 0) {
      const card_status = [];
      const card_assignment = [];
      filter.forEach((e) => {
        if (e.indexOf('assign') !== -1) {
          card_assignment.push(e);
        } else {
          card_status.push(e);
        }
      });
      if (card_status.length > 0) {
        params.push(['card_status', card_status.join(',')]);
      }
      if (card_assignment.length > 0) {
        params.push(['card_assignment', card_assignment.join(',')]);
      }
    }
    if (search.length > 0) {
      params.push(['search', search]);
    }
    if (sort.attr !== '') {
      params.push(['order_by', `${sort.attr}:${sort.order}`]);
    }
    return params;
  };

  const reFetchData = async () => {
    setPageSize(window.innerHeight >= 1175 ? 20 : 10);
    const rspData = await getData(getParams(id, filter, searchTerm, sort));
    if (rspData.information && rspData.user) {
      setCards(rspData.information);
      setCounts(rspData.counts);
    } else {
      if (rspData === 'error') {
      } else {
        navigate('/error', { replace: true });
      }
    }
  };

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getData(getParams(id, filter, searchTerm, sort));
      if (rspData.information && rspData.user) {
        setCards(rspData.information);
        setCounts(rspData.counts);
      } else {
        if (rspData === 'error') {
        } else {
          navigate('/error', { replace: true });
        }
      }
    };
    fetchData();
  }, [id, navigate, getData, filter, searchTerm, sort]);

  const onDownloadClick = async () => {
    const rspData = await getData(getParams(id, filter, searchTerm, sort));
    if (rspData.information) {
      const rows = JSON.parse(JSON.stringify(rspData.information)).map(
        (row) => {
          const transformedRow = {
            [t('token_visual_number')]: row.token.token_visual_number,
            [t('card_status')]: !row.token.card_status
              ? t('statusInactive')
              : row.token.expiring_soon
              ? t('expiring_soonFilter')
              : t('statusActive'),
            [t('expiry_date')]: `${row.token.expiry_date.substr(
              5,
              2,
            )}/${row.token.expiry_date.substr(2, 2)}`,
            [t('email_address_ev_driver')]: row.email_address_ev_driver || '-',
          };
          return transformedRow;
        },
      );
      downloadExcel(`Cards.xlsx`, rows);
    }
  };

  const quickFilters = [
    {
      attrName: 'assigned',
      bgColor: '#9370db40',
      textColor: '#9370db',
    },
    {
      attrName: 'unassigned',
      bgColor: '#ebebeb',
      textColor: '#666666',
    },
    {
      attrName: 'active',
      bgColor: 'rgba(153, 204, 0, 0.27)',
      textColor: '#090',
    },
    {
      attrName: 'inactive',
      bgColor: '#ebebeb',
      textColor: '#666666',
    },
    {
      attrName: 'expiring_soon',
      bgColor: 'rgba(245, 219, 136, 0.5)',
      textColor: '#ff7e00',
    },
  ];

  useEffect(() => {
    if (!isLoading) {
      searchInput.current.focus();
    }
  }, [isLoading]);

  return (
    <>
      <DialogRouter onDialogClose={reFetchData} />
      <DashboardFilterContainer>
        <QuickFilterContainer>
          {quickFilters.map((quickFilter) => {
            const { attrName, bgColor, textColor } = quickFilter;
            const isFilterActive =
              filter.filter((e) => e === attrName).length > 0;
            return (
              <div style={{ position: 'relative' }} key={attrName}>
                <QuickFilterButton
                  textColor={textColor}
                  bgColor={bgColor}
                  active={isFilterActive}
                  onClick={() => {
                    if (isFilterActive) {
                      setFilter((prevFilter) =>
                        prevFilter.filter((e) => e !== attrName),
                      );
                    } else {
                      setFilter((prevFilter) => [...prevFilter, attrName]);
                    }
                  }}
                >
                  <QuickFilterCount bgColor={textColor}>
                    {counts[attrName]}
                  </QuickFilterCount>
                  {t(attrName)}
                </QuickFilterButton>
                {isFilterActive && (
                  <CancelButton
                    color={textColor}
                    onClick={() => {
                      setFilter((prevFilter) =>
                        prevFilter.filter((e) => e !== attrName),
                      );
                    }}
                  />
                )}
              </div>
            );
          })}
          <Box ml="auto">
            <TextField
              search
              type="text"
              onChange={debouncedSearch}
              disabled={isLoading}
              ref={searchInput}
            />
          </Box>
          <Box ml="0.5rem">
            <Button onClick={onDownloadClick} variant="grey">
              <GetAppIcon fontSize="small" />
              {t('downloadData')}
            </Button>
          </Box>
        </QuickFilterContainer>
      </DashboardFilterContainer>
      <Wrapper>
        <TableContainer>
          <TitleRow>
            <HeadlineBox>
              <RowBox width={2.5}></RowBox>
              {[
                { width: 18, attrName: 'token_visual_number' },
                { width: 10, attrName: 'card_status' },
                { width: 10, attrName: 'expiry_date' },
                { width: 54.5, attrName: 'email_address_ev_driver' },
              ].map((e) => (
                <RowBox key={e.attrName} width={e.width}>
                  <RowFilterServer
                    sortAttr={e.attrName}
                    text={t(e.attrName)}
                    sort={sort}
                    setSort={setSort}
                  />
                </RowBox>
              ))}
            </HeadlineBox>
          </TitleRow>
          {!isLoading && cards.length === 0 && (
            <Center>
              <h2>{t('noDataAvailable')}</h2>
            </Center>
          )}
          {(isLoading && (
            <LoadingCircleContainer style={{ padding: '2rem' }}>
              <CircularProgress />
            </LoadingCircleContainer>
          )) || (
            <div>
              {cards.map((row, i) => {
                if (i < pageSize) {
                  return (
                    <CardTableRow
                      {...row.token}
                      isDisabled={
                        row.driver_approval_status.length > 0
                          ? row.driver_approval_status[0]
                              .driver_approval_status_name === 'created'
                          : true
                      }
                      key={`row-${row.cardnumber + String(i)}`}
                      superUser={superUser}
                      fleetManagerId={id}
                      email={row.email_address_ev_driver}
                      idpId={row.ev_driver_idp_id}
                      issuer={row.token.issuer || null}
                    />
                  );
                }
                return null;
              })}
            </div>
          )}
        </TableContainer>
      </Wrapper>
    </>
  );
};

export default CardOverview;
