import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { Tooltip } from '@material-ui/core';
import app from '../../static/img/app.svg';
import portal from '../../static/img/portal.svg';
import portalApp from '../../static/img/portalApp.svg';
import approvalStatus from '../../constants/approvalStatus';
import styled from 'styled-components';

const StyledImg = styled.img`
  :hover {
    cursor: pointer;
  }
`;

const reimbusmentStatus = [
  approvalStatus.invited,
  approvalStatus.loggedInOnce,
  approvalStatus.initialDataRejected,
  approvalStatus.initialDataEntered,
  approvalStatus.tariffModified,
  approvalStatus.bankingModified,
  approvalStatus.wallboxModified,
  approvalStatus.wallboxUpdateRequested,
  approvalStatus.tariffUpdateRequested,
  approvalStatus.bankingUpdateRequested,
  approvalStatus.approved,
];

export const getUserType = (status, tooltip) => {
  const hasReimbursementStatus =
    status.filter((st) => reimbusmentStatus.indexOf(st) !== -1).length > 0;
  const hasAppUserStatus =
    status.filter((st) => {
      return st === approvalStatus.appUserOnly;
    }).length > 0;
  if (hasReimbursementStatus && hasAppUserStatus) {
    return tooltip ? 'portalAppTooltip' : portalApp;
  }
  if (!hasReimbursementStatus && hasAppUserStatus) {
    return tooltip ? 'appTooltip' : app;
  }
  if (hasReimbursementStatus && !hasAppUserStatus) {
    return tooltip ? 'portalTooltip' : portal;
  }
  return null;
};

const IconSwitch = ({ status }) => {
  const { t } = useTranslation('directCustomer');

  return (
    getUserType(status, true) && (
      <Tooltip title={t(getUserType(status, true))}>
        <StyledImg src={getUserType(status, false)} alt="filter" />
      </Tooltip>
    )
  );
};

IconSwitch.propTypes = {
  status: PropTypes.arrayOf(PropTypes.string),
};

export default IconSwitch;
