import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import PropTypes from 'prop-types';
import { Button, Dialog, Error } from '..';
import WarningIcon from '@material-ui/icons/Warning';

const useStyles = makeStyles({
  dialog: {
    padding: '2rem',
    width: 600,
  },
  paper: {
    borderRadius: '10px !important',
  },
  attentionIcon: {
    height: 55,
    width: 55,
    marginBottom: 18,
    color: 'var(--trafineo-rot-100)',
  },
  center: { textAlign: 'center' },
  text: {
    marginTop: 0,
    marginBottom: 18,
  },
  buttons: {
    marginTop: 30,
  },
});

function CancelReimbursementDialog({ open, onClose, onConfirm, data }) {
  const classes = useStyles();
  const { t } = useTranslation('actions');
  const tDirectCustomer = useTranslation('directCustomer').t;
  const [isErrorVisible, setIsErrorVisible] = useState(false);

  const confirm = async () => {
    try {
      await onConfirm({
        mail: data?.email_address_ev_driver,
        card_number: data?.card_number,
      });
      setIsErrorVisible(false);
    } catch (err) {
      setIsErrorVisible(true);
    }
  };

  return (
    <Dialog
      disableBackdropClick
      disableEscapeKeyDown
      data-cy="dialog"
      {...{ open, onClose }}
    >
      <Grid container justify="center" className={classes.dialog}>
        <Grid item xs={12} className={classes.center}>
          <WarningIcon className={classes.attentionIcon} />
        </Grid>
        <Grid item xs={12} className={classes.center}>
          <h3 className={classes.text}>{tDirectCustomer('areYouSure')}</h3>
        </Grid>
        <Grid item xs={12} className={classes.center}>
          <Error visible={isErrorVisible} text={t('generalRequestError')} />
        </Grid>
        <Grid container className={classes.buttons} justify="space-between">
          <Grid item xs={4}>
            <Button data-cy="confirm" onClick={confirm} variant="secondary">
              {tDirectCustomer('confirmCancelReimbursement')}
            </Button>
          </Grid>
          <Grid item xs={4}>
            <Button onClick={onClose} variant="primary">
              {tDirectCustomer('abortCancelReimbursement')}
            </Button>
          </Grid>
        </Grid>
      </Grid>
    </Dialog>
  );
}

CancelReimbursementDialog.defaultProps = {
  data: {},
  open: false,
  onClose: () => {},
  onConfirm: () => {},
};

CancelReimbursementDialog.propTypes = {
  data: PropTypes.shape({
    card_number: PropTypes.string,
    email_address_ev_driver: PropTypes.string,
    evse_id: PropTypes.number,
  }),
  open: PropTypes.bool,
  onClose: PropTypes.func,
  onConfirm: PropTypes.func,
};

export default CancelReimbursementDialog;
