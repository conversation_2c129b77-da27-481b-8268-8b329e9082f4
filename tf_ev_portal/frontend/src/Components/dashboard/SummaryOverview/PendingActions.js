import { useTranslation } from 'react-i18next';

import {
  HeadlineBox,
  TableContainer,
  LoadingCircleContainer,
} from '../DashboardTemplates';

import { RowBox, TitleRow } from '../RowTemplates';
import ShortTableRow from './ShortTableRow';
import CircularProgress from '../../CircularProgress';

import Center from '../../helper/Center';
import { Link } from 'react-router-dom';
import styled from 'styled-components';
import Timeout from '../../Timeout';

const RowHeader = styled.div`
  display: flex;
  justify-content: ${(props) => (props.isAction ? 'center' : 'left')};
  width: 100%;
`;

const WidgetWrapper = styled.div`
  position: relative;
  min-height: 450px;
`;

const StyledLink = styled(Link)`
  color: var(--primary);
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  position: absolute;
  bottom: -10px;
  ::after {
    content: ' >';
  }
`;

const PendingActions = ({ isLoading, cards, showLink, error }) => {
  const { t } = useTranslation('directCustomer');
  return (
    <WidgetWrapper>
      <TableContainer>
        <TitleRow>
          <HeadlineBox>
            <RowBox width={2.5}></RowBox>
            {[
              { width: 30, attrName: 'email_address_ev_driver' },
              { width: 15, attrName: 'firstname' },
              { width: 15, attrName: 'lastname' },
              { width: 10, attrName: 'card_count' },
              { width: 20, attrName: 'next_action', noFilter: true },
            ].map((e) => (
              <RowBox key={e.attrName} width={e.width}>
                <RowHeader isAction={e.attrName === 'next_action'}>
                  <b>{t(e.attrName)}</b>
                </RowHeader>
              </RowBox>
            ))}
          </HeadlineBox>
        </TitleRow>
        {!isLoading && cards.length === 0 && !error && (
          <Center style={{ padding: '10rem' }}>
            <h2>{t('noPendingActions')}</h2>
          </Center>
        )}
        {!isLoading && error && <Timeout />}
        {(isLoading && (
          <LoadingCircleContainer style={{ padding: '2rem' }}>
            <CircularProgress />
          </LoadingCircleContainer>
        )) || (
          <div>
            {cards.map((row, i) => {
              return (
                <ShortTableRow
                  isDisabled={
                    row.driver_approval_status[0]
                      .driver_approval_status_name === 'created'
                  }
                  rowData={row}
                  key={`row-${row.ev_driver_idp_id}`}
                  // superUser={superUser}
                  idpId={row.ev_driver_idp_id}
                />
              );
            })}
          </div>
        )}
      </TableContainer>
      {showLink && (
        <StyledLink to="/?tab=drivers&pending=true">{t('moreLink')}</StyledLink>
      )}
    </WidgetWrapper>
  );
};

export default PendingActions;
