import { useCallback, useEffect, useState } from 'react';

import requester from '../../../utils/requester';
import logger from '../../../utils/logger';

import paymentIcon from '../../../static/img/DriverDashboard/activeServicesDashboard.svg';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { isMobile } from 'react-device-detect';

import CircularProgress from '../../CircularProgress';
import { LoadingCircleContainer } from '../DashboardTemplates';
import TimeoutWrapper from '../../TimeoutWrapper';
import Timeout from '../../Timeout';

const SummaryItem = styled.div`
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  margin: 1.5rem 0 1rem 0;
  width: ${(props) => (props.ismobile ? '33.3%' : '100%')};
`;

const IconWrapper = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  margin: 0.5rem;
  height: 150px;
  img {
    height: 180px;
    width: 180px;
  }
`;

const ActiveServices = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [amount, setAmount] = useState(0);
  const [error, setError] = useState(false);

  const { t } = useTranslation('directCustomer');

  const config = [
    {
      icon: <img src={paymentIcon} alt="totalActiveServices" />,
      amount: amount,
      label: t('totalActiveServices'),
    },
  ];

  const getActiveServicesData = useCallback(async () => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get(
          '/Information_Overview/card_overview_by_driver?offset=0&limit=1&reimbursement_status=active',
        )
      ).data;
      setIsLoading(false);
      setAmount(rsp.counts.total);
      return rsp;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(
          `Timeout of request for /Information_Overview/card_overview_by_driver.`,
        );
        setError(true);
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return {};
      }
    }
  }, []);

  useEffect(() => {
    getActiveServicesData();
  }, [getActiveServicesData]);

  const Item = ({ e }) => (
    <>
      <SummaryItem key={e.label}>
        <IconWrapper>{e.icon}</IconWrapper>

        <div
          style={{
            fontSize: '24px',
            fontWeight: 'bold',
            lineHeight: '33px',
          }}
        >
          {e.amount ? e.amount : 0}
        </div>

        <div
          style={{
            fontSize: '15px',
            lineHeight: '24px',
            fontWeight: 'bold',
            marginTop: '0.5rem',
            textAlign: 'center',
          }}
        >
          {e.label}
        </div>
      </SummaryItem>
    </>
  );
  return (
    <TimeoutWrapper>
      {isLoading && (
        <LoadingCircleContainer style={{ padding: '2rem' }}>
          <CircularProgress />
        </LoadingCircleContainer>
      )}
      {!isLoading && !error && (
        <div
          style={{
            display: 'flex',
            flexDirection: 'flex-direction: row',
          }}
        >
          {config.map((e, i) => (
            <Item key={i} e={e} isLoading={isLoading} isMobile={isMobile} />
          ))}
        </div>
      )}
      {error && <Timeout />}
    </TimeoutWrapper>
  );
};

export default ActiveServices;
