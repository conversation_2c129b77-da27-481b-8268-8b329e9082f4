/* eslint-disable react/jsx-curly-newline */
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { IconButton, Tooltip } from '@material-ui/core';
import ButtonSwitch from '../ButtonSwitch';
import InfoIcon from '@mui/icons-material/Info';

import { ColoredAmount, Row, RowBox, RowContainer } from '../RowTemplates';
import PersonIcon from '@mui/icons-material/Person';
import { useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';

const ShortTableRow = ({
  isDisabled,
  idpId,
  short = false,
  rowData,
  superUser,
  fleetManagerId,
  noUser = false,
}) => {
  const { t } = useTranslation('directCustomer');
  const {
    tokens,
    firstname,
    lastname,
    driver_approval_status,
    email_address_ev_driver,
  } = rowData;
  const driverStatus = driver_approval_status
    ? driver_approval_status.map(
        ({ driver_approval_status_name }) => driver_approval_status_name,
      )
    : [null];

  const navigate = useNavigate();

  return (
    <Row data-cy="tableRow">
      <RowContainer>
        <RowBox width={2.5}></RowBox>
        <RowBox width={30}>{email_address_ev_driver || '-'}</RowBox>
        <RowBox width={15}>{firstname || '-'}</RowBox>
        <RowBox width={15}>{lastname || '-'}</RowBox>
        <RowBox width={10}>
          <Tooltip title={t('activeCards')}>
            <Box>
              <ColoredAmount
                margin
                color={
                  tokens.filter((e) => e.card_status).length > 0
                    ? '#090'
                    : '#323338'
                }
                background={
                  tokens.filter((e) => e.card_status).length > 0
                    ? 'rgba(153, 204, 0, 0.27)'
                    : '#dbdbdb'
                }
              >
                {tokens.filter((e) => e.card_status).length}
              </ColoredAmount>
            </Box>
          </Tooltip>
          <IconButton
            disabled={isDisabled}
            style={{
              padding: '0.125rem',
            }}
            data-cy="viewUserDetails"
          >
            <InfoIcon
              disabled={isDisabled}
              onClick={() => {
                navigate(
                  `/userdetails/s/${
                    superUser ? `${encodeURIComponent(fleetManagerId)}/` : ''
                  }${encodeURIComponent(idpId)}?nav=cards`,
                );
              }}
              fontSize="small"
            />
          </IconButton>
        </RowBox>
        <RowBox center width={20}>
          <ButtonSwitch status={driverStatus} data={rowData} />
        </RowBox>

        <Tooltip title="yo" placement="top-end">
          <RowBox center width={5}>
            <Tooltip
              title={
                noUser
                  ? t('userDetailAfterOnboarding')
                  : isDisabled
                  ? t('userAfterActivation')
                  : t('viewUserDetails')
              }
            >
              {/* Add span to enable tooltip on disabled button */}
              <span>
                <IconButton
                  disabled={isDisabled}
                  style={{
                    border: '1px solid',
                    padding: '0.25rem',
                    borderRadius: '20px',
                  }}
                  data-cy="viewUserDetails"
                >
                  <PersonIcon
                    onClick={() => {
                      if (!noUser) {
                        navigate(
                          `/userdetails/s/${
                            superUser
                              ? `${encodeURIComponent(fleetManagerId)}/`
                              : ''
                          }${encodeURIComponent(idpId)}`,
                        );
                      }
                    }}
                    fontSize="small"
                  />
                </IconButton>
              </span>
            </Tooltip>
          </RowBox>
        </Tooltip>
      </RowContainer>
    </Row>
  );
};

ShortTableRow.defaultProps = {
  rowData: {},
};

ShortTableRow.propTypes = {
  rowData: PropTypes.shape({
    card_number: PropTypes.string,
    card_status: PropTypes.bool,
    reimbursement_status_id: PropTypes.number,
    driver_approval_status: PropTypes.arrayOf(
      PropTypes.shape({
        driver_approval_status_name: PropTypes.string,
      }),
    ),
    evse_id: PropTypes.number,
  }),
};

export default ShortTableRow;
