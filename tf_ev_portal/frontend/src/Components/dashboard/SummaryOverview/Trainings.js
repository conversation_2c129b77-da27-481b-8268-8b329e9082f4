import { useTranslation } from 'react-i18next';
import { useEffect, useCallback, useContext, useState } from 'react';

import { LoadingCircleContainer } from '../DashboardTemplates';
import CircularProgress from '../../CircularProgress';

import requester from '../../../utils/requester';
import logger from '../../../utils/logger';
import { dialogContext } from '../../../ContextProvider';

import styled from 'styled-components';

import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import CircleIcon from '@mui/icons-material/Circle';
import DialogRouter from '../DialogRouter';
import { getBranding } from '../../../utils/helper';

//imports for demo
import { isInDemoMode } from '../../../utils/helper';
import { userContext } from '../../../ContextProvider';
import jwtDecode from 'jwt-decode';
import trainingsFleetmanagerIndirect from '../../../mock/dashboardData/Trainings_fleetmanager_indirect';
import trainingsFleetmanagerDirect from '../../../mock/dashboardData/Trainings_fleetmanager_direct';

//GIFs
import gifs from '../../../static/gif/gifs';

const RowWrapper = styled.div`
  height: ${(props) => props.height || 'auto'};
  display: flex;
  padding-top: 15px;
  flex-direction: ${(props) => props.align || ''};
`;

const Wrapper = styled.div`
  position: relative;
`;

const Container = styled.div`
  height: 20px;
  width: 100%;
  background-color: var(--trafineo-grau-20);
  border-radius: 15px;
`;

const Filler = styled.div`
  height: 100%;
  width: ${(props) => props.progress || '0'};
  background-color: var(--trafineo-rot-100);
  border-radius: inherit;
  text-align: right;
`;

const AmountText = styled.div`
  height: 20px;
  width: 100%;
  text-align: right;
`;

const CompletedText = styled.div`
  height: 20px;
`;

const TrainingName = styled.p`
  vertical-align: middle;
  display: inline-block;
  margin: 0;
  min-height: 24px;
  line-height: 24px;
  color: ${(props) => (props.isCompleted ? 'var(--trafineo-grau-50)' : '')};
  font-weight: ${(props) => (!props.isCompleted ? '700' : '')};
`;

const StyledCheck = styled(CheckCircleIcon)`
  color: var(--trafineo-rot-100);
  vertical-align: middle;
  display: inline-block;
  margin-right: 1px;
`;

const StyledCircle = styled(CircleIcon)`
  color: var(--trafineo-grau-20);
  vertical-align: middle;
  display: inline-block;
  margin-right: 1px;
`;

const TrainingArrow = styled.div`
  flex: auto;
  text-align: right;
  font-weight: ${(props) => (!props.isCompleted ? '700' : '')};
`;

const TrainingWrapper = styled.div`
  width: 100%;
  display: flex;
  min-height: 24px;
  margin-bottom: 10px;
  cursor: ${(props) => (props.isOnboarding ? '' : 'pointer')};
`;

const Trainings = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [trainings, setTrainings] = useState([]);
  const [completedTrainings, setCompletedTrainings] = useState(0);
  const [currentTraining, setCurrentTraining] = useState(0);
  const { dialogData, setDialogData } = useContext(dialogContext);

  const [oldDialogType, setOldDialogType] = useState(null);

  const { user } = useContext(userContext);

  const { t } = useTranslation('directCustomer');
  const { i18n } = useTranslation('home');

  const getTrainings = useCallback(async (params) => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get(
          //request only one card to only get trainings attached to user
          '/Information_Overview/card_overview?limit=1&offset=0',
          {},
        )
      ).data;
      setIsLoading(false);
      return rsp.user;
    } catch (err) {
      logger().error(
        `Couldn't get overview training data from api.\n${err.message}`,
      );
      setIsLoading(false);
      return {};
    }
  }, []);

  const getDemoTrainings = useCallback(() => {
    setIsLoading(true);
    const role = jwtDecode(user.access_token).role;

    setIsLoading(false);

    if (role.toLowerCase() === 'fleetmanager_indirect') {
      return trainingsFleetmanagerIndirect.user;
    } else {
      return trainingsFleetmanagerDirect.user;
    }
  }, [user]);

  const fetchData = useCallback(async () => {
    //unable to properly seperate demo and non demo cases so isInDemoMode is needed
    if (isInDemoMode() || window.location.host === 'localhost:3000') {
      const rspData = getDemoTrainings();
      if (
        rspData &&
        rspData.trainings &&
        rspData.trainings.findIndex((training) => training.id === -1) === -1
      ) {
        rspData.trainings.unshift({
          id: -1,
          name: 'trainingOnboarding',
          content: null,
          completed: true,
        });
        setTrainings(rspData.trainings);
      } else {
        console.error('Error requesting trainings data');
      }
    } else {
      const rspData = await getTrainings();
      if (rspData) {
        rspData.trainings.unshift({
          id: -1,
          name: 'trainingOnboarding',
          content: null,
          completed: true,
        });
        setTrainings(rspData.trainings);
      } else {
        console.error('Error requesting trainings data');
      }
    }
  }, [getTrainings, getDemoTrainings]);

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  useEffect(() => {
    // check if dialog was existing and is now closed for training data refresh
    if (
      oldDialogType !== dialogData.type &&
      dialogData.type === null &&
      oldDialogType === 'training'
    ) {
      fetchData();
    } else {
      setOldDialogType(dialogData.type);
    }
  }, [dialogData, oldDialogType, fetchData]);

  useEffect(() => {
    const calculateCompletedTrainings = () => {
      let i = 0;
      trainings.forEach((training, index) => {
        if (training.completed) {
          i++;
        }
      });
      setCurrentTraining(
        trainings.findIndex((training) => !training.completed),
      );
      setCompletedTrainings((i / trainings.length) * 100);
    };
    if (trainings.length > 0) {
      calculateCompletedTrainings();
    }
  }, [trainings, setCurrentTraining, currentTraining]);

  const getGif = (training) => {
    const branding = getBranding();
    const language = i18n.language;
    const currentGif = gifs[branding][training.name]?.[language];
    return currentGif || '';
  };

  return (
    <Wrapper>
      <DialogRouter />
      {isLoading && (
        <LoadingCircleContainer style={{ padding: '2rem' }}>
          <CircularProgress />
        </LoadingCircleContainer>
      )}
      {!isLoading && (
        <>
          <RowWrapper>
            <CompletedText>{t('trainingsCompleted')}</CompletedText>

            <AmountText>{Math.floor(completedTrainings) + '%'}</AmountText>
          </RowWrapper>
          <RowWrapper>
            <Container>
              <Filler progress={completedTrainings + '%'}></Filler>
            </Container>
          </RowWrapper>
          <RowWrapper align={'column'}>
            {trainings?.map((training, i) => {
              return (
                <TrainingWrapper
                  isOnboarding={i === 0}
                  onClick={() =>
                    i !== 0 &&
                    setDialogData({
                      ...dialogData,
                      type: 'training',
                      training: training,
                      gif: getGif(training),
                      open: true,
                    })
                  }
                >
                  {training.completed && <StyledCheck />}
                  {!training.completed && <StyledCircle />}
                  <TrainingName isCompleted={training.completed}>
                    {t(training.name)}
                  </TrainingName>
                  <TrainingArrow isCompleted={training.completed}>
                    {'>'}
                  </TrainingArrow>
                </TrainingWrapper>
              );
            })}
          </RowWrapper>
        </>
      )}
    </Wrapper>
  );
};

export default Trainings;
