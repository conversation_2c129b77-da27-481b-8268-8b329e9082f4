import { useTranslation } from 'react-i18next';
import { useEffect, useCallback, useState } from 'react';

import { LoadingCircleContainer } from '../DashboardTemplates';
import CircularProgress from '../../CircularProgress';

import { useNavigate } from 'react-router-dom';

import requester from '../../../utils/requester';
import logger from '../../../utils/logger';

// import ReactComponent to fix webpack bug see: https://github.com/boopathi/react-svg-loader/issues/197
import { ReactComponent as IconPayedoutBP } from '../../../static/img/icons/IconPayedout.bp.svg';
import { ReactComponent as IconPayedoutAral } from '../../../static/img/icons/IconPayedout.aral.svg';
import { ReactComponent as IconLoading } from '../../../static/img/icons/IconLoading.svg';
import { ReactComponent as IconCharge } from '../../../static/img/icons/IconCharge.svg';

import styled from 'styled-components';
import { getBranding } from '../../../utils/helper';
import Timeout from '../../Timeout';
import TimeoutWrapper from '../../TimeoutWrapper';

const RowWrapper = styled.div`
  height: 50px;
  display: flex;
  padding-top: 15px;
`;

const Spacer = styled.div`
  height: 20px;
`;

const IconCircle = styled.div`
  height: 40px;
  width: 40px;
  min-width: 40px;
  border-radius: 25px;
  background-color: ${(props) => (props.color ? props.color : 'transparent')};
  margin-right: 10px;
  text-align: center;
`;

const StyledIconPayedoutBP = styled(IconPayedoutBP)`
  margin-top: 6px;
  width: 30px;
  height: 30px;
`;

const StyledIconPayedoutAral = styled(IconPayedoutAral)`
  margin-top: 6px;
  width: 30px;
  height: 30px;
`;

const StyledLoading = styled(IconLoading)`
  margin-top: 5px;
  width: 30px;
  height: 30px;
`;

const StyledCharge = styled(IconCharge)`
  margin-top: 7px;
  width: 25px;
  height: 25px;
`;

const AmountText = styled.p`
  font-weight: bold;
  font-size: 20px;
  width: 100%;
  margin: 0;
  display: table-cell;
  line-height: 28px;
`;

const InfoText = styled.p`
  width: 100%;
  margin: 0;
  display: table-cell;
  font-size: 12px;
  line-height: 12px;
`;

const TextWrapper = styled.div`
  display: flex;
  flex-direction: column;
`;

const Payments = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [amount, setAmount] = useState({});
  const [last30Days, setlast30Days] = useState({});
  const [error, setError] = useState(false);

  const { t } = useTranslation('directCustomer');

  const navigate = useNavigate();

  const getDateTo = (date) => {
    const dateCopy = new Date(date);
    dateCopy.setHours(23, 59, 59, 999);
    const lastDay = new Date(
      dateCopy.getFullYear(),
      dateCopy.getMonth(),
      0,
      dateCopy.getHours(),
      dateCopy.getMinutes(),
      dateCopy.getSeconds(),
      dateCopy.getMilliseconds(),
    );
    return lastDay.toISOString();
  };

  const getRequestDate = (date) => {
    const dateCopy = new Date(date);
    const firstDay = new Date(
      dateCopy.getFullYear(),
      dateCopy.getMonth() - 1,
      1,
    );
    return firstDay.toISOString();
  };

  const getAmount = useCallback(async (date, dateTo) => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get('/chargingsessions/', {
          params: {
            date_from: date,
            date_to: dateTo,
            cdr_type: 'home',
          },
        })
      ).data;
      setIsLoading(false);
      return rsp;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(`Timeout of request for /chargingsessions.`);
        setError(true);
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return {};
      }
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getAmount(
        getRequestDate(Date.now()),
        getDateTo(Date.now()),
      );
      const today = new Date();
      const past29Days = new Date(
        new Date(today).setDate(today.getDate() - 29),
      );
      const last30DaysData = await getAmount(
        new Date(past29Days.setHours(0, 0, 0, 0)).toISOString(),
        new Date(today.setHours(23, 59, 59, 999)).toISOString(),
      );
      if (rspData.meta) {
        setAmount(rspData.meta);
      } else {
        if (rspData === 'error') {
        } else {
          console.error('Error requesting pending actions data');
          navigate('/error', { replace: true });
        }
      }
      if (last30DaysData.meta) {
        setlast30Days(last30DaysData.meta);
      } else {
        if (last30DaysData === 'error') {
        } else {
          console.error('Error requesting pending actions data');
          navigate('/error', { replace: true });
        }
      }
    };
    fetchData();
  }, [navigate, getAmount]);
  return (
    <TimeoutWrapper>
      {isLoading && (
        <LoadingCircleContainer style={{ padding: '2rem' }}>
          <CircularProgress />
        </LoadingCircleContainer>
      )}
      {!isLoading && !error && (
        <>
          <Spacer />
          <RowWrapper>
            <IconCircle
              color={
                getBranding() === 'aral' ? '#C7E3F2' : 'rgba(153, 204, 0, 0.27)'
              }
            >
              {getBranding() === 'aral' && <StyledIconPayedoutAral />}
              {getBranding() !== 'aral' && <StyledIconPayedoutBP />}
            </IconCircle>
            <TextWrapper>
              <AmountText>
                {(
                  Math.round(
                    (amount.total_paid_reimbursement_cost + Number.EPSILON) *
                      100,
                  ) / 100
                ).toFixed(2) || 0}{' '}
                €
              </AmountText>
              <InfoText>{t('paidOut')}</InfoText>
            </TextWrapper>
          </RowWrapper>
          <Spacer />
          <RowWrapper>
            <IconCircle color="rgba(255, 196, 32, 0.49)">
              <StyledLoading />
            </IconCircle>
            <TextWrapper>
              <AmountText>
                {(
                  Math.round(
                    (last30Days.total_expected_reimbursement_cost +
                      Number.EPSILON) *
                      100,
                  ) / 100
                ).toFixed(2) || 0}{' '}
                €
              </AmountText>
              <InfoText>{t('inProgress')}</InfoText>
            </TextWrapper>
          </RowWrapper>
          <Spacer />
          <RowWrapper>
            <IconCircle color="#E7E7E7">
              <StyledCharge color="#000" />
            </IconCircle>
            <TextWrapper>
              <AmountText>{last30Days.total_sessions_received || 0}</AmountText>
              <InfoText>{t('sessionsRecieved')}</InfoText>
            </TextWrapper>
          </RowWrapper>
        </>
      )}
      {!isLoading && error && <Timeout />}
    </TimeoutWrapper>
  );
};

export default Payments;
