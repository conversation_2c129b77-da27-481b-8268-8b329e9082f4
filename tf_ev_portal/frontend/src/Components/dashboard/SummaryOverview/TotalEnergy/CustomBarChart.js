import { useTranslation } from 'react-i18next';

import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from 'recharts';

import styled from 'styled-components';
import { getBranding } from '../../../../utils/helper';

const YearText = styled.p`
  font-size: 14px;
  color: #b5b5b5;
  margin: 0px;
  margin-top: 2px;
`;

const EnergyText = styled.p`
  font-weight: 600;
  font-size: 20px;
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 5px;
`;

const MonthText = styled.p`
  font-size: 14px;
  color: #b5b5b5;
  margin: 0px;
`;

const TooltipWrapper = styled.div`
  background-color: #fff;
  width: 130px;
  height: auto;
  border-radius: 5px;
  padding: 5px;
  box-shadow: 4px 4px 7px 4px rgba(0, 0, 0, 0.3);
  border: none !important;
  outline: none !important;
`;

const CustomBarChart = (data) => {
  const { t } = useTranslation('overview');

  const CustomTooltip = ({ active, payload, label }) => {
    if (active && payload && payload.length) {
      return (
        <TooltipWrapper>
          <YearText>{payload[0].payload.timestamp.split('-')[0]}</YearText>
          <EnergyText>
            {(
              Math.round((payload[0].payload.value + Number.EPSILON) * 100) /
              100
            ).toFixed(2) || 0}
            {' kWh'}
          </EnergyText>
          <MonthText>{t(payload[0].payload.month)}</MonthText>
        </TooltipWrapper>
      );
    }
    return null;
  };

  return (
    <ResponsiveContainer>
      <BarChart
        width={700}
        height={300}
        data={data.data}
        margin={{
          top: 5,
          right: 10,
          left: -15,
          bottom: 5,
        }}
      >
        <CartesianGrid vertical={false} strokeDasharray={'0 0'} />
        <XAxis dataKey="name" />
        <YAxis width={70} />
        <Tooltip wrapperStyle={{ outline: 'none' }} content={CustomTooltip} />
        <Bar
          dataKey="value"
          fill={
            getBranding() === 'aral'
              ? '#0064cc'
              : getBranding() === 'bp'
              ? '#9acc00'
              : '#c60018'
          }
        />
      </BarChart>
    </ResponsiveContainer>
  );
};

export default CustomBarChart;
