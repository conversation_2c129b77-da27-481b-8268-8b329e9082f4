import { useTranslation } from 'react-i18next';
import { useEffect, useCallback, useState } from 'react';

import { useNavigate } from 'react-router-dom';

import requester from '../../../../utils/requester';
import logger from '../../../../utils/logger';

import styled from 'styled-components';

import CustomBarChart from './CustomBarChart';
import CircularProgress from '../../../CircularProgress';
import { LoadingCircleContainer } from '../../DashboardTemplates';
import Timeout from '../../../Timeout';
import TimeoutWrapper from '../../../TimeoutWrapper';

const Wrapper = styled.div`
  width: 100%;
  height: 100%;
  max-height: 290px;
`;

const TotalEnergy = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [totalEnergyData, setTotalEnergyData] = useState([]);
  const [error, setError] = useState(false);

  const navigate = useNavigate();
  const { t } = useTranslation('directCustomer');

  const setZeroData = useCallback(() => {
    const date = new Date(Date.now());
    const noDataPlaceholder = [];
    const firstMonth = new Date(date.getFullYear() - 1, date.getMonth(), 1);

    for (let i = 0; i < 12; i++) {
      let modifiedMonth = new Date(
        firstMonth.getFullYear(),
        firstMonth.getMonth() + i,
        1,
      );

      const year = modifiedMonth.toLocaleString('default', { year: 'numeric' });
      const month = modifiedMonth.toLocaleString('default', {
        month: '2-digit',
      });

      let currentMonthData = {
        value: 0,
        timestamp: [year, month].join('-'),
      };

      noDataPlaceholder.push(currentMonthData);
    }
    return noDataPlaceholder;
  }, []);

  const getTotalEnergy = useCallback(async (date, dateTo) => {
    setIsLoading(true);
    try {
      const rsp = await requester().get('/stats/cdr_totalenergy', {
        params: {
          date_from: date,
          date_to: dateTo,
          agg_strategy: 'SUM',
          agg_interval: 'month',
        },
      });
      setIsLoading(false);
      return rsp.data;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(`Timeout of request for chargingsessions.`);
        setError(true);
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return {};
      }
    }
  }, []);

  const getDateTo = (date) => {
    const dateCopy = new Date(date);
    dateCopy.setHours(23, 59, 59, 999);
    const lastDay = new Date(
      dateCopy.getFullYear(),
      dateCopy.getMonth(),
      0,
      dateCopy.getHours() + 2,
      dateCopy.getMinutes(),
      dateCopy.getSeconds(),
      dateCopy.getMilliseconds(),
    );
    return lastDay.toISOString();
  };

  const getRequestDate = (date) => {
    const dateCopy = new Date(date);
    dateCopy.setFullYear(date.getFullYear() - 1);
    const firstDay = new Date(
      dateCopy.getFullYear(),
      dateCopy.getMonth(),
      1,
      2,
      0,
      0,
    );
    return firstDay.toISOString();
  };

  useEffect(() => {
    const adjustDataForChart = (data) => {
      data.forEach((energyMonth) => {
        if (energyMonth.value === null) {
          energyMonth.value = 0;
        }
        const mockDate = new Date(
          energyMonth.timestamp.split('-')[0],
          energyMonth.timestamp.split('-')[1] - 1,
          1,
        );
        energyMonth.name = t(
          mockDate.toLocaleString('en-US', { month: 'short' }).toLowerCase(),
        );
        energyMonth.month = mockDate
          .toLocaleString('en-US', { month: 'long' })
          .toLowerCase();
      });

      setTotalEnergyData(data);
    };
    const fetchData = async () => {
      const requestDate = getRequestDate(new Date(Date.now()));
      const dateTo = getDateTo(new Date(Date.now()));
      const rspData = await getTotalEnergy(requestDate, dateTo);
      if (rspData.data && rspData.data !== null) {
        adjustDataForChart(rspData.data);
      } else if (rspData.data === null) {
        adjustDataForChart(setZeroData());
      } else {
        if (rspData === 'error') {
        } else {
          console.error('Error requesting energy data');
          navigate('/error', { replace: true });
        }
      }
    };
    fetchData();
  }, [navigate, getTotalEnergy, setZeroData, t]);

  return (
    <Wrapper>
      <TimeoutWrapper>
        {isLoading && (
          <LoadingCircleContainer style={{ padding: '2rem' }}>
            <CircularProgress />
          </LoadingCircleContainer>
        )}
        {!isLoading && !error && <CustomBarChart data={totalEnergyData} />}

        {error && <Timeout />}
      </TimeoutWrapper>
    </Wrapper>
  );
};

export default TotalEnergy;
