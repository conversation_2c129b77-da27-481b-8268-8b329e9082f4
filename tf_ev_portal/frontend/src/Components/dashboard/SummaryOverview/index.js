import { useEffect, useState, useCallback, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import TagManager from 'react-gtm-module';

import { isMobile } from 'react-device-detect';

import requester from '../../../utils/requester';
import logger from '../../../utils/logger';

import PendingActions from './PendingActions';
import ActiveServices from './ActiveServices';
import Payments from './Payments';
import TotalEnergy from './TotalEnergy';
import AverageCost from './AverageCost';
import Trainings from './Trainings';

import DialogRouter from '../DialogRouter';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';

import HelpIcon from '@mui/icons-material/Help';
import { ReactComponent as AverageCostIcon } from '../../../static/img/icon-average-cost.svg';
import { ReactComponent as TotalkWhIcon } from '../../../static/img/icon-total-kwh.svg';
import { ReactComponent as PendingActionsIcon } from '../../../static/img/icon-pending-actions.svg';

import { dialogContext } from '../../../ContextProvider';

import styled from 'styled-components';

const StyledTooltip = styled(Tooltip)`
  text-align: center !important;
  width: 25px;
  padding: 0 !important;
`;

const StyledIconButton = styled(IconButton)`
  position: absolute !important;
  right: 0px;
  top: 0px;
  background-color: transparent !important;
`;

const Grid = styled.div`
  display: grid;
  grid-template-areas:
    'totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy  totalActiveServices totalActiveServices  totalActiveServices totalActiveServices  payments payments payments payments payments info info info info info'
    'averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions';
  gap: 1.5rem;
  grid-template-columns: repeat(24, 1fr);
  @media (max-width: 1365px) {
    grid-template-areas:
      'totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalEnergy totalActiveServices totalActiveServices totalActiveServices payments payments payments'
      'pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions pendingActions info info info'
      'averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost averageCost';
    gap: 1.5rem;
    grid-template-columns: repeat(12, 1fr);
  }
  ${isMobile
    ? `@media (max-width: 768px) {
    grid-template-areas:
      'totalEnergy'
      'totalActiveServices'
      'payments'
      'chargingsessions'
      'reimbursements'
      'info';
    gap: 1rem;
    grid-template-columns: repeat(1, 1fr);
  }`
    : 'min-width:768px'}
`;

const GridItem = styled.div`
  grid-area: ${(props) => props.name};
  border-radius: 20.5px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
  background-color: ${(props) => props.bg || '#fff'};
  ${(props) =>
    props.minHeight && !isMobile ? `min-height: ${props.minHeight}` : ''};
  height: auto;
  padding: 1.5rem;
  width: ${(props) =>
    isMobile && props.windowWidth <= 768 ? 'calc(100vw - 2rem)' : '100%'};
  box-sizing: border-box;
`;

const ItemHeadline = styled.div`
  text-align: left;
  font-weight: bold;
  color: black;
  font-size: 22px;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 10px;
  svg {
    margin: 0 0 0 0.5rem;
    margin-right: ${(props) => props.marginSVG || ''};
    height: 20px;
  }
  margin-bottom: ${(props) => props.marginBottom || ''};
`;

const SummaryOverview = () => {
  const { t } = useTranslation('directCustomer');
  const [isLoading, setIsLoading] = useState(false);
  const [pendingActionCards, setPendingActionCards] = useState([]);
  const [tooltipOpen, setOpen] = useState({ services: false, payments: false });
  const [oldDialogType, setOldDialogType] = useState(null);
  const [pendingActionsError, setPendingActionsError] = useState(false);

  const { dialogData } = useContext(dialogContext);

  const navigate = useNavigate();

  const handleTooltipOpen = (type) => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'tooltip_interaction',
        tooltip: {
          name: `tooltip_dashboard_${type ? type : 'payments'}`,
          interaction_type: 'open',
        },
      },
    });
    if (type === 'services') {
      setOpen({ ...tooltipOpen, services: true });
    } else {
      setOpen({ ...tooltipOpen, payments: true });
    }
  };

  const handleTooltipClose = (e) => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'tooltip_interaction',
        tooltip: {
          name: `tooltip_dashboard_${e}`,
          interaction_type: 'close',
        },
      },
    });
    setOpen({ services: false, payments: false });
  };

  const getPendingActionsData = useCallback(async (params) => {
    setIsLoading(true);
    try {
      const rsp = (
        await requester().get('/Information_Overview/card_overview_by_driver', {
          params: new URLSearchParams(
            'ev_driver_data_status=initial_data_entered%2Ctariff_modified%2Ccreated%2Cnew_card_requested',
          ),
        })
      ).data;
      setIsLoading(false);
      return rsp;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(
          `Timeout of request for /Information_Overview/card_overview_by_driver.`,
        );
        setPendingActionsError(true);
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return {};
      }
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getPendingActionsData();
      if (rspData.information) {
        setPendingActionCards(rspData.information);
      } else {
        if (rspData === 'error') {
        } else {
          console.error('Error requesting cost data');
          navigate('/error', { replace: true });
        }
      }
    };
    fetchData();
  }, [navigate, getPendingActionsData]);

  const reFetchData = useCallback(async () => {
    const rspData = await getPendingActionsData();
    if (rspData.information) {
      setPendingActionCards(rspData.information);
    } else {
      navigate('/error', { replace: true });
    }
  }, [getPendingActionsData, navigate]);

  useEffect(() => {
    // check if dialog was existing and is now closed for pending actions data refresh
    if (
      oldDialogType !== dialogData.type &&
      dialogData.type === null &&
      oldDialogType !== 'training'
    ) {
      reFetchData();
    } else {
      setOldDialogType(dialogData.type);
    }
  }, [dialogData, oldDialogType, reFetchData]);

  return (
    <>
      <DialogRouter onDialogClose={reFetchData} />
      <Grid>
        <GridItem name="totalEnergy">
          <ItemHeadline marginBottom="15px">
            <TotalkWhIcon />
            {t('totalEnergy')}
          </ItemHeadline>
          <TotalEnergy />
        </GridItem>
        <GridItem name="totalActiveServices">
          <ItemHeadline>
            {t('services')}
            <StyledTooltip
              PopperProps={{
                disablePortal: true,
              }}
              onClose={() => handleTooltipClose('services')}
              open={tooltipOpen.services}
              title={t('totalActiveServicesHint')}
            >
              <StyledIconButton onClick={(e) => handleTooltipOpen('services')}>
                <HelpIcon />
              </StyledIconButton>
            </StyledTooltip>
          </ItemHeadline>
          <ActiveServices />
        </GridItem>
        <GridItem minHeight="307px" minWidth="250px" name="payments">
          <ItemHeadline minWidth="250px">
            {t('payments')}
            <StyledTooltip
              PopperProps={{
                disablePortal: true,
              }}
              onClose={() => handleTooltipClose('payments')}
              open={tooltipOpen.payments}
              title={
                <span style={{ whiteSpace: 'pre-line' }}>
                  {t('paymentsFooter')}
                </span>
              }
            >
              <StyledIconButton onClick={(e) => handleTooltipOpen()}>
                <HelpIcon />
              </StyledIconButton>
            </StyledTooltip>
          </ItemHeadline>
          <Payments />
        </GridItem>
        <GridItem name="info">
          <ItemHeadline>{t('trainingsHeadline')}</ItemHeadline>
          <Trainings />
        </GridItem>
        <GridItem minHeight="350px" name="averageCost">
          <ItemHeadline marginBottom="15px" marginSVG="0.5rem">
            <AverageCostIcon />
            {t('averageCostHeadline')}
          </ItemHeadline>
          <AverageCost />
        </GridItem>
        <GridItem minHeight="450px" name="pendingActions">
          <ItemHeadline>
            <PendingActionsIcon />
            {t('pendingActions')}
          </ItemHeadline>
          <PendingActions
            isLoading={isLoading}
            cards={pendingActionCards.slice(0, 5)}
            showLink={pendingActionCards.length > 5}
            error={pendingActionsError}
          />
        </GridItem>
      </Grid>
    </>
  );
};

export default SummaryOverview;
