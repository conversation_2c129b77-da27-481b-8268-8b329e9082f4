import { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import PropTypes from 'prop-types';
import qs from 'querystring';
import { makeStyles } from '@material-ui/styles';
import { Button } from '..';
import CancelButton from '../CancelButton';
import approvalStatus from '../../constants/approvalStatus';
import requester from '../../utils/requester';
import CircularProgress from '../CircularProgress';
import SuccessDialog from '../SuccessDialog';
import CancelReimbursementDialog from './cancelReimbursementDialog';
import { dialogContext } from '../../ContextProvider';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import { useNavigate } from 'react-router-dom';
import TagManager from 'react-gtm-module';

// Component is used everywhere where multiple buttons can be shown depending on the current state, e.a. Drivertable
const ButtonBox = styled.div`
  display: flex;
  height: 100%;

  width: 100%;
  justify-content: space-around;
`;

const useStyles = makeStyles({
  buttonOverlay: {
    position: 'relative',
    width: '100%',
  },
  buttonChild: {
    display: 'flex',
    height: '100%',
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center',
  },
});

const ButtonSwitch = ({
  superUser,
  cancelReimbursementButton,
  status,
  data,
  inUserDetails,
  assignCard,
  ...rowData
}) => {
  const { dialogData, setDialogData } = useContext(dialogContext);
  const { t } = useTranslation('directCustomer');
  const [isLoading, setIsLoading] = useState(false);
  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);
  const classes = useStyles();
  const [isCancelStatusDialogVisible, setIsCancelStatusDialogVisible] =
    useState(false);
  const navigate = useNavigate();

  const {
    token_visual_number: cardnumber,
    ev_driver_idp_id: idpId,
    email_address_ev_driver: driverMail,
    evse_id: evseId,
    tokens,
    expiry_date: expiryDate,
  } = data;
  let buttons;

  const inviteDriver = () => {
    setDialogData({
      ...dialogData,
      type: 'inviteOrAssign',
      cardNumber: cardnumber,
      expiryDate,
      driverMail: driverMail,
      open: true,
    });
  };

  const appUserActivation = () => {
    setDialogData({
      ...dialogData,
      type: 'appUserActivation',
      cardNumber: tokens[0] ? tokens[0].token_visual_number : null,
      expiryDate: tokens[0] ? tokens[0].expiry_date : null,
      driverMail: driverMail,
      open: true,
    });
  };

  const cancelInvitation = async () => {
    try {
      setIsLoading(true);
      await requester().delete(`/driver_management/${driverMail}`, {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      });
      setIsLoading(false);
      setIsStatusDialogVisible(true);
    } catch (e) {
      setIsLoading(false);
      console.error(e);
    }
  };
  const updateDataRequest = (isSpecialRequest = false, onlyBank = false) => {
    setDialogData({
      ...dialogData,
      type: 'requestDataUpdate',
      idpId,
      evse_id: evseId,
      driverMail: driverMail,
      open: true,
      isSpecialRequest,
      onlyBank,
    });
  };
  const reviewAndApproveData = () => {
    setDialogData({
      ...dialogData,
      type: 'approval',
      driverMail: driverMail,
      idpId,
      open: true,
    });
  };
  const reviewIndirectRequest = () => {
    setDialogData({
      ...dialogData,
      type: 'indirectRequest',
      driverMail: driverMail,
      idpId,
      open: true,
      rowData: data,
    });
  };
  const reviewIndirectCardRequest = () => {
    setDialogData({
      ...dialogData,
      type: 'indirectRequest',
      driverMail: driverMail,
      idpId,
      open: true,
      rowData: data,
      isCard: true,
    });
  };

  const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false);

  if (assignCard) {
    return (
      <Button
        style={{ width: 'fit-content', boxShadow: 'none' }}
        variant="grey"
        data-cy="assignCard"
        onClick={() => inviteDriver()}
      >
        <CreditCardIcon style={{ marginRight: '0.5rem' }} fontSize="small" />{' '}
        {t('assignCard')}
      </Button>
    );
  }

  if (cancelReimbursementButton) {
    return (
      <div>
        <SuccessDialog
          isVisible={isCancelStatusDialogVisible}
          onClose={() => window.location.reload()}
          text={t('cancelReimbursementSuccessMessage', {
            cardNumber: cardnumber,
          })}
        />

        <CancelReimbursementDialog
          open={isCancelDialogOpen}
          onClose={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'popup_interaction',
                popup: {
                  name: `popup_cancel_reimbursement`,
                  interaction_type: 'close',
                },
              },
            });
            setIsCancelDialogOpen(!isCancelDialogOpen);
          }}
          onConfirm={async () => {
            const url = `/Reimbursement/Put_Cancel_Reimbursement_Service?${qs.stringify(
              {
                card_number: cardnumber,
                expiry_date: expiryDate,
              },
            )}`;
            await requester()({
              method: 'PUT',
              url,
              headers: {
                'Content-Type': 'application/json',
              },
            });
            setIsCancelDialogOpen(false);
            setIsCancelStatusDialogVisible(true);
          }}
          data={data}
        />
        <Button
          variant="grey"
          disableRipple
          style={{ color: '#FF3B30', borderColor: '#FF3B30' }}
          data-cy="cancelReimbursement"
          onClick={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'popup_interaction',
                popup: {
                  name: `popup_cancel_reimbursement`,
                  interaction_type: 'open',
                },
              },
            });
            setIsCancelDialogOpen(true);
          }}
        >
          {t('cancelReimbursement')}
        </Button>
      </div>
    );
  }

  // If first status is appUserOnly pass secound status into switch case to be handled as a driver
  // Second status can be request from FM, then check third

  // If not an appUserOnly and tariff_modified, make reviewing changes possible
  switch (
    status.length > 1 &&
    (status.includes('tariff_modified') ||
      status.includes('new_card_requested'))
      ? status.length > 1 && status.includes('tariff_modified')
        ? status[status.indexOf('tariff_modified')]
        : status.length > 1 && status.includes('new_card_requested')
        ? status[status.indexOf('new_card_requested')]
        : status[1]
      : status[0]
  ) {
    default:
      buttons = [<></>];
      break;
    case approvalStatus.newCardRequest:
      buttons = [
        <Button
          isInCardOverview
          fontSize="10px"
          variant="primary"
          data-cy="reviewAndApproveData"
          onClick={(e) => reviewIndirectCardRequest()}
        >
          {t('reviewAndApproveData')}
        </Button>,
      ];
      break;
    case approvalStatus.approved:
      if (status.filter((e) => e.indexOf('update') !== -1).length === 0) {
        buttons = [
          <Button
            isInCardOverview
            data-cy="updataDataRequest"
            fontSize="11px"
            onClick={(e) =>
              updateDataRequest(
                false,
                !tokens.filter((e) => e.reimbursement_status_id === 2).length >
                  0,
              )
            }
          >
            {t('updataDataRequest')}
          </Button>,
        ];
      } else {
        buttons = [
          <Button
            isInCardOverview
            data-cy="requestedChangesDriver"
            fontSize="12px"
            diable
            disabled
          >
            {t('requestedChangesDriver')}
          </Button>,
        ];
      }
      break;

    case approvalStatus.invited:
      buttons = [
        !superUser ? (
          <div className={classes.buttonOverlay}>
            {(isLoading && <CircularProgress />) || (
              <>
                <Button
                  isInCardOverview
                  data-cy="waitingDriverNotLoggedIn"
                  fontSize="10px"
                  variant="disabled"
                  disabled
                >
                  {t('waitingDriverNotLoggedIn')}
                </Button>
                <CancelButton
                  data-cy="cancelInvitation"
                  onClick={cancelInvitation}
                />
              </>
            )}
          </div>
        ) : (
          <Button
            isInCardOverview
            data-cy="waitingDriverNotLoggedIn"
            fontSize="10px"
            variant="disabled"
            disabled
          >
            {t('waitingDriverNotLoggedIn')}
          </Button>
        ),
      ];
      break;
    case approvalStatus.initialDataEntered:
      buttons = [
        <Button
          isInCardOverview
          fontSize="10px"
          variant="primary"
          data-cy="reviewAndApproveData"
          onClick={() => reviewAndApproveData()}
        >
          {t('reviewAndApproveData')}
        </Button>,
      ];
      break;
    case approvalStatus.tariffModified:
    case approvalStatus.wallboxModified:
    case approvalStatus.bankingModified:
      buttons = [
        <Button
          isInCardOverview
          fontSize="11px"
          variant="primary"
          data-cy="reviewAndApproveDataChanges"
          onClick={() => reviewAndApproveData()}
        >
          {t('reviewAndApproveDataChanges')}
        </Button>,
      ];
      break;
    case approvalStatus.initialDataRejected:
      buttons = [
        <Button
          isInCardOverview
          data-cy="pendingCorrectionsDriver"
          fontSize="10px"
          variant="disabled"
          disabled
        >
          {t('pendingCorrectionsDriver')}
        </Button>,
      ];
      break;
    case approvalStatus.loggedInOnce:
      buttons = [
        <Button
          isInCardOverview
          data-cy="waitingDriverAccepted"
          fontSize="10px"
          variant="disabled"
          disabled
        >
          {t('waitingDriverAccepted')}
        </Button>,
      ];
      break;
    case approvalStatus.appUserOnly:
      buttons = [
        <Button
          isInCardOverview
          variant={'primary'}
          data-cy="sendInvitation"
          onClick={appUserActivation}
        >
          {t('sendInvitation')}
        </Button>,
      ];
      break;
    case approvalStatus.wallboxUpdateRequested:
    case approvalStatus.bankingUpdateRequested:
      buttons = [
        <Button
          isInCardOverview
          data-cy="requestedChangesDriver"
          fontSize="10px"
          variant="disabled"
          disabled
        >
          {t('requestedChangesDriver')}
        </Button>,
      ];
      break;
    case approvalStatus.tariffUpdateRequested:
      if (
        status.includes('wallbox_update_requested') ||
        status.includes('banking_update_requested')
      ) {
        buttons = [
          <Button
            isInCardOverview
            data-cy="requestedChangesDriver"
            fontSize="10px"
            variant="disabled"
            disabled
          >
            {t('requestedChangesDriver')}
          </Button>,
        ];
      } else {
        buttons = [
          <Button
            isInCardOverview
            data-cy="updataDataRequest"
            fontSize="11px"
            onClick={(e) =>
              !tokens.filter((e) => e.reimbursement_status_id === 2).length > 0
                ? updateDataRequest(false, true)
                : updateDataRequest(true, false)
            }
          >
            {t('updataDataRequest')}
          </Button>,
        ];
      }
      break;
    case approvalStatus.created:
      buttons = [
        <Button
          isInCardOverview
          data-cy="reviewRequest"
          fontSize="11px"
          variant="primary"
          onClick={(e) => reviewIndirectRequest()}
        >
          {t('reviewData')}
        </Button>,
      ];

      break;
  }

  return (
    <>
      <SuccessDialog
        isVisible={isStatusDialogVisible}
        onClose={
          inUserDetails ? () => navigate('/') : () => window.location.reload()
        }
        text={`${t('cancelSuccess')} ${driverMail}`}
      />
      <ButtonBox>
        <div className={classes.buttonChild}>
          {superUser ? buttons[0].props.children : buttons[0]}
        </div>
      </ButtonBox>
    </>
  );
};

ButtonSwitch.defaultProps = {
  status: [],
  data: {},
};

ButtonSwitch.propTypes = {
  status: PropTypes.arrayOf(PropTypes.string),
  inUserDetails: PropTypes.bool,
  data: PropTypes.shape({
    card_number: PropTypes.string,
    reimbursement_status_id: PropTypes.number,
    email_address_ev_driver: PropTypes.string,
    evse_id: PropTypes.number,
    licence_plate: PropTypes.string,
    expiry_date: PropTypes.string,
  }),
};

export default ButtonSwitch;
