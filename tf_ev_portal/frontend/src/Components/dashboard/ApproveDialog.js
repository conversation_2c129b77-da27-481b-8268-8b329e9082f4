import { useCallback, useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import requester from '../../utils/requester';
import styled from 'styled-components';
import { Dialog } from '@material-ui/core';

import {
  Description,
  Error,
  InputWrapper,
  Row,
} from '../evDriver/PageTemplate';
import { Button } from '..';
import SuccessMessage from '../SuccessMessage';
import CircularProgress from '../CircularProgress';

import CloseIcon from '../myTeam/CloseIcon';

const Counter = styled.p`
  font-style: italic;
  text-align: initial;
  color: ${(props) => (props.full ? 'var(--error-color)' : 'default')};
`;

const DialogWrapper = styled.div`
  position: relative;
  width: auto;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  width: 500px;
  input {
    width: 500px;
    background-color: white;
    ::placeholder {
      color: grey;
      opacity: 1;
    }
  }
  button {
    width: fit-content;
    margin-left: 0.5rem;
  }
`;

const Headline = styled.h1`
  font-size: 30px;
`;

const ActionDescription = styled.div`
  margin: 2rem 0;
  font-size: 16px;
  line-height: 24px;
`;
const ButtonWrapper = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: flex-end;
`;

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const ErrorContainer = styled.div`
  margin-bottom: 1rem;
`;
const TextArea = styled.textarea`
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 20px;
  padding: 0.5rem;
`;

const ApproveDialog = ({
  open,
  action,
  onClose,
  id,
  email,
  isSales = false,
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [comment, setComment] = useState('');
  const { t } = useTranslation('superuser');
  const tServiceProvider = useTranslation('serviceProvider').t;
  const resetComponent = () => {
    setShowError(false);
    setIsLoading(false);
    setShowSuccess(false);
    setComment('');
  };

  useEffect(() => {
    if (open) {
      resetComponent();
    }
  }, [open]);

  const actions = {
    approve: useCallback(async () => {
      try {
        const rsp = await requester().patch(
          `/onboarding/approval/${id}`,
          {
            registration_status: 'approved',
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        return rsp;
      } catch (e) {
        console.error(e);
        return null;
      }
    }, [id]),
    reject: useCallback(async () => {
      try {
        const rsp = await requester().patch(
          `/onboarding/approval/${id}`,
          {
            registration_status: 'rejected',
            comment: comment,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        return rsp;
      } catch (e) {
        console.error(e);
        return null;
      }
    }, [id, comment]),
  };

  const performAction = async () => {
    setIsLoading(true);
    const rspData = await actions[action]();
    setShowError(rspData ? false : true);
    setShowSuccess(rspData ? true : false);
    setIsLoading(false);
  };

  return (
    <Dialog open={open} onClose={onClose}>
      <CloseIcon onClick={onClose} />
      {showSuccess ? (
        <SuccessMessage
          message={t(`${action}Sucess`, { fleetManagerMail: email })}
        />
      ) : (
        <DialogWrapper>
          {isSales && action === 'approve' &&(
            <>
              <Headline>{t('confirmation')}</Headline>
              <ActionDescription>
                {t('confirmationDescription')}
              </ActionDescription>
            </>
          )}
          {(!isSales || action === 'reject') &&(
            <ActionDescription>
              {t(`${action}Description`, { fleetManagerMail: email })}
            </ActionDescription>
          )}

          {action === 'reject' && (
            <Row>
              <InputWrapper isMobile>
                <Description>{t('comment')}</Description>
                <TextArea
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  maxLength={255}
                  placeholder={t('commentPlaceholder')}
                  rows={5}
                ></TextArea>
                <Counter full={comment.length === 255}>
                  {255 - comment.length}/255
                </Counter>
              </InputWrapper>
            </Row>
          )}

          {showError && (
            <ErrorContainer>
              <Error>{tServiceProvider('requestError')}</Error>
            </ErrorContainer>
          )}

          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button onClick={onClose} variant="secondary">
                {isSales ? t('cancel') : t(`cancelButton`)}
              </Button>
              <Button onClick={performAction} variant="primary">
                {isSales ? action === 'reject' ? t('approveRejection') : t('approve') : t(`${action}Action`)}
              </Button>
            </ButtonWrapper>
          )}
        </DialogWrapper>
      )}
    </Dialog>
  );
};

ApproveDialog.propTypes = {
  open: PropTypes.bool,
  action: PropTypes.string,
  email: PropTypes.string,
  id: PropTypes.number,
  onClose: PropTypes.func,
};

export default ApproveDialog;
