/* eslint-disable react/jsx-curly-newline */
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { IconButton, Tooltip } from '@material-ui/core';
import ButtonSwitch from './ButtonSwitch';
import IconSwitch from './IconSwitch';
import InfoIcon from '@mui/icons-material/Info';

import { ColoredAmount, Row, RowBox, RowContainer } from './RowTemplates';
import PersonIcon from '@mui/icons-material/Person';
import { useNavigate } from 'react-router-dom';
import { Box } from '@mui/material';
import ReimbursementStatus from '../ReimbursementStatus';

const TableRow = ({ short = false, rowData, superUser, fleetManagerId }) => {
  const { t } = useTranslation('directCustomer');

  const {
    tokens,
    firstname,
    lastname,
    driver_approval_status,
    ev_driver_idp_id,
    email_address_ev_driver,
    vehicle,
  } = rowData;
  const driverStatus = driver_approval_status
    ? driver_approval_status.map(
        ({ driver_approval_status_name }) => driver_approval_status_name,
      )
    : [null];

  const navigate = useNavigate();
  return (
    <Row data-cy="tableRow">
      <RowContainer>
        <RowBox width={2.5}></RowBox>
        <RowBox width={25}>{email_address_ev_driver || '-'}</RowBox>
        <RowBox width={12.5}>{firstname || '-'}</RowBox>
        <RowBox width={12.5}>{lastname || '-'}</RowBox>
        <RowBox width={10}>{vehicle ? vehicle.licence_plate : '-'}</RowBox>
        <RowBox width={5}>
          <Tooltip title={t('activeCards')}>
            <Box>
              <ColoredAmount
                margin
                color={
                  tokens.filter((e) => e.card_status).length > 0
                    ? '#090'
                    : '#323338'
                }
                background={
                  tokens.filter((e) => e.card_status).length > 0
                    ? 'rgba(153, 204, 0, 0.27)'
                    : '#dbdbdb'
                }
              >
                {tokens.filter((e) => e.card_status).length}
              </ColoredAmount>
            </Box>
          </Tooltip>
          <IconButton
            disabled={
              driver_approval_status[0].driver_approval_status_name ===
              'created'
            }
            style={{
              padding: '0.125rem',
            }}
            data-cy="viewUserDetails"
          >
            <InfoIcon
              onClick={() => {
                navigate(
                  `/userdetails/u/${
                    superUser ? `${encodeURIComponent(fleetManagerId)}/` : ''
                  }${encodeURIComponent(ev_driver_idp_id)}?nav=cards`,
                );
              }}
              fontSize="small"
            />
          </IconButton>
        </RowBox>
        <RowBox center width={10}>
          <ReimbursementStatus
            reimbursementStatusId={
              rowData.tokens.filter((e) => e.reimbursement_status_id === 2)
                .length > 0
                ? 2
                : null
            }
          />
        </RowBox>
        <RowBox center width={5}>
          <IconSwitch status={driverStatus} />
        </RowBox>
        <RowBox center width={12.5}>
          <ButtonSwitch
            superUser={superUser}
            status={driverStatus}
            data={rowData}
          />
        </RowBox>
        <RowBox center width={5}>
          <Tooltip
            title={
              driver_approval_status[0].driver_approval_status_name ===
              'created'
                ? t('userAfterActivation')
                : t('viewUserDetails')
            }
          >
            {/* Add span to enable tooltip on disabled button */}
            <span>
              <IconButton
                disabled={
                  driver_approval_status[0].driver_approval_status_name ===
                  'created'
                }
                style={{
                  border: '1px solid',
                  padding: '0.25rem',
                  borderRadius: '20px',
                }}
                data-cy="viewUserDetails"
              >
                <PersonIcon
                  onClick={() => {
                    navigate(
                      `/userdetails/u/${
                        superUser
                          ? `${encodeURIComponent(fleetManagerId)}/`
                          : ''
                      }${encodeURIComponent(ev_driver_idp_id)}`,
                    );
                  }}
                  fontSize="small"
                />
              </IconButton>
            </span>
          </Tooltip>
        </RowBox>
      </RowContainer>
    </Row>
  );
};

TableRow.defaultProps = {
  rowData: {},
};

TableRow.propTypes = {
  rowData: PropTypes.shape({
    card_number: PropTypes.string,
    card_status: PropTypes.bool,
    reimbursement_status_id: PropTypes.number,
    driver_approval_status: PropTypes.arrayOf(
      PropTypes.shape({
        driver_approval_status_name: PropTypes.string,
      }),
    ),
    evse_id: PropTypes.number,
    workprice: PropTypes.number,
    electricity_contract_valid_from: PropTypes.string,
    last_four_iban_digits: PropTypes.number,
    banking_data_valid_from: PropTypes.string,
    ev_driver_idp_id: PropTypes.string,
    email_address_ev_driver: PropTypes.string,
    licence_plate: PropTypes.string,
    additional_workprice: PropTypes.number,
    additional_electricity_contract_valid_from: PropTypes.string,
    location_of_wallbox: PropTypes.string,
  }),
};

export default TableRow;
