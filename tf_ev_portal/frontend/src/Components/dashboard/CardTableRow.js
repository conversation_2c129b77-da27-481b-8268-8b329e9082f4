/* eslint-disable react/jsx-curly-newline */
import { useTranslation } from 'react-i18next';
import { useContext } from 'react';
import PropTypes from 'prop-types';
import { IconButton, Tooltip } from '@material-ui/core';
import ButtonSwitch from './ButtonSwitch';
import PersonIcon from '@mui/icons-material/Person';

import { Row, RowBox, RowContainer } from './RowTemplates';
import { useNavigate } from 'react-router-dom';
import CardStatus from '../CardStatus';
import { StatusIndicator } from './RowTemplates';
import { userContext } from '../../ContextProvider';

const CardTableRow = ({
  isDisabled,
  token_visual_number,
  expiry_date,
  card_status,
  fleetManagerId,
  expiring_soon,
  email,
  superUser,
  idpId,
  issuer,
}) => {
  const { t } = useTranslation('directCustomer');
  const navigate = useNavigate();
  const { REACT_APP_KEYCLOAK_SUPERUSER_ROLE } = process.env;
  const { role } = useContext(userContext);
  const isSuperUser = role === REACT_APP_KEYCLOAK_SUPERUSER_ROLE;

  return (
    <Row data-cy="tableRow">
      <RowContainer>
        <RowBox width={2.5}></RowBox>
        <RowBox width={18}>
          {token_visual_number}
          {isSuperUser && issuer?.toLowerCase() === 'dcs' && (
            <StatusIndicator margin background="#dbdbdb" color="#323338">
              DCS
            </StatusIndicator>
          )}
        </RowBox>
        <RowBox width={10}>
          <CardStatus card_status={card_status} expiring_soon={expiring_soon} />
        </RowBox>
        <RowBox width={10}>
          {expiry_date
            ? `${expiry_date.substr(5, 2)}/${expiry_date.substr(2, 2)}`
            : '-'}
        </RowBox>
        <RowBox width={54.5}>
          {email ||
            (!superUser && card_status && (
              <ButtonSwitch
                assignCard
                data={{ token_visual_number, expiry_date }}
              />
            ))}
        </RowBox>
        <RowBox center width={5}>
          {email && (
            <Tooltip
              title={
                isDisabled ? t('userAfterActivation') : t('viewUserDetails')
              }
            >
              {/* Add span to enable tooltip on disabled button */}
              <span>
                <IconButton
                  disabled={isDisabled}
                  style={{
                    border: '1px solid',
                    padding: '0.25rem',
                    borderRadius: '20px',
                  }}
                  data-cy="viewUserDetails"
                >
                  <PersonIcon
                    onClick={() =>
                      navigate(
                        `/userdetails/c/${
                          superUser
                            ? `${encodeURIComponent(fleetManagerId)}/`
                            : ''
                        }${encodeURIComponent(idpId)}`,
                      )
                    }
                    fontSize="small"
                  />
                </IconButton>
              </span>
            </Tooltip>
          )}
        </RowBox>
      </RowContainer>
    </Row>
  );
};

CardTableRow.propTypes = {
  token_visual_number: PropTypes.string,
  expiry_date: PropTypes.string,
  card_status: PropTypes.bool,
  expiring_soon: PropTypes.bool,
  superUser: PropTypes.bool,
  email: PropTypes.string,
  fleetManagerId: PropTypes.string,
};

export default CardTableRow;
