import { useContext, useEffect } from 'react';
import DriverInvitation from '../../Views/DirectCustomer/DriverInvitation';
import DriverAssign from '../../Views/DirectCustomer/DriverAssign';
import InviteOrAssign from '../../Views/DirectCustomer/InviteOrAssign';
import IndirectDriverReview from '../../Views/DirectCustomer/IndirectDriverReview';
import AppDriverInvitation from '../../Views/DirectCustomer/AppUserActivation';
import { defaultDialogContext, dialogContext } from '../../ContextProvider';
import PropTypes from 'prop-types';
import DataApproval from '../../Views/DirectCustomer/DataApproval';
import RequestDataUpdate from '../../Views/DirectCustomer/RequestDataUpdate';
import TrainingView from '../../Views/DirectCustomer/TrainingView';
import TagManager from 'react-gtm-module';
import DriverAddCard from '../../Views/DirectCustomer/DriverAddCard';

const DialogRouter = ({ onDialogClose }) => {
  const { dialogData, setDialogData } = useContext(dialogContext);
  const {
    cardNumber,
    idpId,
    driverMail,
    evseId,
    type,
    open,
    expiryDate,
    onlyBank,
    training,
    gif,
    isSpecialRequest = false,
    rowData,
    isCard = false,
  } = dialogData;

  sessionStorage.setItem('triggerEvent', false);

  useEffect(() => {
    if (type && open && sessionStorage.getItem('triggerEvent') === 'false') {
      sessionStorage.setItem('triggerEvent', true);
      if (training) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'popup_interaction',
            popup: {
              name: `popup_${type}`,
              interaction_type: 'open',
              training: training.training_name,
            },
          },
        });
      } else {
        TagManager.dataLayer({
          dataLayer: {
            event: 'popup_interaction',
            popup: {
              name: `popup_${type}`,
              interaction_type: 'open',
            },
          },
        });
      }
    }
  }, [type, open, training]);

  const onClose = () => {
    if (training) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_${type}`,
            training: training.training_name,
            interaction_type: 'close',
          },
        },
      });
    } else {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_${type}`,
            interaction_type: 'close',
          },
        },
      });
    }

    sessionStorage.setItem('triggerEvent', false);
    setDialogData(defaultDialogContext);
    if (onDialogClose && typeof onDialogClose === 'function') {
      onDialogClose();
    }
  };

  switch (type) {
    case 'addCard':
      return <DriverAddCard open={open} onClose={onClose} />;
    case 'indirectRequest':
      return (
        <IndirectDriverReview
          cardNumber={cardNumber}
          expiryDate={expiryDate}
          open={open}
          rowData={rowData}
          onClose={onClose}
          isCard={isCard}
        />
      );
    case 'inviteOrAssign':
      return (
        <InviteOrAssign
          cardNumber={cardNumber}
          expiryDate={expiryDate}
          open={open}
          canModifyLicensePlate={false}
          onClose={onClose}
        />
      );

    case 'invitation':
      return (
        <DriverInvitation
          cardNumber={cardNumber}
          expiryDate={expiryDate}
          open={open}
          onClose={onClose}
        />
      );

    case 'assign':
      return (
        <DriverAssign
          cardNumber={cardNumber}
          expiryDate={expiryDate}
          open={open}
          onClose={onClose}
        />
      );
    case 'appUserActivation':
      return (
        <AppDriverInvitation
          cardNumber={cardNumber}
          driverMail={driverMail}
          expiryDate={expiryDate}
          open={open}
          onClose={onClose}
        />
      );
    case 'requestDataUpdate':
      return (
        <RequestDataUpdate
          idpId={idpId}
          driverMail={driverMail}
          evseId={evseId}
          open={open}
          onClose={onClose}
          onlyBank={onlyBank}
          isSpecialRequest={isSpecialRequest}
        />
      );
    case 'approval':
      return (
        <DataApproval
          idpId={idpId}
          driverMail={driverMail}
          open={open}
          onClose={onClose}
        />
      );
    case 'training':
      return (
        <TrainingView
          training={training}
          open={open}
          gif={gif}
          onClose={onClose}
        />
      );
    case 'driverTraining':
      return (
        <TrainingView
          isDriver
          training={training}
          open={open}
          gif={gif}
          onClose={onClose}
        />
      );
    default:
      return '';
  }
};

export default DialogRouter;

DriverInvitation.propTypes = {
  onDialogClose: PropTypes.func,
};
