import {
  useContext,
  useCallback,
  useEffect,
  useMemo,
  useState,
  useRef,
} from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useParams } from 'react-router-dom';
import logger from '../../utils/logger';
import requester from '../../utils/requester';
import CircularProgress from '../CircularProgress';
import debouce from 'lodash.debounce';
import Center from '../helper/Center';
import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from './DashboardTemplates';
import DialogRouter from './DialogRouter';
import RowFilterServer from './RowFilterServer';
import { RowBox, TitleRow } from './RowTemplates';
import CancelButton from '../CancelButton';
import TextField from '../TextField';
import { Box } from '@mui/system';
import TableRow from './TableRow';
import {
  CustomFilter,
  DashboardFilterContainer,
  QuickFilterButton,
  QuickFilterContainer,
  QuickFilterCount,
} from './DashboardFilter';
import approvalStatus from '../../constants/approvalStatus';
import GetAppIcon from '@material-ui/icons/GetApp';
import Button from '../Button';
import { downloadExcel } from '../../utils';
import { getUserType } from './IconSwitch';
import ManageCompanyDialog from '../superUser/ManageCompanyDialog';
import PrincipleDialog from './PrincipleDialog';
import TagManager from 'react-gtm-module';
import TimeoutWrapper from '../TimeoutWrapper';
import Timeout from '../Timeout';
import { userContext } from '../../ContextProvider';
import jwtDecode from 'jwt-decode';

const getParams = (id, filter, search, customFilter, sort) => {
  const params = [];
  if (id) {
    params.push(['fleetmanager_id', id]);
  }

  if (filter.length > 0) {
    params.push([
      'ev_driver_data_status',
      ['initial_data_entered', 'tariff_modified'].join(','),
    ]);
  } else {
    const statusFilters = Object.entries(customFilter.status)
      .filter((e) => {
        return e[1];
      })
      .map((e) => {
        return approvalStatus[e[0]];
      });
    if (statusFilters.length > 0) {
      params.push(['ev_driver_data_status', statusFilters.join(',')]);
    }
    const cardFilters = Object.entries(customFilter.card)
      .filter((e) => {
        return e[1];
      })
      .map((e) => {
        return e[0];
      });

    if (cardFilters.length > 0) {
      params.push(['card_status', cardFilters.join(',')]);
    }

    const reimbursementFilters = Object.entries(customFilter.reimbursement)
      .filter((e) => {
        return e[1];
      })
      .map((e) => {
        return e[0];
      });
    if (reimbursementFilters.length > 0) {
      params.push(['reimbursement_status', reimbursementFilters.join(',')]);
    }
  }
  if (search.length > 0) {
    params.push(['search', search]);
  }

  if (sort.attr !== '') {
    params.push(['order_by', `${sort.attr}:${sort.order}`]);
  }

  return params;
};

const UserOverview = ({ setDashboardUser, onlyPending }) => {
  const { t } = useTranslation('directCustomer');
  const [user, setUser] = useState({
    idp_id: null,
    provider: null,
    e_mail: null,
    function: null,
    principle: null,
  });
  const [counts, setCounts] = useState({
    pending_actions: 0,
  });
  const [cards, setCards] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [filter, setFilter] = useState(onlyPending ? ['pending_actions'] : []);
  const [sort, setSort] = useState({
    attr: '',
    order: '',
  });
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const { user: userFull } = useContext(userContext);

  let role = '';
  try {
    role = jwtDecode(userFull.access_token).role;
  } catch (e) {
    role = '';
  }

  const searchInput = useRef(null);
  // reset filter if not redirected from pending actions widget
  useEffect(() => {
    if (onlyPending === null) {
      resetFilter();
    }
  }, [onlyPending]);

  const [pageSize, setPageSize] = useState(
    window.innerHeight >= 1175 ? 20 : 10,
  );
  const navigate = useNavigate();

  const handleSearch = (e) => {
    setSearchTerm(e.target.value.trim());
  };

  const debouncedSearch = useMemo(() => {
    return debouce(handleSearch, 750);
  }, []);

  const onDownloadClick = async () => {
    const rspData = await getData([]);
    if (rspData.information) {
      const rows = JSON.parse(JSON.stringify(rspData.information)).map(
        (row) => {
          const driverStatus = row.driver_approval_status
            ? row.driver_approval_status.map(
                ({ driver_approval_status_name }) =>
                  driver_approval_status_name,
              )
            : [null];
          const transformedRow = {
            [t('email_address_ev_driver')]: row.email_address_ev_driver,
            [t('firstname')]: row.firstname,
            [t('lastname')]: row.lastname,
            [t('licence_plate')]: row.vehicle?.licence_plate || '-',
            [t('allCards')]: row.tokens.length,
            [t('reimbursement_status_id')]:
              row.tokens.filter((e) => e.reimbursement_status_id === 2).length >
              0
                ? t('statusActive')
                : t('statusInactive'),
            [t('platform')]: t(getUserType(driverStatus, true)),
          };
          return transformedRow;
        },
      );
      downloadExcel(`Drivers.xlsx`, rows);
    }
  };

  const emptyCustomFilter = {
    card: {
      active: false,
      inactive: false,
      expiring_soon: false,
    },
    reimbursement: {
      active: false,
      inactive: false,
    },
    status: {
      invited: false,
      loggedInOnce: false,
      initialDataRejected: false,
      initialDataEntered: false,
      tariffModified: false,
      wallboxUpdateRequested: false,
      bankingUpdateRequested: false,
      approved: false,
    },
  };

  const [customFilter, setCustomFilter] = useState(emptyCustomFilter);

  const resetCustomFilter = () => {
    setCustomFilter(emptyCustomFilter);
  };
  const resetFilter = () => {
    setFilter([]);
  };

  const { id } = useParams();

  const superUser = id;

  //reset scroll to prevent infinite scrolling on page change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  const getCompanyData = async () => {
    try {
      const { fleetManagers } = (
        await requester().get('/authorities/fleetmanagers')
      ).data;
      return fleetManagers;
    } catch (err) {
      if (err.response.status === 504) {
        logger().error(`Timeout of request for /authorities/fleetmanagers.`);
        setError(true);
        setIsLoading(false);
        return 'error';
      } else {
        logger().error(`Couldn't get overview data from api.\n${err.message}`);
        setIsLoading(false);
        return [];
      }
    }
  };
  const [changeCompanyData, setChangeCompanyData] = useState(null);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [error, setError] = useState(false);

  const manageCompanyId = async () => {
    const companyData = await getCompanyData();
    setChangeCompanyData(
      companyData.filter((company) => company.IdpId === user.idp_id)[0],
    );
    setIsDialogVisible(true);
  };

  const getData = useCallback(
    async (params, limit, offset, noLoad) => {
      if (!noLoad) {
        setIsLoading(true);
      }
      try {
        let rsp = null;
        if ((limit && offset) || (limit && offset === 0)) {
          rsp = await requester().get(
            `/Information_Overview/card_overview_by_driver?offset=${offset}&limit=${limit}`,
            {
              params: new URLSearchParams(params),
            },
          );
        } else {
          rsp = await requester().get(
            `/Information_Overview/card_overview_by_driver?offset=0&limit=${pageSize}`,
            {
              params: new URLSearchParams(params),
            },
          );
        }
        if (!noLoad) {
          setIsLoading(false);
        }
        return rsp.data;
      } catch (err) {
        if (err.response.status === 504) {
          logger().error(
            `Timeout of request for /Information_Overview/card_overview_by_driver.`,
          );
          setError(true);
          if (!noLoad) {
            setIsLoading(false);
          }
          return 'error';
        } else {
          logger().error(
            `Couldn't get overview data from api.\n${err.message}`,
          );
          if (!noLoad) {
            setIsLoading(false);
          }
          return {};
        }
      }
    },
    [pageSize],
  );

  useEffect(() => {
    const getMoreData = async (limit, offset) => {
      const data = await getData(
        getParams(id, filter, searchTerm, customFilter, sort),
        limit,
        offset,
        true,
      );

      return data;
    };

    const handleScroll = async () => {
      const bottom =
        Math.ceil(window.innerHeight + window.scrollY) >=
        document.documentElement.scrollHeight;

      if (bottom && !isLoadingMore && !isLoading) {
        if (counts.total > pageSize) {
          setIsLoadingMore(true);
          const newData = await getMoreData(10, pageSize);
          setCards(cards.concat(newData.information));

          setPageSize(pageSize + 10);
          setIsLoadingMore(false);
        }
      }
    };
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [
    setPageSize,
    counts.total,
    pageSize,
    id,
    filter,
    searchTerm,
    customFilter,
    sort,
    getData,
    isLoadingMore,
    isLoading,
    cards,
  ]);

  const reFetchData = async () => {
    setPageSize(window.innerHeight >= 1175 ? 20 : 10);
    const rspData = await getData(
      getParams(id, filter, searchTerm, customFilter, sort, pageSize, 0),
    );
    if (rspData.information && rspData.user) {
      setCards(rspData.information);
      setUser(rspData.user);
      setCounts(rspData.counts);
      setDashboardUser(rspData.user);
    } else {
      if (rspData === 'error') {
      } else {
        navigate('/error', { replace: true });
      }
    }
  };

  useEffect(() => {
    if (!isLoading) {
      searchInput.current.focus();
    }
  }, [isLoading]);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getData(
        getParams(id, filter, searchTerm, customFilter, sort, pageSize, 0),
      );
      if (rspData.information && rspData.user) {
        setCards(rspData.information);
        setCounts(rspData.counts);
        setUser(rspData.user);
        setDashboardUser(rspData.user);
      } else {
        if (rspData === 'error') {
        } else {
          navigate('/error', { replace: true });
        }
      }
    };
    if (!isLoading) {
      fetchData();
    }
  }, [customFilter, filter, searchTerm, sort]);

  const quickFilters = [
    {
      attrName: 'pending_actions',
      bgColor: 'rgba(153, 204, 0, 0.27)',
      textColor: '#090',
    },
  ];
  const [isPrincipleDialogVisible, setIsPrincipleDialogVisible] =
    useState(false);

  const managePrinciple = async () => {
    setTimeout(() => {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_manage_principle`,
            interaction_type: 'open',
          },
        },
      });
    }, 200);
    setIsPrincipleDialogVisible(true);
  };

  return (
    <>
      <DialogRouter onDialogClose={reFetchData} />
      <PrincipleDialog
        open={isPrincipleDialogVisible}
        pendingActions={
          cards.filter((e) => {
            return (
              e.driver_approval_status?.filter(
                (el) =>
                  el.driver_approval_status_name ===
                    approvalStatus.initialDataEntered ||
                  el.driver_approval_status_name ===
                    approvalStatus.tariffModified ||
                  el.driver_approval_status_name ===
                    approvalStatus.bankingModified ||
                  el.driver_approval_status_name ===
                    approvalStatus.wallboxModified,
              ).length > 0
            );
          }).length
        }
        currentPrinciple={user.principle}
        onClose={() => {
          TagManager.dataLayer({
            dataLayer: {
              event: 'popup_interaction',
              popup: {
                name: `popup_manage_principle`,
                interaction_type: 'close',
              },
            },
          });
          window.location.reload();
        }}
      />
      {user && user.idp_id && (
        <ManageCompanyDialog
          needConfirmation={role.toLowerCase() === 'support'}
          open={isDialogVisible}
          data={changeCompanyData}
          onClose={() => {
            setIsDialogVisible(false);
          }}
        />
      )}

      <DashboardFilterContainer>
        <QuickFilterContainer>
          {quickFilters.map((quickFilter) => {
            const { attrName, bgColor, textColor } = quickFilter;
            const isFilterActive =
              filter.filter((e) => e === attrName).length > 0;
            return (
              <div style={{ position: 'relative' }} key={attrName}>
                <QuickFilterButton
                  textColor={textColor}
                  bgColor={bgColor}
                  active={isFilterActive}
                  disabled={isLoading || isLoadingMore}
                  onClick={() => {
                    if (isLoading || isLoadingMore) {
                      //Prevent Filter set on loading --> disabled does work locally but not on QA
                    } else {
                      if (isFilterActive) {
                        setFilter((prevFilter) =>
                          prevFilter.filter((e) => e !== attrName),
                        );
                      } else {
                        resetCustomFilter();
                        setFilter((prevFilter) => [...prevFilter, attrName]);
                      }
                    }
                  }}
                >
                  <QuickFilterCount bgColor={textColor}>
                    {counts[attrName]}
                  </QuickFilterCount>
                  {t(attrName)}
                </QuickFilterButton>
                {isFilterActive && (
                  <CancelButton
                    color={textColor}
                    onClick={() => {
                      setFilter((prevFilter) =>
                        prevFilter.filter((e) => e !== attrName),
                      );
                    }}
                  />
                )}
              </div>
            );
          })}

          <Box ml={'auto'}>
            <TextField
              search
              ref={searchInput}
              type="text"
              onChange={debouncedSearch}
              disabled={isLoading}
            />
          </Box>
          {superUser && (
            // (user.provider?.toLowerCase() === 'aral' ||
            //   user.provider?.toLowerCase() === 'bp') &&
            <Box ml="0.5rem">
              <Button onClick={manageCompanyId} variant="grey">
                {t('manageCompany')}
              </Button>
            </Box>
          )}
          {!superUser && user.function === 'admin' && (
            <Box ml="0.5rem">
              <Button onClick={managePrinciple} variant="grey">
                {t('managePrinciple')}
              </Button>
            </Box>
          )}
          <Box ml="0.5rem">
            <CustomFilter
              disabled={isLoading || isLoadingMore}
              customFilters={customFilter}
              setCustomFilters={setCustomFilter}
              resetCustomFilters={resetCustomFilter}
              resetFilter={resetFilter}
            />
          </Box>
          <Box ml="0.5rem">
            <Button onClick={onDownloadClick} variant="grey">
              <GetAppIcon fontSize="small" />
              {t('downloadData')}
            </Button>
          </Box>
        </QuickFilterContainer>
      </DashboardFilterContainer>
      <Wrapper>
        <TimeoutWrapper>
          <TableContainer>
            <TitleRow>
              <HeadlineBox>
                <RowBox width={2.5}></RowBox>
                {[
                  { width: 25, attrName: 'email_address_ev_driver' },
                  { width: 12.5, attrName: 'firstname' },
                  { width: 12.5, attrName: 'lastname' },
                  { width: 10, attrName: 'licence_plate' },
                  { width: 5, attrName: 'card_count', noFilter: true },
                  {
                    width: 10,
                    attrName: 'reimbursement_status_id',
                    noFilter: true,
                  },
                  { width: 5, attrName: 'platform', noFilter: true },
                  { width: 12.5, attrName: 'next_action', noFilter: true },
                ].map((e) => (
                  <RowBox key={e.attrName} width={e.width}>
                    {e.noFilter ? (
                      <div
                        style={{
                          display: 'flex',
                          justifyContent: 'center',
                          width: '100%',
                        }}
                      >
                        <b>{t(e.attrName)}</b>
                      </div>
                    ) : (
                      <RowFilterServer
                        sortAttr={e.attrName}
                        text={t(e.attrName)}
                        sort={sort}
                        setSort={setSort}
                      />
                    )}
                  </RowBox>
                ))}
              </HeadlineBox>
            </TitleRow>
            {!isLoading && cards.length === 0 && !error && (
              <Center style={{ padding: '10rem' }}>
                <h2>{t('noDataAvailable')}</h2>
              </Center>
            )}
            {isLoading && (
              <LoadingCircleContainer style={{ padding: '2rem' }}>
                <CircularProgress />
              </LoadingCircleContainer>
            )}
            {!isLoading && error && <Timeout />}

            {!isLoading && !error && (
              <div>
                {cards.map((row, i) => {
                  if (i < pageSize) {
                    return (
                      <TableRow
                        rowData={row}
                        fleetManagerId={id}
                        key={`row-${row.ev_driver_idp_id}`}
                        superUser={superUser}
                      />
                    );
                  }
                  return null;
                })}
                <>
                  {isLoadingMore && (
                    <LoadingCircleContainer style={{ padding: '2rem' }}>
                      <CircularProgress />
                    </LoadingCircleContainer>
                  )}
                </>
              </div>
            )}
          </TableContainer>
        </TimeoutWrapper>
      </Wrapper>
    </>
  );
};

export default UserOverview;
