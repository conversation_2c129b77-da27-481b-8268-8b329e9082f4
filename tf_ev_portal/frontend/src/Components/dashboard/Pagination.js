import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { makeStyles } from '@material-ui/styles';

const Pagination = ({ count, pageSize, position, onChange }) => {
  const { t } = useTranslation('directCustomer');

  const useStyles = makeStyles({
    button: {
      height: '30px',
      margin: '0 0 0 5px',
      padding: '0 12px',
      border: 'none',
      cursor: 'pointer',
      borderRadius: '4px',
      boxShadow: '0 3px 6px 0 rgba(0, 0, 0, 0.16)',
      fontSize: '14px',
      backgroundColor: '#ffffff',
      outline: 'none',
      '&:disabled': {
        backgroundColor: 'var(--trafineo-grau-20) !important',
        cursor: 'not-allowed',
      },
      '&:hover': {
        backgroundColor: 'var(--trafineo-rot-20)',
      },
    },
    seleceted: {
      backgroundColor: 'var(--trafineo-rot-100)',
      color: 'white',
    },
    pagination: {
      margin: '1.5rem 0',
    },
    filler: {
      height: '30px',
      margin: '0 0 0 5px',
      padding: '0 12px',
      display: 'inline',
    },
  });
  const classes = useStyles();

  const Button = ({ text, ...props }) => {
    return (
      <button
        className={`${classes.button} ${
          text === position + 1 ? classes.seleceted : ''
        }`}
        onClick={() => {
          if (text === t('prev')) {
            onChange(position - 1);
          } else if (text === t('next')) {
            onChange(position + 1);
          } else {
            onChange(Number(text) - 1);
          }
        }}
        {...props}
        type="button"
      >
        {text}
      </button>
    );
  };

  const pagination = [];
  pagination.push(<Button disabled={position === 0} text={t('prev')} />);
  const siteSum = Math.ceil(count / pageSize);
  const Filler = () => <div className={classes.filler}>...</div>;

  if (siteSum <= 5) {
    for (let i = 0; i < Math.ceil(count / pageSize); i += 1) {
      pagination.push(<Button text={i + 1} />);
    }
  } else if (position <= 2) {
    pagination.push(<Button text={1} />);
    pagination.push(<Button text={2} />);
    pagination.push(<Button text={3} />);
    pagination.push(<Button text={4} />);
    pagination.push(<Filler />);
    pagination.push(<Button text={siteSum} />);
  } else if (position + 3 >= siteSum) {
    pagination.push(<Button text={1} />);
    pagination.push(<Filler />);
    pagination.push(<Button text={siteSum - 3} />);
    pagination.push(<Button text={siteSum - 2} />);
    pagination.push(<Button text={siteSum - 1} />);
    pagination.push(<Button text={siteSum} />);
  } else {
    pagination.push(<Button text={1} />);
    pagination.push(<Filler />);
    pagination.push(<Button text={position} />);
    pagination.push(<Button text={position + 1} />);
    pagination.push(<Button text={position + 2} />);
    pagination.push(<Filler />);
    pagination.push(<Button text={siteSum} />);
  }
  pagination.push(
    <Button
      data-cy="nextButton"
      disabled={
        position === Math.ceil(count / pageSize) - 1 || count <= pageSize
      }
      text={t('next')}
    />,
  );
  return <div className={classes.pagination}>{pagination}</div>;
};

Pagination.propTypes = {
  count: PropTypes.number,
  position: PropTypes.number,
  pageSize: PropTypes.number,
  onChange: PropTypes.func,
};

export default Pagination;
