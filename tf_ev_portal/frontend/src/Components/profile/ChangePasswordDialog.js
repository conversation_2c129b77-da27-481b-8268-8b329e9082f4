import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { idpRequest } from '../../utils/requester';
import styled from 'styled-components';
import { IconButton } from '@material-ui/core';
import ClearIcon from '@material-ui/icons/Clear';
import CheckIcon from '@material-ui/icons/Check';
import { ButtonWrapper, Error, Row } from '../evDriver/PageTemplate';
import { Button } from '..';
import CircularProgress from '../CircularProgress';
import TextField from '../TextField';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import DialogWrapper from '../DialogWrapper';
import TagManager from 'react-gtm-module';

export const TextFieldWrapper = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
  position: relative;
  button {
    position: absolute;
    top: 36px;
    right: 6px;
    height: 32.5px;
    width: 32.5px;
  }
`;

const PasswordCriteriaText = styled.div`
  font-size: 14px;
  margin-left: 0.25rem;
  text-align: left;
`;

const PasswordCriteriaWrapper = styled.div`
  color: ${(props) => (props.valid === true ? 'green' : 'var(--error-color)')};
  display: flex;
  align-items: center;
  padding: 0.25rem 0;
`;

const Center = styled.div`
  display: flex;
  justify-content: center;
`;

const ErrorContainer = styled.div`
  margin: 1rem 0;
`;

export const PasswordCriteria = ({ valid, children }) => {
  return (
    <PasswordCriteriaWrapper valid={valid}>
      {valid ? <CheckIcon fontSize="small" /> : <ClearIcon fontSize="small" />}
      <PasswordCriteriaText>{children}</PasswordCriteriaText>
    </PasswordCriteriaWrapper>
  );
};

const ChangePasswordDialog = ({ open, onClose, email }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [showError, setShowError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [oldPw, setOldPw] = useState('');
  const [newPw, setNewPw] = useState('');
  const [newPwRepeat, setNewPwRepeat] = useState('');
  const [newPwVisible, setNewPwVisible] = useState(false);
  const [newPwRepeatVisible, setNewPwRepeatVisible] = useState(false);
  const { t } = useTranslation('profile');
  const resetComponent = () => {
    setShowError(false);
    setIsLoading(false);
    setShowSuccess(false);
    setOldPw('');
    setNewPw('');
    setNewPwRepeat('');
  };

  useEffect(() => {
    if (open) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_changePasswordDriver`,
            interaction_type: 'open',
          },
        },
      });
      resetComponent();
    }
  }, [open]);

  const changePassword = async () => {
    setIsLoading(true);
    try {
      await idpRequest().post(
        `/changePassword`,
        {},
        {
          headers: {
            Accept: 'application/json',
            oldPassword: oldPw,
            newPassword: newPw,
          },
        },
      );
      setShowError(false);
      setShowSuccess(true);
    } catch (error) {
      console.error(error);
      setOldPw('');
      setShowError(true);
      setIsLoading(false);
    }
  };

  const trackEventAndClose = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'popup_interaction',
        popup: {
          name: `popup_changePasswordDriver`,
          interaction_type: 'close',
        },
      },
    });
    onClose();
  };

  return (
    <DialogWrapper
      open={open}
      onClose={trackEventAndClose}
      successMessage={t('changePasswordSuccess')}
      headline={t('changePassword')}
      showSuccess={showSuccess}
    >
      <form>
        <input type="hidden" autocomplete="username" value={email} />
        <Row>
          <TextFieldWrapper>
            <TextField
              autoFocus
              newDriver
              autoComplete="current-password"
              type="password"
              data-cy="oldPw"
              value={oldPw}
              placeholder={t('oldPwPlaceholder')}
              label={t('oldPw')}
              name="oldPassword"
              onChange={(e) => {
                setOldPw(e.target.value);
              }}
            />
          </TextFieldWrapper>
        </Row>
        <Row>
          <TextFieldWrapper>
            <TextField
              newDriver
              data-cy="newPw"
              type={newPwVisible ? 'text' : 'password'}
              value={newPw}
              autoComplete="new-password"
              placeholder={t('newPwPlaceholder')}
              label={t('newPw')}
              name="newPassword"
              onChange={(e) => {
                setNewPw(e.target.value);
              }}
            />
            <IconButton
              aria-label="toggle password visibility"
              onClick={() => setNewPwVisible(!newPwVisible)}
            >
              {newPwVisible ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </TextFieldWrapper>
        </Row>
        <Row>
          <TextFieldWrapper>
            <TextField
              newDriver
              data-cy="newPwRepeat"
              type={newPwRepeatVisible ? 'text' : 'password'}
              value={newPwRepeat}
              autoComplete="new-password"
              placeholder={t('newPwRepeatPlaceholder')}
              label={t('newPwRepeat')}
              name="newPasswordRepeat"
              onChange={(e) => {
                setNewPwRepeat(e.target.value);
              }}
            />
            <IconButton
              aria-label="toggle password visibility"
              onClick={() => setNewPwRepeatVisible(!newPwRepeatVisible)}
            >
              {newPwRepeatVisible ? <VisibilityOff /> : <Visibility />}
            </IconButton>
          </TextFieldWrapper>
        </Row>
        <PasswordCriteria valid={newPw.match(/[a-zA-Z]/gm) ? true : false}>
          {t('pwCriterieaOneLetter')}
        </PasswordCriteria>
        <PasswordCriteria valid={newPw.match(/[0-9]/gm) ? true : false}>
          {t('pwCriterieaOneNumber')}
        </PasswordCriteria>
        <PasswordCriteria valid={newPw.match(/[@$!%*?&]/gm) ? true : false}>
          {t('pwCriterieaSpecialCharakter')}
        </PasswordCriteria>
        <PasswordCriteria valid={newPw.length >= 8}>
          {t('pwCriteriea8Charakters')}
        </PasswordCriteria>
        <PasswordCriteria valid={newPw.length >= 8 && newPw === newPwRepeat}>
          {t('pwCriterieaPasswordsMatch')}
        </PasswordCriteria>
        {showError && (
          <ErrorContainer>
            <Error>{t('passwordError')}</Error>
          </ErrorContainer>
        )}

        {isLoading ? (
          <Center>
            <CircularProgress />
          </Center>
        ) : (
          <ButtonWrapper>
            <Button onClick={onClose} variant="secondary">
              {t(`cancelButton`)}
            </Button>
            <Button
              disabled={
                !(
                  newPw.match(
                    /^(?=.*\d)(?=.*[@$!%*?&])(?=.*[a-zA-Z]).{8,}$/gm,
                  ) &&
                  newPw === newPwRepeat &&
                  oldPw !== ''
                )
              }
              onClick={changePassword}
              variant="primary"
            >
              {t('changePassword')}
            </Button>
          </ButtonWrapper>
        )}
      </form>
    </DialogWrapper>
  );
};

ChangePasswordDialog.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  email: PropTypes.string,
};

export default ChangePasswordDialog;
