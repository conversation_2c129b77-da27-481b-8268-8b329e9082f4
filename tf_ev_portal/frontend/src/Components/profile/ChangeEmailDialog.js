import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { useTranslation } from 'react-i18next';
import { TextField, Button } from '..';
import { idpRequest } from '../../utils/requester';
import Center from '../helper/Center';
import CircularProgress from '../CircularProgress';
import DialogWrapper from '../DialogWrapper';
import { TextFieldWrapper } from './ChangePasswordDialog';
import { ButtonWrapper, Error, Row } from '../evDriver/PageTemplate';
import TagManager from 'react-gtm-module';

const ChangeEmailDialog = ({ open, onClose }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [newEmail, setNewEmail] = useState('');
  const [password, setPassword] = useState('');
  const { t } = useTranslation('profile');

  useEffect(() => {
    if (open) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'popup_interaction',
          popup: {
            name: `popup_changeEmailDriver`,
            interaction_type: 'open',
          },
        },
      });
    }
  }, [open]);

  const resetComponent = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'popup_interaction',
        popup: {
          name: `popup_changeEmailDriver`,
          interaction_type: 'close',
        },
      },
    });
    setNewEmail('');
    setIsErrorVisible(false);
    setPassword('');
    setShowSuccess(false);
    onClose();
  };

  const changeMail = async () => {
    setIsLoading(true);
    try {
      await idpRequest().post(
        `/changeEmail`,
        { newEmail, password },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      setIsErrorVisible(false);
      setShowSuccess(true);
    } catch (error) {
      console.error(error);
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  return (
    <DialogWrapper
      headline={t('changeEmailHeadline')}
      successMessage={t('changeEmailSucess')}
      showSuccess={showSuccess}
      open={open}
      onClose={resetComponent}
    >
      <Row>
        <TextFieldWrapper>
          <TextField
            autoFocus
            newDriver
            label={t('newMail')}
            name="newEmail"
            type="text"
            value={newEmail}
            onChange={(e) => {
              const { value } = e.target;
              setNewEmail(value);
            }}
          />
        </TextFieldWrapper>
      </Row>
      <Row>
        <TextFieldWrapper>
          <TextField
            newDriver
            autoComplete="current-password"
            label={t('password')}
            value={password}
            type="password"
            onChange={(e) => {
              const { value } = e.target;
              setPassword(value);
            }}
            fatLabel
          />
        </TextFieldWrapper>
      </Row>
      {isErrorVisible && (
        <Row>
          <Error>{t('changeEmailError')}</Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button variant="primary" onClick={changeMail}>
            {t('changeEmailButton')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

ChangeEmailDialog.propTypes = {
  open: PropTypes.bool,
  data: PropTypes.string,
  onClose: PropTypes.func,
};

export default ChangeEmailDialog;
