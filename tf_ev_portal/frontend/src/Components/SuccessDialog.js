import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import SuccessMessage from './SuccessMessage';
import CloseIcon from './myTeam/CloseIcon';
import { Dialog } from '@mui/material';

const SuccessDialog = ({ isVisible, text, onClose, closeDelay }) => {
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const delay = closeDelay || 3000;

  useEffect(() => {
    if (isVisible) {
      setIsDialogVisible(true);
      setTimeout(() => {
        setIsDialogVisible(false);
        onClose();
      }, delay);
    }
  }, [isVisible, onClose, delay]);

  return (
    <Dialog
      maxWidth={false}
      scroll="paper"
      open={isDialogVisible}
      onClose={onClose}
    >
      <CloseIcon onClick={onClose} />
      <SuccessMessage message={text} />
    </Dialog>
  );
};

SuccessDialog.propTypes = {
  isVisible: PropTypes.bool,
  text: PropTypes.string,
  onClose: PropTypes.func,
  closeDelay: PropTypes.number,
};

export default SuccessDialog;
