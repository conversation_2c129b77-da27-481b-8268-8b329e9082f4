import { useEffect } from 'react';
import { Divider, Grid } from '@mui/material';
import jwtDecode from 'jwt-decode';
import { useContext, useState } from 'react';
import { isMobile } from 'react-device-detect';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { userContext, evDriverContext } from '../../ContextProvider';
import { EditButton } from '../ActionButtons';
import EditWrapper from '../EditWrapper';
import {
  Description,
  InputWrapper,
  Row,
  StaticValue,
} from '../evDriver/PageTemplate';
import ChangeEmailDialog from '../profile/ChangeEmailDialog';
import ChangePasswordDialog from '../profile/ChangePasswordDialog';
import TextField from '../TextField';
import { useCallbackPrompt } from '../useCallbackPrompt';
import DialogWrapper from '../DialogWrapper';
import Button from '../Button';
import ErrorIcon from '@mui/icons-material/Error';

const SectionLabel = styled.div`
  font-weight: bold;
  font-size: 14px;
  line-height: 21px;
  margin-bottom: 1.5rem;
  height: fit-content;
`;

const CenterGrid = styled(Grid)`
  text-align: center;
`;

const StyledUpdateIcon = styled(ErrorIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: #ff7e00 !important;
  margin-bottom: -3.5px;
`;

const PersonalInfo = () => {
  const { t } = useTranslation('evDriver');
  const { t: ta } = useTranslation('actions');
  const { user } = useContext(userContext);
  const { evDriverData } = useContext(evDriverContext);
  const navigate = useNavigate();
  let email = '';
  let newMail = '';
  let changeValid = false;

  try {
    const token = jwtDecode(user.access_token);
    email = token.preferred_username;
    newMail = token.newemail;
    const changeExpire = token['newemail-expiration'];
    changeValid = Number(changeExpire) * 1000 > Date.parse(new Date());
  } catch (e) {
    console.error(e);
    navigate('/error', { replace: true });
  }

  const [firstname, setFirstName] = useState({
    value: evDriverData?.firstname || undefined,
    error: false,
  });
  const [lastname, setLastname] = useState({
    value: evDriverData?.lastname || undefined,
    error: false,
  });
  const [changeEmailVisible, setChangeEmailVisible] = useState(false);
  const [changePasswordVisible, setChangePasswordVisible] = useState(false);
  const [showDialog, setShowDialog] = useState(false);
  const [showPrompt, confirmNavigation, cancelNavigation] =
    useCallbackPrompt(showDialog);

  const [earlyPrompt, setEarlyPromt] = useState({ type: null, show: false });

  const bankingUpdateRequested = evDriverData.approvalStatus.includes(
    'banking_update_requested',
  );

  const checkChanges = (type) => {
    if (showDialog) {
      setEarlyPromt({ type: type, show: true });
    } else {
      if (type === 'email') {
        setChangeEmailVisible(true);
      } else if (type === 'password') {
        setChangePasswordVisible(true);
      }
    }
  };

  const handleConfirm = () => {
    if (earlyPrompt.type === 'email') {
      setChangeEmailVisible(true);
    }
    if (earlyPrompt.type === 'password') {
      setChangePasswordVisible(true);
    }
    setEarlyPromt({ ...earlyPrompt, show: false });
    setShowDialog(false);
    confirmNavigation();
  };

  const handleLeave = () => {
    if (earlyPrompt.type === 'email') {
      setChangeEmailVisible(false);
    }
    if (earlyPrompt.type === 'password') {
      setChangePasswordVisible(false);
    }
    setEarlyPromt({ ...earlyPrompt, show: false });
    cancelNavigation();
  };

  useEffect(() => {
    if (
      (firstname.value === undefined ||
        firstname.value === null ||
        firstname.value === '') &&
      (lastname.value === undefined ||
        lastname.value === null ||
        lastname.value === '')
        ? true
        : lastname.value === evDriverData.lastname &&
          firstname.value === evDriverData.firstname
    ) {
      setShowDialog(false);
    } else {
      setShowDialog(true);
    }
  }, [firstname, lastname, evDriverData]);

  const validateInputs = () => {
    let error = false;

    if (!firstname.value || firstname.value.trim() === '') {
      setFirstName({ ...firstname, error: true });
      error = true;
    }

    if (!lastname.value || lastname.value.trim() === '') {
      setLastname({ ...lastname, error: true });
      error = true;
    }
    setShowDialog(false);

    return !error;
  };

  return (
    <Grid container>
      <DialogWrapper
        open={showPrompt || earlyPrompt.show}
        onClose={cancelNavigation}
        headline={t('warning')}
      >
        <Grid container>
          <Grid item xs={12}>
            <p>{t('leavePageConfirm')}</p>
            <br />
          </Grid>
          <CenterGrid item xs={6}>
            <Button onClick={handleConfirm}>{t('leavePage')}</Button>
          </CenterGrid>
          <CenterGrid item xs={6}>
            <Button onClick={handleLeave}>{t('stayPage')}</Button>
          </CenterGrid>
        </Grid>
      </DialogWrapper>
      {changeEmailVisible && (
        <ChangeEmailDialog
          open
          data={email}
          onClose={() => {
            setChangeEmailVisible(false);
            window.location.reload();
          }}
        />
      )}
      <ChangePasswordDialog
        open={changePasswordVisible}
        email={email}
        onClose={() => {
          setChangePasswordVisible(false);
          window.location.reload();
        }}
      />
      <Grid item xs={12} md={5.5}>
        <EditWrapper
          disabled={
            firstname.value === undefined ||
            firstname.value === null ||
            firstname.value === '' ||
            lastname.value === undefined ||
            lastname.value === null ||
            lastname.value === ''
              ? true
              : lastname.value === evDriverData.lastname &&
                firstname.value === evDriverData.firstname
          }
          beforeSafe={validateInputs}
          data={{
            firstname: firstname.value,
            lastname: lastname.value,
          }}
        >
          <SectionLabel>{t('basicInformation')}</SectionLabel>
          <Row>
            <InputWrapper isMobile={isMobile}>
              <TextField
                data-cy="mandatory"
                placeholder={t('firstnamePlaceholder')}
                newDriver
                error={firstname.error}
                onFocus={() =>
                  setFirstName({
                    ...firstname,
                    error: false,
                  })
                }
                label={t('firstname')}
                value={firstname.value}
                onChange={(e) =>
                  setFirstName({
                    ...firstname,
                    value: e.target.value,
                  })
                }
                icon={!evDriverData?.firstname}
              />
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper isMobile={isMobile}>
              <TextField
                data-cy="mandatory"
                placeholder={t('lastnamePlaceholder')}
                newDriver
                error={lastname.error}
                onFocus={() =>
                  setLastname({
                    ...lastname,
                    error: false,
                  })
                }
                label={t('lastname')}
                value={lastname.value}
                onChange={(e) =>
                  setLastname({
                    ...lastname,
                    value: e.target.value,
                  })
                }
                icon={!evDriverData?.lastname}
              />
            </InputWrapper>
          </Row>
        </EditWrapper>
      </Grid>

      <Grid justifyContent="center" display="flex" item xs={0} md={1}>
        {!isMobile && (
          <Divider
            style={{
              height: '80%',
            }}
            variant="middle"
            orientation="vertical"
          />
        )}
      </Grid>
      <Grid item xs={12} md={5.5}>
        <div>
          <SectionLabel
            style={{ marginBottom: '18px', marginTop: isMobile ? '18px' : '' }}
          >
            {t('myAccount')}
          </SectionLabel>
          <Row>
            <InputWrapper>
              <Description>
                {ta('loginEmail')}
                <EditButton onClick={() => checkChanges('email')} />
              </Description>
              <StaticValue>{email}</StaticValue>
              {changeValid && (
                <>
                  <Description>{ta('newLoginEmail')}</Description>
                  <StaticValue red>{newMail}</StaticValue>
                  <Description>{ta('mailConfirmAction')}</Description>
                </>
              )}
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper>
              <Description>
                {ta('password')}
                <EditButton onClick={() => checkChanges('password')} />
              </Description>
              <StaticValue>********</StaticValue>
            </InputWrapper>
          </Row>
          <SectionLabel>
            {t('bankData')}
            {bankingUpdateRequested && <StyledUpdateIcon />}
          </SectionLabel>
          <Row>
            <InputWrapper>
              <Description>
                {t('iban')}
                <EditButton onClick={() => navigate('/bankData')} />
              </Description>
              <StaticValue>
                {`**** **** **** **** **${evDriverData?.bankData?.iban?.old
                  ?.toString()
                  .slice(0, 2)} ${evDriverData?.bankData?.iban?.old
                  ?.toString()
                  .slice(2)}`}
              </StaticValue>
            </InputWrapper>
          </Row>
        </div>
      </Grid>
    </Grid>
  );
};

export default PersonalInfo;
