import { useState } from 'react';
import { withStyles, makeStyles } from '@material-ui/core/styles';
import { Tooltip } from '@material-ui/core';
import PropTypes from 'prop-types';
import { isMobile } from 'react-device-detect';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import TagManager from 'react-gtm-module';

const useStyles = makeStyles({
  chip: {
    fontFamily: 'Arial, Helvetica, sans-serif',
    marginLeft: '0.5rem',
    background: '#ddd',
    color: '#666',
    borderRadius: '50%',
    display: 'inline-block',
    textAlign: 'center',
    fontSize: '19px',
    lineHeight: 1.16,
    width: 21,
    height: 21,
    backgroundColor: '#c4c4c5',
    cursor: 'pointer',
  },
  smallChip: {
    fontFamily: 'Arial, Helvetica, sans-serif',
    marginLeft: '0.25rem',
    marginBottom: '1px',
    background: '#ddd',
    color: 'white',
    borderRadius: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: !isMobile ? '11px' : '14px',
    width: !isMobile ? 14 : 20,
    height: !isMobile ? 14 : 20,
    backgroundColor: '#666666',
    cursor: 'pointer',
  },
  mobileChip: {
    fontFamily: 'Arial, Helvetica, sans-serif',
    marginLeft: '0.25rem',
    marginBottom: '1px',
    background: '#ddd',
    color: 'white',
    borderRadius: '50%',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    fontSize: '16px',
    width: 35,
    height: 35,
    zIndex: 1,
    backgroundColor: '#666666',
    cursor: 'pointer',
  },
});

const getCustomTooltip = ({ maxWidth }) =>
  withStyles({
    tooltip: {
      borderRadius: '8px',
      boxShadow: '0 0 40px 0 rgba(0, 0, 0, 0.16)',
      backgroundColor: 'var(--default-text)',
      whiteSpace: 'pre-wrap',
      fontSize: '14px',
      padding: '1rem',
      maxWidth: isMobile ? '80vw' : maxWidth || '300px',
    },
  })(Tooltip);

const QuestionMark = ({ tooltip, small, mobile }) => {
  const classes = useStyles();
  const CustomTooltip = getCustomTooltip(tooltip);
  const [open, setOpen] = useState(false);

  const show = () => {
    if (tooltip?.type && !open) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'tooltip_interaction',
          tooltip: {
            name: `tooltip_reimbursement_${tooltip.type}`,
            interaction_type: 'open',
          },
        },
      });
    } else {
      if (!open) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'tooltip_interaction',
            tooltip: {
              name: `tooltip_reimbursement_tariff`,
              interaction_type: 'open',
            },
          },
        });
      }
    }
    setOpen(true);
  };

  const hide = () => {
    if (tooltip?.type && open) {
      TagManager.dataLayer({
        dataLayer: {
          event: 'tooltip_interaction',
          tooltip: {
            name: `tooltip_reimbursement_${tooltip.type}`,
            interaction_type: 'close',
          },
        },
      });
    } else {
      if (open) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'tooltip_interaction',
            tooltip: {
              name: `tooltip_reimbursement_tariff`,
              interaction_type: 'close',
            },
          },
        });
      }
    }
    setOpen(false);
  };

  return (
    <ClickAwayListener onClickAway={hide}>
      <CustomTooltip
        disableHoverListener
        onMouseEnter={() => show()}
        onClick={(e) => {
          e.stopPropagation();
          show();
        }}
        data-cy="tooltip"
        onMouseLeave={() => hide()}
        open={open || false}
        title={tooltip.content}
        placement={tooltip.placement}
      >
        <div
          className={
            mobile
              ? classes.mobileChip
              : small
              ? classes.smallChip
              : classes.chip
          }
        >
          ?
        </div>
      </CustomTooltip>
    </ClickAwayListener>
  );
};

QuestionMark.defaultProps = {
  tooltip: {},
  small: false,
  mobile: false,
};

QuestionMark.propTypes = {
  tooltip: PropTypes.shape({
    content: PropTypes.oneOfType([PropTypes.string, PropTypes.element]),
    small: PropTypes.bool,
    mobile: PropTypes.bool,
    placement: PropTypes.string,
  }),
  small: PropTypes.bool,
};

export default QuestionMark;
