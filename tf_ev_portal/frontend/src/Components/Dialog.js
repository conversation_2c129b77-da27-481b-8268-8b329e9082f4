import Dialog from '@material-ui/core/Dialog';
import { makeStyles } from '@material-ui/styles';
import { Paper } from '@material-ui/core';
import PropTypes from 'prop-types';

const useStyles = makeStyles({
  paper: {
    borderRadius: '10px !important',
    maxWidth: 'none',
  },
  root: {
    maxWidth: 'none',
    '&.MuiDialog-paperWidthSm': {
      maxWidth: 'none',
    },
  },
});

const Popup = ({ children, ...rest }) => {
  const classes = useStyles();

  return (
    <Dialog
      classes={{
        root: classes.root,
      }}
      {...rest}
      PaperComponent={Paper}
      PaperProps={{ className: classes.paper }}
    >
      {children}
    </Dialog>
  );
};

Popup.propTypes = {
  children: PropTypes.oneOfType([
    PropTypes.arrayOf(PropTypes.node),
    PropTypes.node,
  ]).isRequired,
};

export default Popup;
