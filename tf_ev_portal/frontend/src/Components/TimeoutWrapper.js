import PropTypes from 'prop-types';
import styled from 'styled-components';

const StyledWrapper = styled.div`
  position: relative;
  height: 100%;
  width: 100%;
`;

const TimeoutWrapper = ({ children }) => {
  return <StyledWrapper>{children}</StyledWrapper>;
};

TimeoutWrapper.defaultProps = {
  visible: false,
};

TimeoutWrapper.propTypes = {
  visible: PropTypes.bool,
  text: PropTypes.string.isRequired,
};

export default TimeoutWrapper;
