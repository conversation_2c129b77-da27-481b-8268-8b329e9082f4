import { useContext, useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { evDriverContext } from '../ContextProvider';
import { isMobile } from 'react-device-detect';
import PageWrapper from '../Components/PageWrapper';
import { Error } from '../Components/evDriver/PageTemplate';
import requester from '../utils/requester';
import LanguageSelection from '../Components/LanguageSelection';
import { Box } from '@mui/material';

//Cookie Consent
import { Row } from '../Components/evDriver/PageTemplate';
import { Switch } from '@material-ui/core';
import { Button } from '../Components';
import Cookies from 'universal-cookie';
import { useNavigate } from 'react-router-dom';
import {
  StyledSwitch,
  Spacer,
  ButtonWrapper,
  Headline,
  SwitchWrapper,
  MainHeadline,
} from './CookieSettings';

const Settings = () => {
  const { t } = useTranslation('evDriver');
  const { t: ta } = useTranslation('actions');
  const { t: to } = useTranslation('onboarding');
  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [checked, setChecked] = useState(true);

  const [isLoading, setIsLoading] = useState(false);

  const cookies = useMemo(() => new Cookies(), []);
  const navigate = useNavigate();

  const { t: tCookie } = useTranslation('cookie');
  const { t: tDriver } = useTranslation('evDriver');

  const changeLangReq = async (lang) => {
    try {
      setIsErrorVisible(false);
      setIsLoading(true);
      await requester().patch(
        `/Ev_Driver_Data/Patch_Ev_Driver_Data`,
        {
          language: lang,
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      const { data } = await requester().get(
        `/Ev_Driver_Data/Get_Ev_Driver_Data`,
      );
      evDriverDispatch({
        type: 'set',
        value: data,
      });
      setIsLoading(false);
    } catch (err) {
      setIsLoading(false);
      setIsErrorVisible(true);
    }
  };

  const handleChange = () => {
    setChecked(!checked);
  };

  const handleAcceptCookie = () => {
    cookies.set('reimbursementCookie', true);
  };

  useEffect(() => {
    if (cookies.get('reimbursementCookie') === undefined) {
      setChecked(true);
    } else {
      setChecked(cookies.get('reimbursementCookie'));
    }
  }, [cookies]);

  const savePreferences = () => {
    if (checked) {
      handleAcceptCookie();
      navigate('/dashboard');
    } else {
      cookies.set('reimbursementCookie', false);
      window[`a-disable-${process.env.REACT_APP_GOOGLE_ANALYTICS_ID}`] = true;
      navigate('/dashboard');
    }
  };

  return (
    <PageWrapper backButton title={t('settings')} isMobile={isMobile}>
      <Box sx={{ maxWidth: '330px' }}>
        <Box mb="1em">
          <Box>
            <p>
              <b>
                {to('language')}
                {': '}
              </b>
            </p>
          </Box>
          <Box>
            <LanguageSelection
              isLoading={isLoading}
              language={evDriverData?.language?.new}
              changeLanguage={changeLangReq}
            />
          </Box>
          {isErrorVisible && <Error>{ta('generalRequestError')}</Error>}
        </Box>
      </Box>
      <Spacer />
      <p>{tCookie('intro')}</p>
      <MainHeadline>{tCookie('headline')}</MainHeadline>
      <Row>
        <Headline>{tCookie('functionalCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <Switch
            checked={true}
            disabled
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{tCookie('functionalCookiesText')}</p>
      <Spacer />
      <Row>
        <Headline>{tCookie('performanceCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <StyledSwitch
            checked={checked}
            onChange={handleChange}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{tCookie('performanceCookiesText')}</p>
      <Spacer />
      <ButtonWrapper>
        <Button variant="primary" onClick={savePreferences}>
          {tDriver('save')}
        </Button>
      </ButtonWrapper>
      <Spacer />
      <Spacer />
    </PageWrapper>
  );
};

export default Settings;
