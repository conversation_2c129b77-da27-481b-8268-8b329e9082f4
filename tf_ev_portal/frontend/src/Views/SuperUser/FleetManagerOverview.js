import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import Center from '../../Components/helper/Center';
import logger from '../../utils/logger';
import { Button, GeneralInfo } from '../../Components';
import AddFleetManagerDialog from '../../Components/myTeam/AddFleetManagerDialog';
import LaunchIcon from '@material-ui/icons/Launch';
import { IconButton } from '@material-ui/core';
import FleetManagerActionsMenu from '../../Components/myTeam/FleetmanagerActionsMenu';
import PageWrapper from '../../Components/PageWrapper';
import {
  Row,
  RowBox,
  RowContainer,
  TitleRow,
} from '../../Components/dashboard/RowTemplates';
import {
  ButtonWrapper,
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../../Components/dashboard/DashboardTemplates';
import TimeoutWrapper from '../../Components/TimeoutWrapper';
import Timeout from '../../Components/Timeout';

const getData = async ({ setError }) => {
  try {
    const { fleetManagers } = (
      await requester().get('/authorities/fleetmanagers')
    ).data;
    return fleetManagers;
  } catch (err) {
    if (err.response.status === 504) {
      logger().error(
        `Timeout of request for /Information_Overview/card_overview_by_driver.`,
      );
      setError(true);
      return null;
    } else {
      logger().error(`Couldn't get overview data from api.\n${err.message}`);
      return [];
    }
  }
};

const FleetManagerOverview = () => {
  const { t } = useTranslation('superuser');
  const [data, setData] = useState([]);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [reimbursementSum, setReimbursementSum] = useState(0);
  const [activeFcCards, setActiveFcCards] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isFleetManagerDialogVisible, setIsFleetManagerDialogVisible] =
    useState(false);
  const [error, setError] = useState(false);
  const navigate = useNavigate();

  const fetchData = async () => {
    setIsLoading(true);
    const rspData = await getData({ setError });
    if (rspData !== null) {
      setData(rspData);
      setTotalCustomers(rspData.length);
      setReimbursementSum(
        rspData.reduce((acc, el) => acc + el.reimbursementSum, 0),
      );
      setActiveFcCards(rspData.reduce((acc, el) => acc + el.cardSum, 0));
    }
    setIsLoading(false);
  };

  useEffect(() => {
    fetchData();
  }, []);

  const infoFields = [
    { label: t('totalCustomers'), value: totalCustomers },
    { label: t('activeFcCardsSum'), value: activeFcCards },
    { label: t('reimbursementSum'), value: reimbursementSum },
  ];

  const DataRow = ({ rowData, index }) => {
    return (
      <Row>
        <RowContainer>
          <RowBox width={2.5}></RowBox>
          <RowBox width={37.5}>{rowData.eMail}</RowBox>
          <RowBox center width={10}>
            {rowData.function}
          </RowBox>
          <RowBox center width={7.5}>
            {rowData.status}
          </RowBox>
          <RowBox center width={10}>
            {rowData.cardSum}
          </RowBox>
          <RowBox center width={17.5}>
            {rowData.reimbursementSum}
          </RowBox>
          <RowBox center width={7}>
            {rowData.provider}
          </RowBox>
          <RowBox center width={4}>
            <FleetManagerActionsMenu
              email={rowData.eMail}
              fmFunction={rowData.function}
              status={rowData.status}
              onClose={fetchData}
            />
          </RowBox>
          <RowBox center width={4}>
            <IconButton
              onClick={() => {
                navigate(`/cardoverview/${rowData.IdpId}`);
              }}
            >
              <LaunchIcon />
            </IconButton>
          </RowBox>
        </RowContainer>
      </Row>
    );
  };

  return (
    <PageWrapper title={t('customerOverview')}>
      <AddFleetManagerDialog
        open={isFleetManagerDialogVisible}
        superUser
        onClose={() => {
          setIsFleetManagerDialogVisible(false);
          fetchData();
        }}
      />
      <GeneralInfo superUser="true" fields={infoFields} loading={isLoading} />
      <ButtonWrapper>
        <Button
          onClick={() => setIsFleetManagerDialogVisible(true)}
          variant="primary"
        >
          {t('addFleetManager')}
        </Button>
      </ButtonWrapper>
      <Wrapper>
        <TimeoutWrapper>
          <TableContainer>
            <TitleRow>
              <HeadlineBox>
                <RowBox width={2.5}></RowBox>
                <RowBox bold left width={37.5}>
                  {t('feetManager')}
                </RowBox>
                <RowBox center bold width={10}>
                  {t('function')}
                </RowBox>
                <RowBox center bold width={7.5}>
                  {t('status')}
                </RowBox>
                <RowBox center bold width={10}>
                  {t('numFcCard')}
                </RowBox>
                <RowBox center bold width={17.5}>
                  {t('numReimb')}
                </RowBox>
                <RowBox center bold width={7}>
                  {t('provider')}
                </RowBox>
                <RowBox center bold width={8}>
                  {t('actions')}
                </RowBox>
              </HeadlineBox>
            </TitleRow>
            {isLoading && (
              <LoadingCircleContainer>
                <CircularProgress />
              </LoadingCircleContainer>
            )}
            {!isLoading && error && <Timeout />}

            {!isLoading && !error && (
              <div>
                {data.map((row, i) => (
                  <DataRow rowData={row} key={row.IdpId} index={i} />
                ))}
              </div>
            )}
            {!isLoading && data.length === 0 && !error && (
              <Center>
                <h2>{t('noDataAvailable')}</h2>
              </Center>
            )}
          </TableContainer>
        </TimeoutWrapper>
      </Wrapper>
    </PageWrapper>
  );
};
export default FleetManagerOverview;
