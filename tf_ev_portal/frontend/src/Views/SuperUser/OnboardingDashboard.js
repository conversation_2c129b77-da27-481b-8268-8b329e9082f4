import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import DashboardFilter from '../../Components/dashboard/DashboardFilter';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import Center from '../../Components/helper/Center';
import logger from '../../utils/logger';

import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../../Components/dashboard/DashboardTemplates';
import RowFilter from '../../Components/dashboard/RowFilter';
import { RowBox, TitleRow } from '../../Components/dashboard/RowTemplates';
import OnboardingTableRow from '../../Components/dashboard/OnboardingTableRow';
import PageWrapper from '../../Components/PageWrapper';

const getData = async () => {
  try {
    const rsp = (await requester().get('/onboarding/registration')).data;

    return rsp;
  } catch (err) {
    logger().error(`Couldn't get overview data from api.\n${err.message}`);
    return {};
  }
};

const OnboardingDashboard = () => {
  const { t } = useTranslation('superuser');
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('pending');
  const [sort, setSort] = useState({
    attr: '',
    order: '',
  });
  const [cards, setCards] = useState([]);
  const [filteredCards, setFilteredCards] = useState([]);
  const [pageSize, setPageSize] = useState(
    window.innerHeight >= 1175 ? 20 : 15,
  );
  const navigate = useNavigate();

  useEffect(() => {
    const handleScroll = () => {
      const bottom =
        Math.ceil(window.innerHeight + window.scrollY) >=
        document.documentElement.scrollHeight;

      if (bottom) {
        if (cards.length > pageSize) {
          setPageSize(pageSize + 10);
        }
      }
    };
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [setPageSize, cards, pageSize]);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getData();
      if (rspData.registrations) {
        const enrichedRegistrations = rspData.registrations.map((e) => {
          e['valid'] =
            e.emailadresses?.find((e) => !e.valid) === undefined &&
            e.cards_sum > 0 &&
            e.companies
              .map(
                (company) =>
                  company.company_ids.filter((ids) => !ids.valid).length === 0,
              )
              .indexOf(false) < 0;
          return e;
        });
        setCards(enrichedRegistrations);
        setFilteredCards(
          enrichedRegistrations.filter(
            (e) => e.registration_status === 'pending',
          ),
        );
      } else {
        navigate('/error', { replace: true });
      }
      setIsLoading(false);
    };
    fetchData();
  }, [navigate]);

  return (
    <PageWrapper backButton title={t('dashboard-header')}>
      <DashboardFilter
        onboardingMode
        filter={filter}
        setFilter={setFilter}
        data={cards}
        setData={setFilteredCards}
      />

      <Wrapper>
        <TableContainer>
          <TitleRow>
            <HeadlineBox>
              <RowBox width={2.5}></RowBox>
              <RowBox bold width={22.5}>
                {t('mail')}
              </RowBox>
              <RowBox width={7.5}>
                <RowFilter
                  sortAttr="customer_type"
                  text={t('customerType')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={10}>
                <RowFilter
                  sortAttr="company_id"
                  text={t('companyId')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={5}>
                <RowFilter
                  sortAttr="cards_sum"
                  text={t('cardsSum')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={8.5}>
                <RowFilter
                  sortAttr="country_code"
                  text={t('countryCode')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={7.5}>
                <RowFilter
                  sortAttr="principle"
                  text={t('principle')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox bold center width={10}>
                <RowFilter
                  sortAttr="valid"
                  text={t('validity')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox bold center width={10}>
                <RowFilter
                  sortAttr="registration_status"
                  text={t('status')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox bold center width={11.5}>
                <RowFilter
                  sortAttr="id"
                  text={t('createdDate')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
            </HeadlineBox>
          </TitleRow>
          {!isLoading && cards.length === 0 && (
            <Center>
              <h2>{t('noDataAvailable')}</h2>
            </Center>
          )}
          {(isLoading && (
            <LoadingCircleContainer>
              <CircularProgress />
            </LoadingCircleContainer>
          )) || (
            <div>
              {filteredCards.map((row, i) => {
                if (i < pageSize) {
                  return (
                    <OnboardingTableRow rowData={row} key={`row-${row.id}`} />
                  );
                }
                return <></>;
              })}
            </div>
          )}
        </TableContainer>
      </Wrapper>
    </PageWrapper>
  );
};

export default OnboardingDashboard;
