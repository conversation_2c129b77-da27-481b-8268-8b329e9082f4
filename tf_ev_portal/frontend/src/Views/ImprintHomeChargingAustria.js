import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@material-ui/styles';
import PageWrapper from '../Components/PageWrapper';
import { isMobile } from 'react-device-detect';

const Imprint = () => {
  const useStyles = makeStyles({
    imprint: {
      '& p': {
        fontSize: '12px',
      },
      '& ul': {
        color: 'var(--default-text)',
        fontSize: '12px',
      },
      '& a': {
        color: 'var(--trafineo-rot-100)',
        fontSize: '12px',
      },
      '& h3': {
        fontWeight: 'bold',
      },
    },
  });
  const classes = useStyles();

  const { t } = useTranslation('footer');
  const TextBlock = styled.div`
    text-align: left;
    margin-bottom: 4rem;
  `;

  return (
    <PageWrapper backButton isMobile={isMobile} title={t('imprint')}>
      <TextBlock>
        <div
          className={classes.imprint}
          dangerouslySetInnerHTML={{
            __html: t('homeChargingAustriaImprint'),
          }}
        />
      </TextBlock>
    </PageWrapper>
  );
};

export default Imprint;
