import { useContext, useState, useEffect, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import JwtDecode from 'jwt-decode';
import { userContext } from '../ContextProvider';
import ChangeEmailDialog from '../Components/profile/ChangeEmailDialog';
import { isMobile } from 'react-device-detect';
import ChangePasswordDialog from '../Components/profile/ChangePasswordDialog';
import PageWrapper from '../Components/PageWrapper';
import {
  Description,
  InputWrapper,
  Row,
  StaticValue,
} from '../Components/evDriver/PageTemplate';
import { Box } from '@mui/material';
import { EditButton } from '../Components/ActionButtons';

//Cookie Consent
import { Switch } from '@material-ui/core';
import { Button } from '../Components';
import Cookies from 'universal-cookie';
import { useNavigate } from 'react-router-dom';
import {
  StyledSwitch,
  Spacer,
  ButtonWrapper,
  Headline,
  SwitchWrapper,
  MainHeadline,
} from './CookieSettings';
import TagManager from 'react-gtm-module';

const MyInformationLite = () => {
  const { t: td } = useTranslation('evDriver');
  const { t: ta } = useTranslation('actions');
  const { user } = useContext(userContext);
  const [isDialogVisible, setIsDialogVisible] = useState(false);

  const { t } = useTranslation('cookie');
  const { t: tDriver } = useTranslation('evDriver');

  const [isChangePasswordDialogVisible, setIsChangePasswordDialogVisible] =
    useState(false);

  const [checked, setChecked] = useState(true);

  const cookies = useMemo(() => new Cookies(), []);
  const navigate = useNavigate();

  let email = '';
  let newMail = '';
  let changeValid = false;

  try {
    email = JwtDecode(user.access_token).preferred_username;
    newMail = JwtDecode(user.access_token).newemail;
    const changeExpire = JwtDecode(user.access_token)['newemail-expiration'];
    changeValid = Number(changeExpire) * 1000 > Date.parse(new Date());
  } catch (e) {
    console.error(e);
  }

  const handleChange = () => {
    setChecked(!checked);
  };

  const handleAcceptCookie = () => {
    cookies.set('reimbursementCookie', true);
  };

  useEffect(() => {
    if (cookies.get('reimbursementCookie') === undefined) {
      setChecked(true);
    } else {
      setChecked(cookies.get('reimbursementCookie'));
    }
  }, [cookies]);

  const savePreferences = () => {
    if (checked) {
      handleAcceptCookie();
      navigate('/dashboard');
    } else {
      cookies.set('reimbursementCookie', false);
      window[`a-disable-${process.env.REACT_APP_GOOGLE_ANALYTICS_ID}`] = true;
      navigate('/dashboard');
    }
  };

  return (
    <PageWrapper backButton title={td('myInformation')} isMobile={isMobile}>
      <ChangeEmailDialog
        open={isDialogVisible}
        data={email}
        onClose={() => {
          TagManager.dataLayer({
            dataLayer: {
              event: 'popup_interaction',
              popup: {
                name: `popup_changeEmail`,
                interaction_type: 'close',
              },
            },
          });
          setIsDialogVisible(false);
          window.location.reload();
        }}
      />
      <ChangePasswordDialog
        open={isChangePasswordDialogVisible}
        email={email}
        onClose={() => {
          TagManager.dataLayer({
            dataLayer: {
              event: 'popup_interaction',
              popup: {
                name: `popup_changePassword`,
                interaction_type: 'close',
              },
            },
          });
          setIsChangePasswordDialogVisible(false);
        }}
      />
      <Box mt="1rem">
        <Row>
          <InputWrapper>
            <Description bold>
              {ta('loginEmail')}
              <EditButton
                onClick={() => {
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'popup_interaction',
                      popup: {
                        name: `popup_changeEmail`,
                        interaction_type: 'open',
                      },
                    },
                  });
                  setIsDialogVisible(true);
                }}
              />
            </Description>
            <StaticValue>{email}</StaticValue>
            {changeValid && (
              <>
                <Description bold>{ta('newLoginEmail')}</Description>
                <StaticValue red>{newMail}</StaticValue>
                <Description>{ta('mailConfirmAction')}</Description>
              </>
            )}
          </InputWrapper>
        </Row>
        <Row>
          <InputWrapper>
            <Description bold>
              {ta('password')}
              <EditButton
                onClick={() => {
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'popup_interaction',
                      popup: {
                        name: `popup_changeEmail`,
                        interaction_type: 'open',
                      },
                    },
                  });
                  setIsChangePasswordDialogVisible(true);
                }}
              />
            </Description>
            <StaticValue>********</StaticValue>
          </InputWrapper>
        </Row>
      </Box>
      <p>{t('intro')}</p>
      <MainHeadline>{t('headline')}</MainHeadline>
      <Row>
        <Headline>{t('functionalCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <Switch
            checked={true}
            disabled
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{t('functionalCookiesText')}</p>
      <Spacer />
      <Row>
        <Headline>{t('performanceCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <StyledSwitch
            checked={checked}
            onChange={handleChange}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{t('performanceCookiesText')}</p>
      <Spacer />
      <ButtonWrapper>
        <Button variant="primary" onClick={savePreferences}>
          {tDriver('save')}
        </Button>
      </ButtonWrapper>
      <Spacer />
      <Spacer />
      <Spacer />
    </PageWrapper>
  );
};

export default MyInformationLite;
