import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { makeStyles } from '@material-ui/styles';
import { getBranding } from '../utils/helper';
import PageWrapper from '../Components/PageWrapper';
import { isMobile } from 'react-device-detect';

const Imprint = () => {
  const useStyles = makeStyles({
    imprint: {
      '& p': {
        fontSize: '12px',
      },
      '& ul': {
        color: 'var(--default-text)',
        fontSize: '12px',
      },
      '& a': {
        color: 'var(--trafineo-rot-100)',
        fontSize: '12px',
      },
      '& h3': {
        fontWeight: 'bold',
      },
    },
  });
  const classes = useStyles();

  const { t } = useTranslation('footer');
  const TextBlock = styled.div`
    text-align: left;
    margin-bottom: 4rem;
  `;
  const BoldH3 = styled.h3`
    font-weight: bold;
  `;

  const BoldH2 = styled.h2`
    font-weight: bold;
  `;
  const StyledP = styled.p`
    white-space: pre-wrap;
  `;
  const StyledA = styled.a`
    color: var(--trafineo-rot-100);
    font-size: 12px;
    font-weight: bold;
    text-decoration: underline;
  `;

  return (
    <PageWrapper backButton isMobile={isMobile} title={t('imprint')}>
      {(getBranding() === 'trafineo' && (
        <TextBlock>
          <BoldH2>{t('responsible')}</BoldH2>
          <BoldH3>{t('trafineo')}</BoldH3>
          <StyledP>{t('trafineoAddress')}</StyledP>
          <BoldH3>{t('PhoneTitle')}</BoldH3>
          <StyledP>
            <StyledA target="_blank" href={`tel:${t('Phone')}`}>
              {t('Phone')}
            </StyledA>
          </StyledP>
          <BoldH3>{t('FaxTitle')}</BoldH3>
          <StyledP>
            <StyledA target="_blank" href={`tel:${t('Fax')}`}>
              {t('Fax')}
            </StyledA>
          </StyledP>
          <BoldH3>{t('EmailTitle')}</BoldH3>
          <StyledP>
            <StyledA target="_blank" href={`mailto:${t('Email')}`}>
              {t('Email')}
            </StyledA>
          </StyledP>
          <BoldH3>{t('InternetTitle')}</BoldH3>
          <StyledP>
            <StyledA target="_blank" href={t('Intetnet')}>
              {t('Intetnet')}
            </StyledA>
          </StyledP>
          <BoldH3>{t('RepresentitiveTitle')}</BoldH3>
          <StyledP>{t('Representitive')}</StyledP>
          <BoldH3>{t('ResponsibilityTitle')}</BoldH3>
          <StyledP>{t('Responsibility')}</StyledP>
          <BoldH3>{t('LegaleTitle')}</BoldH3>
          <StyledP>{t('Legal')}</StyledP>
          <BoldH3>{t('CountriesTitle')}</BoldH3>
          <StyledP>{t('Countries')}</StyledP>
        </TextBlock>
      )) || (
        <TextBlock>
          <div
            className={classes.imprint}
            dangerouslySetInnerHTML={{
              __html: t('bpAralImprint'),
            }}
          />
        </TextBlock>
      )}
    </PageWrapper>
  );
};

export default Imprint;
