import { useState, useContext, useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import { Md5 } from 'ts-md5/dist/md5';
import ExpandMoreIcon from '@material-ui/icons/ExpandMore';
import { Accordion, AccordionDetails, AccordionSummary } from '@mui/material';
import { Button, TextField } from '../Components';
import { userContext } from '../ContextProvider';
import { isMobile } from 'react-device-detect';
import PageWrapper from '../Components/PageWrapper';
import ArrowForwardIcon from '@material-ui/icons/ArrowForward';
import faq from '../utils/faq';
import UploadField from '../Components/UploadField';
import requester from '../utils/requester';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../Components/evDriver/PageTemplate';
import TextArea from '../Components/TextArea';
import Center from '../Components/helper/Center';
import CircularProgress from '../Components/CircularProgress';
import jwtDecode from 'jwt-decode';
import SuccessDialog from '../Components/SuccessDialog';
import MultiFileDisplay from '../Components/MultiFileDisplay';
import Obfuscate from 'react-obfuscate';

const StyledAccordion = styled(Accordion)`
  border-radius: 0;
  ${(props) => props.modified && 'color: var(--trafineo-rot-100) !important'};
  box-shadow: none !important;
  background-color: #f2f2f2 !important;
  :nth-child(even) {
    background-color: #fbfafa !important;
  }
  ${(props) => props.singleTariff && 'background-color: white !important;'};
  ::before {
    display: none;
  }
`;
const StyledAccordionSummary = styled(AccordionSummary)`
  width: 100%;
  padding: 0 1rem;
  & .MuiAccordionSummary-content {
    font-size: 14px;
    line-height: 24px;
    margin: 1rem 0 !important;
    align-items: center;
    font-weight: bold;
  }
`;
const StyledAccordionDetails = styled(AccordionDetails)`
  display: flex;
  flex-direction: column;
  font-size: 14px;
  line-height: 24px;
  padding: 0 1rem 1rem 1rem;
`;

const Col = styled.div`
  display: flex;
  flex-direction: column;
  width: ${(props) => `${props.width}%`};
  box-sizing: border-box;
`;

const Wrapper = styled.div`
  display: flex;
  margin-top: 1rem;
`;

const SearchWrapper = styled.div`
  width: ${isMobile ? '100%' : '33%'};
`;

const SectionRoot = styled.div`
  width: 100%;
  margin: 1.5rem 0;
`;
const SupportWrapper = styled.div`
  margin: 2rem 1rem;
  font-size: 14px;
`;
const SupportButton = styled.button`
  color: var(--trafineo-rot-100);
  font-size: 16px;
  color: var(--trafineo-rot-100);
  font-size: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  background: no-repeat;
  border: navajowhite;
  margin: 0;
  padding: 0;
  text-decoration: underline;
  cursor: pointer;
  :hover {
    opacity: 0.6;
  }
`;
const SectionHeader = styled.div`
  display: flex;
  align-items: center;
  padding: 1rem;
  color: white;
  background: var(--trafineo-rot-100);
  font-size: 16px;
  font-weight: bold;
`;

const Faq = () => {
  const [searchInput, setSearchInput] = useState('');
  const [summary, setSummary] = useState('');
  const [description, setDescription] = useState('');
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const { lang, user } = useContext(userContext);

  let email = '';
  try {
    email = jwtDecode(user.access_token).preferred_username;
  } catch (e) {
    email = '';
  }

  const [questions, setQuestions] = useState(
    JSON.parse(JSON.stringify(faq[lang])),
  );
  const [suggestions, setSuggestions] = useState([]);

  useEffect(() => {
    setQuestions(JSON.parse(JSON.stringify(faq[lang])));
  }, [lang]);

  const { t } = useTranslation('footer');
  const tActions = useTranslation('actions').t;
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState({
    active: false,
    message: '',
  });
  const [files, setFiles] = useState([]);
  const SupportRef = useRef(null);

  const onUpload = async (tariffFile) => {
    setFiles((prevArray) => [...prevArray, tariffFile]);
  };

  const sendSupportRequest = async () => {
    const processHash = Md5.hashStr(new Date().getTime() + email);
    let requestCount = 0;

    setIsLoading(true);
    const finalize = async () => {
      try {
        await requester()({
          method: 'post',
          url: '/SendSupportRequest',
          data: {
            customer_summary: summary,
            customer_body: description,
          },
          headers: {
            'Content-Type': 'application/json',
            portal_process_id: processHash,
          },
        });
        setIsDialogVisible(true);
      } catch (err) {
        setError({
          active: true,
          message: tActions('generalRequestError'),
        });
        setIsLoading(false);
      }
    };
    const uploadFile = async (file) => {
      try {
        var bodyFormData = new FormData();
        bodyFormData.append('file', file);
        bodyFormData.append('upload_type_id', 1);
        bodyFormData.append('portal_process_id', processHash);
        bodyFormData.append('filename', encodeURIComponent(file.name));
        await requester()({
          method: 'post',
          url: '/FileUpload',
          data: bodyFormData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        requestCount = requestCount + 1;
        if (requestCount === files.length) {
          finalize();
        } else {
          uploadFile(files[requestCount]);
        }
      } catch (err) {
        setError({
          active: true,
          message: tActions(
            err.response && err.response.status === 406
              ? 'infectedFileError'
              : 'generalRequestError',
          ),
        });
        setIsLoading(false);
      }
    };
    if (files.length > 0) {
      uploadFile(files[0]);
    } else {
      finalize();
    }
  };

  return (
    <PageWrapper isMobile={isMobile} backButton title={t('faq')}>
      {isDialogVisible && (
        <SuccessDialog
          isVisible={isDialogVisible}
          onClose={() => window.location.reload()}
          text={t('supportRequestSuccess')}
        />
      )}
      <Wrapper>
        {!isMobile && user && (
          <Col width={30} style={{ marginRight: '1rem' }}>
            <Row style={{ fontWeight: 'bold' }}>{t('faqHelpHeader')}</Row>
            <Row>{t('faqHelpText')}</Row>
            <Row>
              <SupportButton
                onClick={() => SupportRef.current.scrollIntoView()}
              >
                {t('sendATicketRequest')}
                <ArrowForwardIcon />
              </SupportButton>
            </Row>
          </Col>
        )}

        <Col width={isMobile || !user ? 100 : 60}>
          <SearchWrapper>
            <TextField
              search
              placeholder={t('searchPlaceholder')}
              value={searchInput}
              type="text"
              onChange={(e) => {
                const input = e.target.value;
                setSearchInput(input);
                const filteredQuestions = JSON.parse(
                  JSON.stringify(faq[lang]),
                ).map((categories) => {
                  return Object.assign(categories, {
                    questions: categories.questions.filter(
                      (question) =>
                        question.question
                          .toLowerCase()
                          .indexOf(input.trim().toLowerCase()) !== -1 ||
                        question.answer
                          .toLowerCase()
                          .indexOf(input.trim().toLowerCase()) !== -1,
                    ),
                  });
                });
                setQuestions(filteredQuestions);
              }}
            />
          </SearchWrapper>

          {questions.map(
            (category, i) =>
              category.questions.length > 0 && (
                <SectionRoot key={i}>
                  <SectionHeader>{category.title}</SectionHeader>
                  {category.questions.map((question, inx) => (
                    <StyledAccordion key={inx} disableGutters square>
                      <StyledAccordionSummary expandIcon={<ExpandMoreIcon />}>
                        {question.question}
                      </StyledAccordionSummary>
                      <StyledAccordionDetails
                        dangerouslySetInnerHTML={{
                          __html: question.answer,
                        }}
                      />
                    </StyledAccordion>
                  ))}
                </SectionRoot>
              ),
          )}
          <SectionRoot ref={SupportRef}>
            <SectionHeader>
              {t(user ? 'SupportHeadlineLoggedIn' : 'SupportHeadline')}
            </SectionHeader>
            {user ? (
              <SupportWrapper>
                <Row style={{ flexDirection: 'column' }}>
                  <InputWrapper fullWidth>
                    <Description style={{ fontWeight: 'bold' }}>
                      {t('summary')}
                    </Description>
                    <TextField
                      style={{ width: '100%' }}
                      newDriver
                      placeholder={t('summaryPlaceholder')}
                      value={summary}
                      onChange={(e) => {
                        const input = e.target.value;
                        setSummary(input);
                        if (input.length > 3) {
                          const filteredQuestions = JSON.parse(
                            JSON.stringify(faq[lang]),
                          ).map((categories) => {
                            return Object.assign(categories, {
                              questions: categories.questions.filter(
                                (question) =>
                                  question.question
                                    .toLowerCase()
                                    .indexOf(input.trim().toLowerCase()) !==
                                    -1 ||
                                  question.answer
                                    .toLowerCase()
                                    .indexOf(input.trim().toLowerCase()) !== -1,
                              ),
                            });
                          });
                          setSuggestions(filteredQuestions);
                        }
                      }}
                    />
                  </InputWrapper>
                  {summary.length > 3 &&
                    suggestions.map(
                      (category, i) =>
                        category.questions.length > 0 &&
                        category.questions.map((question, inx) => (
                          <StyledAccordion key={inx} disableGutters square>
                            <StyledAccordionSummary
                              expandIcon={<ExpandMoreIcon />}
                            >
                              {question.question}
                            </StyledAccordionSummary>
                            <StyledAccordionDetails
                              dangerouslySetInnerHTML={{
                                __html: question.answer,
                              }}
                            />
                          </StyledAccordion>
                        )),
                    )}
                </Row>

                <Row>
                  <InputWrapper fullWidth>
                    <Description style={{ fontWeight: 'bold' }}>
                      {t('desctipton')}
                    </Description>
                    <TextArea
                      value={description}
                      onChange={(e) => setDescription(e.target.value)}
                      placeholder={t('descriptionPlaceholder')}
                      rows={8}
                    ></TextArea>
                  </InputWrapper>
                </Row>
                <Row style={{ flexDirection: 'column' }}>
                  <UploadField
                    multiFile
                    fileErrorMessage={tActions('errorSizeMultiFile', {
                      fileSize: 15,
                    })}
                    fileNames={(() => {
                      const fileObj = {};
                      files.forEach((e) => {
                        fileObj[e.name] = true;
                      });
                      return fileObj;
                    })()}
                    maxFileSize={(() => {
                      const maxSize = 15728640;
                      let size = 0;
                      files.forEach((e) => {
                        size = size + e.size;
                      });
                      return maxSize - size;
                    })()}
                    fileTypes={{
                      'image/png': true,
                      'image/jpeg': true,
                      'image/heic': true,
                      'image/heif': true,
                    }}
                    onUpload={onUpload}
                  />
                  <Description style={{ marginTop: '0.5rem' }}>
                    {t('supportedFileTypes')}
                  </Description>
                  <MultiFileDisplay
                    files={files}
                    onFileDelete={(fileName) => {
                      setFiles(files.filter((el) => el.name !== fileName));
                    }}
                  />
                </Row>
                <Row style={{ justifyContent: 'center' }}>
                  {error.active && <Error>{error.message}</Error>}
                </Row>
                {isLoading ? (
                  <Center>
                    <CircularProgress />
                  </Center>
                ) : (
                  <ButtonWrapper>
                    <Button
                      disabled={summary === '' || description === ''}
                      onClick={() => {
                        sendSupportRequest();
                      }}
                      variant="primary"
                    >
                      {t('sendRequest')}
                    </Button>
                  </ButtonWrapper>
                )}
              </SupportWrapper>
            ) : (
              <SupportWrapper>
                {t('sendSupportMail')}
                <Obfuscate
                  style={{
                    fontSize: '14px',
                    paddingLeft: '0.25rem',
                    color: 'var(--trafineo-rot-100)',
                  }}
                  email="<EMAIL>"
                />
              </SupportWrapper>
            )}
          </SectionRoot>
        </Col>
      </Wrapper>
    </PageWrapper>
  );
};

export default Faq;
