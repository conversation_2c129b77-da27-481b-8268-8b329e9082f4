import { countries } from '../constants/localization';
import Vehicle from './EvDriver/Vehicle';
import Login from './Login';
import ChangePassword from './ChangePassword';
import Imprint from './Imprint';
import ImprintHomeChargingAustira from './ImprintHomeChargingAustria';
import DataPrivacyStatement from './DataPrivacyStatement';
import ForgotPassword from './ForgotPassword';
import ChangeEmail from './ChangeEmail';
import Error from './Error';
import Overviews from './Overviews';
import PayoutOverview from './PayoutOverview';
import Settings from './Settings';
import Dashboard from './DirectCustomer/Dashboard';
import Welcome from './EvDriver/Welcome';
import ElectricityTariff from './EvDriver/ElectricityTariff';
import BankData from './EvDriver/BankData';
import OnboardingSummary from './EvDriver/OnboardingSummary';
import Waiting from './EvDriver/Waiting';
import AcceptDps from './AcceptDps';
import FleetManagerOverview from './SuperUser/FleetManagerOverview';
import RapydMock from './EvDriver/RapydMock';
import HomeChargingAustria from './HomeChargingAustria';
import FleetManagerOnboarding from './FleetManagerOnboarding';
import MyTeam from './MyTeam';
import OnboardingDashboard from './SuperUser/OnboardingDashboard';
import ChangeTariff from './EvDriver/ChangeTariff';
import CenteredCircularProgress from '../Components/helper/CenteredCircularProgress';
import ChargingSessions from './ChargingSessions';
import DriverOnboarding from './DriverOnboarding';
import SignUpType from './SignUpType';
import DriverDashboard from './EvDriver/Dashboard/DriverDashboard';
import UserDetails from './DirectCustomer/UserDetails';
import MyInformation from './EvDriver/MyInformation';
import MyInformationLite from './MyInformationLite';
import SalesDahsboard from './SalesDashboard';
import CookieSettings from './CookieSettings';
import IndirectDriverOnboarding from './IndirectDriverOnboarding';
import LeasingManagerOnboarding from './LeasingManagerOnboarding';
import SustainabilityOverview from './SustainabilityOverview';

export const baseViews = {
  '/': {
    view: CenteredCircularProgress,
    navElement: { type: 'none' },
  },
  '/login': {
    view: Login,
    navElement: { type: 'none' },
  },
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/changePassword': {
    view: ChangePassword,
    navElement: { type: 'none' },
  },
  '/imprint': {
    view: Imprint,
    navElement: { type: 'prevForDriver' },
  },
  '/imprint-home-charging-austria': {
    view: ImprintHomeChargingAustira,
    navElement: { type: 'prevForDriver' },
  },
  '/forgotPassword': {
    view: ForgotPassword,
    navElement: { type: 'none' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/error': {
    view: Error,
    navElement: { type: 'prev', value: { default: 'back' } },
  },
  '/cookie-settings': {
    view: CookieSettings,
    navElement: { type: 'none' },
  },
  '/home-charging-austria-error': {
    view: Error,
    disableHome: true,
  },
  '/home-charging-austria': {
    view: HomeChargingAustria,
    disableHome: true,
  },
  '/onboarding': {
    view: FleetManagerOnboarding,
    navElement: { type: 'none' },
  },
  '/leasing': {
    view: LeasingManagerOnboarding,
    navElement: { type: 'none' },
  },
  '/signUp': {
    view: DriverOnboarding,
    navElement: { type: 'none' },
  },
  '/signUp-type': {
    view: SignUpType,
    navElement: { type: 'none' },
  },
  '/indirectDriverOnboarding': {
    view: IndirectDriverOnboarding,
    navElement: { type: 'none' },
  },
};

export const fleetManagerViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': { view: Dashboard },
  '/myinformation': {
    view: MyInformationLite,
    navElement: { type: 'prevForDriver' },
  },
  '/myteam': {
    view: MyTeam,
    navElement: { type: 'prevForDriver' },
  },
  '/payouts/:id': {
    view: PayoutOverview,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/payouts': {
    view: PayoutOverview,
    navElement: { type: 'prevForDriver' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/userdetails/:from/:ev_driver_idp_id': {
    view: UserDetails,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/reimbursements/:id': {
    view: Overviews,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/charging_sessions/:id': {
    view: ChargingSessions,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/sustainability': {
    view: SustainabilityOverview,
    navElement: { type: 'prev', value: { default: '/' } },
  },
};

export const driverWelcomeViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: Welcome,
    navElement: { type: 'none' },
    props: { isViewInitial: true },
  },
  '/electricityTariff': {
    view: ElectricityTariff,
    navElement: { type: 'prev', value: { default: 'back' } },
    props: { isViewInitial: true },
  },
  '/vehicle': {
    view: Vehicle,
    navElement: { type: 'prev', value: { default: 'back' } },
    props: { isViewInitial: true },
  },
  '/tariffselection': {
    view: ElectricityTariff,
    navElement: { type: 'prev', value: { default: 'back' } },
    props: { isViewInitial: true },
  },
  '/bankData': {
    view: BankData,
    navElement: {
      type: 'prev',
      value: { default: '/electricityTariff', [countries.nl]: '/evseId' },
    },
    props: { isViewInitial: true },
  },
  '/summary': {
    view: OnboardingSummary,
    navElement: { type: 'prev', value: { default: '/bankData' } },
    props: { isViewInitial: true },
  },
  '/rapydMock': {
    view: RapydMock,
    navElement: { type: 'prev', value: { default: '/bankData' } },
  },
  '/myinformation': {
    view: MyInformationLite,
    navElement: { type: 'prevForDriver' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/reimbursements': {
    view: Overviews,
    navElement: { type: 'prevForDriver' },
  },
  '/payouts': {
    view: PayoutOverview,
    navElement: { type: 'prevForDriver' },
  },
  '/charging_sessions': {
    view: ChargingSessions,
    navElement: { type: 'prevForDriver' },
  },
};

export const driverInitialDataEnteredViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: Waiting,
    navElement: { type: 'none' },
  },
  '/myinformation': {
    view: MyInformationLite,
    navElement: { type: 'prevForDriver' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/payouts': {
    view: PayoutOverview,
    navElement: { type: 'prevForDriver' },
  },
  '/reimbursements': {
    view: Overviews,
    navElement: { type: 'prevForDriver' },
  },
  '/charging_sessions': {
    view: ChargingSessions,
    navElement: { type: 'prevForDriver' },
  },
  '/settings': {
    view: Settings,
    navElement: { type: 'prevForDriver' },
  },
};

export const driverModifyViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/myinformation/:tab': {
    view: MyInformation,
    navElement: { type: 'none' },
    props: { isViewInitial: true },
  },
  '/myinformation/:tab/:edit': {
    view: MyInformation,
    navElement: { type: 'none' },
    props: { isViewInitial: true },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: DriverDashboard,
    navElement: { type: 'none' },
  },
  '/electricityTariff': {
    view: ElectricityTariff,
    navElement: { type: 'prev', value: { default: 'back' } },
  },
  '/tariffselection': {
    view: ElectricityTariff,
    navElement: { type: 'prev', value: { default: 'back' } },
  },
  '/bankData': {
    view: BankData,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/rapydMock': {
    view: RapydMock,
    navElement: { type: 'prev', value: { default: '/bankData' } },
  },
  '/myinformation': {
    view: MyInformation,
    navElement: { type: 'prevForDriver' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/payouts': {
    view: PayoutOverview,
    navElement: { type: 'prevForDriver' },
  },
  '/reimbursements': {
    view: Overviews,
    navElement: { type: 'prevForDriver' },
  },
  '/charging_sessions': {
    view: ChargingSessions,
    navElement: { type: 'prevForDriver' },
  },
  '/changeTariff': {
    view: ChangeTariff,
    navElement: { type: 'prevForDriver' },
  },
  '/settings': {
    view: Settings,
    navElement: { type: 'prevForDriver' },
  },
};

export const salesAdminViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: SalesDahsboard,
    navElement: { type: 'none' },
  },

  '/myinformation': {
    view: MyInformationLite,
    navElement: { type: 'prevForDriver' },
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
  },
  '/myteam': {
    view: MyTeam,
    navElement: { type: 'prevForDriver' },
  },
};

export const superUserViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: FleetManagerOverview,
    superUser: true,
  },
  '/userdetails/:from/:fleetManagerId/:ev_driver_idp_id': {
    view: UserDetails,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/myinformation': {
    view: MyInformationLite,
    superUser: true,
  },
  '/cardoverview/:id': {
    view: Dashboard,
    superUser: true,
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
    superUser: true,
  },
  '/reimbursements/:id': {
    view: Overviews,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
  '/charging_sessions/:id': {
    view: ChargingSessions,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
  '/home-charging': {
    view: Overviews,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
  '/onboarding-dashboard': {
    view: OnboardingDashboard,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
};

export const supportViews = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/myinformation': {
    view: MyInformationLite,
    superUser: true,
  },
  '/myteam': {
    view: MyTeam,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: FleetManagerOverview,
    superUser: true,
  },
  '/userdetails/:from/:fleetManagerId/:ev_driver_idp_id': {
    view: UserDetails,
    navElement: { type: 'prev', value: { default: '/' } },
  },
  '/cardoverview/:id': {
    view: Dashboard,
    superUser: true,
  },
  '/changeEmail': {
    view: ChangeEmail,
    navElement: { type: 'prevForDriver' },
    superUser: true,
  },
  '/reimbursements/:id': {
    view: Overviews,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
  '/charging_sessions/:id': {
    view: ChargingSessions,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
  '/onboarding-dashboard': {
    view: OnboardingDashboard,
    navElement: { type: 'prev', value: { default: 'back' } },
    superUser: true,
  },
};

export const acceptDps = {
  '/data-privacy-statement': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/terms-and-conditions': {
    view: DataPrivacyStatement,
    navElement: { type: 'prevForDriver' },
  },
  '/': {
    view: AcceptDps,
  },
};

export const error = {
  '/': {
    view: Error,
  },
};
