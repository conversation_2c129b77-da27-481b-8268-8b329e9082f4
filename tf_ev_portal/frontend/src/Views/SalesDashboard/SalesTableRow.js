/* eslint-disable react/jsx-curly-newline */
import { useTranslation } from 'react-i18next';
import { useState, Fragment } from 'react';
import styled from 'styled-components';
import { IconButton } from '@material-ui/core';
import CheckIcon from '@material-ui/icons/Check';
import CloseIcon from '@material-ui/icons/Close';

import KeyboardArrowDown from '@material-ui/icons/KeyboardArrowDown';
import KeyboardArrowUp from '@material-ui/icons/KeyboardArrowUp';

import {
  InfoBox,
  Row,
  RowBox,
  RowContainer,
  StatusIndicator,
} from '../../Components/dashboard/RowTemplates';
import ApproveDialog from '../../Components/dashboard/ApproveDialog';
import { formatDateTime } from '../../utils/helper';

import AccountCircleIcon from '@mui/icons-material/AccountCircle';
import DirectionsCarIcon from '@mui/icons-material/DirectionsCar';
import GroupsIcon from '@mui/icons-material/Groups';
import FolderOpenIcon from '@mui/icons-material/FolderOpen';

import LeasingCompanies from '../LeasingManagerOnboarding/LeasingCompanies';

const RowExpandContainer = styled.div`
  display: flex;
  font-size: 14px;
  width: 100%;
  flex-grow: 2;
  background-color: #fafafa;
  position: relative;
  z-index: 0;
  box-shadow: 0 0px 2px 0 rgba(0, 0, 0, 0.25);
`;

const ActionButtonContainer = styled.div`
  display: flex;
  width: 100%;
  justify-content: center;
`;

const Green = styled.div`
  svg {
    color: #99cc00;
  }
`;
const Red = styled.div`
  svg {
    color: red;
  }
`;

const InfoHeadline = styled.div`
  margin: 0.75rem 0 !important;
  display: inline-flex;
`;
const InfoLabel = styled.span`
  font-weight: bold;
`;
const InfoRow = styled.div`
  margin: 0 0 0.75rem 0 !important;
  color: ${(props) => (props.invalid ? 'red' : 'var(--default-text);')};
`;
const Spacer = styled.div`
  height: 10px;
`;

const HeadlineText = styled.p`
  padding-left: 2px;
  color: black;
  font-weight: bold;
  margin-top: 5px;
`;

const ContactWrapper = styled.div`
  padding-left: 12px;
  background-color: aliceblue;
  margin: 0 !important;
  padding-right: 12px;
`;

const LastStatus = styled.div`
  color: ${(props) => (props.rejected ? 'red' : 'green')};
`;

const SalesTableRow = ({ rowData }) => {
  const { t: tSuperUser } = useTranslation('superuser');
  const { t: tHomeCharging } = useTranslation('homeCharging');
  const { t: tDirect } = useTranslation('directCustomer');
  const { t } = useTranslation('onboarding');
  const [expanded, setExpanded] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [action, setAction] = useState(null);

  const {
    id,
    country_code,
    companies,
    customer_type,
    cards_sum,
    registration_status,
    emailadresses,
    created_date,
    contact,
    valid,
  } = rowData;

  const CompanyDisplay = () => {
    return (
      <>
        <InfoHeadline>
          <DirectionsCarIcon />
          <HeadlineText>{t('leasingCompanyIds')}</HeadlineText>
        </InfoHeadline>
        {companies.map((company, index) => {
          const matchingCompany = LeasingCompanies().find(
            (e) => e.id === company.leasingcompany_id,
          );
          return (
            <Fragment key={company + index}>
              <InfoRow>
                <InfoLabel>
                  {t('company')} {index + 1}:{' '}
                </InfoLabel>
              </InfoRow>
              <InfoRow>{matchingCompany.name}</InfoRow>
              {company.company_ids.map((id) => {
                return (
                  <InfoRow key={id.company_id} invalid={!id.valid}>
                    {id.company_id}
                  </InfoRow>
                );
              })}
              <Spacer />
            </Fragment>
          );
        })}
      </>
    );
  };

  const ContactInformation = () => {
    return (
      <ContactWrapper>
        <InfoHeadline>
          <AccountCircleIcon />
          <HeadlineText>
            {tHomeCharging('contactInfo')}
            {':'}
          </HeadlineText>
        </InfoHeadline>
        <InfoRow>
          <InfoLabel>{t('firstName')}: </InfoLabel>
        </InfoRow>
        <InfoRow>{contact.firstname || '-'}</InfoRow>
        <Spacer />
        <InfoRow>
          <InfoLabel>{t('lastName')}: </InfoLabel>
        </InfoRow>
        <InfoRow>{contact.lastname || '-'}</InfoRow>
        <Spacer />
        <InfoRow>
          <InfoLabel>{t('corporateMail')}: </InfoLabel>
        </InfoRow>
        <InfoRow> {contact.email || '-'}</InfoRow>
        <Spacer />
        <InfoRow>
          <InfoLabel>{tHomeCharging('phone')}: </InfoLabel>
        </InfoRow>
        <InfoRow>{contact.phonenumber || '-'}</InfoRow>
        <Spacer />
        <InfoRow>
          <InfoLabel>{tSuperUser('createdDate')}: </InfoLabel>
        </InfoRow>
        <InfoRow>{formatDateTime(created_date)}</InfoRow>
        <Spacer />
        <InfoRow>
          <InfoLabel>{t('onboardedMails')}: </InfoLabel>
        </InfoRow>
        {emailadresses.map((e, index) => {
          return (
            <InfoRow key={e.email + index} invalid={!e.valid}>
              {e.email || '-'}
            </InfoRow>
          );
        })}
        <Spacer />
      </ContactWrapper>
    );
  };

  const LeasingFleetManagers = () => {
    return (
      <>
        <InfoHeadline>
          <GroupsIcon />
          <HeadlineText>{t('leasingFleetManagers')}</HeadlineText>
        </InfoHeadline>
        {emailadresses.map((email, index) => {
          return (
            <InfoRow key={email + index} invalid={!email.valid}>
              {email.email}
            </InfoRow>
          );
        })}
      </>
    );
  };

  const AccountInformation = () => {
    return (
      <>
        <InfoHeadline>
          <FolderOpenIcon />
          <HeadlineText>{t('accountInfo')}</HeadlineText>
        </InfoHeadline>

        <InfoRow>
          <InfoLabel>{t('applicationResposible')}: </InfoLabel>
        </InfoRow>
        <InfoRow>{contact.email || '-'}</InfoRow>
        <Spacer />

        <InfoRow>
          <InfoLabel>{t('lastStatus')}: </InfoLabel>
        </InfoRow>
        <InfoRow>
          {registration_status === 'pending' ? (
            '-'
          ) : (
            <LastStatus>{t(registration_status)}</LastStatus>
          )}
        </InfoRow>
        <InfoRow>{formatDateTime(created_date)}</InfoRow>
        <Spacer />
      </>
    );
  };

  const RowExpand = () => {
    return (
      <RowExpandContainer>
        <InfoBox noPadding width={1.5}></InfoBox>
        <InfoBox noPadding width={25}>
          <CompanyDisplay />
        </InfoBox>
        <InfoBox noPadding width={25}>
          <LeasingFleetManagers />
        </InfoBox>
        <InfoBox noPadding width={25}>
          <AccountInformation />
        </InfoBox>
        <InfoBox noPadding right width={25}>
          <ContactInformation />
        </InfoBox>
      </RowExpandContainer>
    );
  };

  const StatusCol = ({ status }) => {
    switch (status) {
      case 'pending':
        return (
          <ActionButtonContainer>
            <Red>
              <IconButton
                data-cy="rejectAction"
                onClick={() => {
                  setAction('reject');
                  setIsDialogVisible(true);
                }}
              >
                <CloseIcon />
              </IconButton>
            </Red>
            <Green>
              <IconButton
                data-cy="approveAction"
                onClick={() => {
                  setAction('approve');
                  setIsDialogVisible(true);
                }}
              >
                <CheckIcon />
              </IconButton>
            </Green>
          </ActionButtonContainer>
        );
      case 'approved':
        return (
          <StatusIndicator
            margin
            color="#090"
            background="rgba(153, 204, 0, 0.27)"
          >
            {tDirect('approved')}
          </StatusIndicator>
        );
      case 'rejected':
        return (
          <StatusIndicator
            margin
            color="#fe5349"
            background="rgba(255, 175, 170, 0.3)"
          >
            {tDirect('rejected')}
          </StatusIndicator>
        );
      default:
        return <div>-</div>;
    }
  };

  return (
    <Row data-cy="tableRow">
      <ApproveDialog
        open={isDialogVisible}
        onClose={() => window.location.reload()}
        email={contact.email}
        id={id}
        action={action}
        isSales
      />
      <RowContainer expanded={expanded}>
        <RowBox width={2.5}></RowBox>
        <RowBox width={15.5}>{contact.company_name}</RowBox>

        <RowBox width={10}>{tSuperUser(customer_type)}</RowBox>
        <RowBox center width={11}>
          {companies.length}
        </RowBox>
        <RowBox center width={16}>
          {cards_sum}
        </RowBox>
        <RowBox center width={11}>
          {country_code}
        </RowBox>
        <RowBox center width={11}>
          {valid ? (
            <StatusIndicator
              margin
              color="#090"
              background="rgba(153, 204, 0, 0.27)"
            >
              {t('valid')}
            </StatusIndicator>
          ) : (
            <StatusIndicator
              margin
              color="#fe5349"
              background="rgba(255, 175, 170, 0.3)"
            >
              {t('invalid')}
            </StatusIndicator>
          )}
        </RowBox>
        <RowBox center width={17}>
          <StatusCol status={registration_status} />
        </RowBox>
        <RowBox center width={5}>
          <IconButton
            data-cy="expandButton"
            onClick={() => setExpanded(!expanded)}
          >
            {expanded ? <KeyboardArrowUp /> : <KeyboardArrowDown />}
          </IconButton>
        </RowBox>
      </RowContainer>
      {expanded && <RowExpand />}
    </Row>
  );
};

export default SalesTableRow;
