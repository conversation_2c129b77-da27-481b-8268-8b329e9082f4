import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import DashboardFilter from '../../Components/dashboard/DashboardFilter';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import Center from '../../Components/helper/Center';
import logger from '../../utils/logger';

import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../../Components/dashboard/DashboardTemplates';
import RowFilter from '../../Components/dashboard/RowFilter';
import { RowBox, TitleRow } from '../../Components/dashboard/RowTemplates';
import SalesTableRow from './SalesTableRow';
import PageWrapper from '../../Components/PageWrapper';

const SalesDashboard = () => {
  const { t: tSuperUser } = useTranslation('superuser');
  const { t: tService } = useTranslation('serviceProvider');
  const { t: tDriver } = useTranslation('evDriver');
  const { t } = useTranslation('onboarding');
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState('pending');
  const [sort, setSort] = useState({
    attr: '',
    order: '',
  });
  const [cards, setCards] = useState([]);
  const [filteredCards, setFilteredCards] = useState([]);
  const [pageSize, setPageSize] = useState(10);
  const navigate = useNavigate();

  const getData = async () => {
    try {
      const rsp = await requester().get('/onboarding/registration');
      return rsp;
    } catch (err) {
      logger().error(`Couldn't get overview data from api.\n${err.message}`);
      return {};
    }
  };

  useEffect(() => {
    const handleScroll = () => {
      const bottom =
        Math.ceil(window.innerHeight + window.scrollY) >=
        document.documentElement.scrollHeight;

      if (bottom) {
        if (cards.length > pageSize) {
          setPageSize(pageSize + 10);
        }
      }
    };
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [setPageSize, cards, pageSize]);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getData();
      if (rspData.data.registrations) {
        const enrichedRegistrations = rspData.data.registrations.map((e) => {
          e['valid'] =
            e.emailadresses.find((e) => !e.valid) === undefined &&
            e.cards_sum > 0 &&
            e.companies
              .map(
                (company) =>
                  company.company_ids.filter((ids) => !ids.valid).length === 0,
              )
              .indexOf(false) < 0;
          return e;
        });
        setCards(enrichedRegistrations);
        setFilteredCards(
          enrichedRegistrations.filter(
            (e) => e.registration_status === 'pending',
          ),
        );
      } else {
        navigate('/error', { replace: true });
      }
      setIsLoading(false);
    };
    fetchData();
  }, [navigate]);

  return (
    <PageWrapper title={t('Dashboard')}>
      <DashboardFilter
        onboardingMode
        filter={filter}
        setFilter={setFilter}
        data={cards}
        setData={setFilteredCards}
      />

      <Wrapper>
        <TableContainer>
          <TitleRow>
            <HeadlineBox>
              <RowBox width={2.5}></RowBox>
              <RowBox bold width={15.5}>
                {t('companyName')}
              </RowBox>
              <RowBox width={11}>
                <RowFilter
                  sortAttr="customer_type"
                  text={t('customerType')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={11}>
                <RowFilter
                  sortAttr="company_id"
                  text={t('leasingCompanyIds')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={16}>
                <RowFilter
                  sortAttr="cards_sum"
                  text={tSuperUser('cardsSum')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox center width={11}>
                <RowFilter
                  sortAttr="country_code"
                  text={tService('countryCode')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox bold center width={11}>
                <RowFilter
                  sortAttr="valid"
                  text={tDriver('validity')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
              <RowBox bold center width={15}>
                <RowFilter
                  sortAttr="registration_status"
                  text={tSuperUser('status')}
                  sort={sort}
                  setSort={setSort}
                  data={filteredCards}
                  setData={setFilteredCards}
                />
              </RowBox>
            </HeadlineBox>
          </TitleRow>
          {!isLoading && cards.length === 0 && (
            <Center>
              <h2>{t('noDataAvailable')}</h2>
            </Center>
          )}
          {(isLoading && (
            <LoadingCircleContainer>
              <CircularProgress />
            </LoadingCircleContainer>
          )) || (
            <div>
              {filteredCards.map((row, i) => {
                if (i < pageSize) {
                  return <SalesTableRow rowData={row} key={`row-${row.id}`} />;
                }
                return <></>;
              })}
            </div>
          )}
        </TableContainer>
      </Wrapper>
    </PageWrapper>
  );
};

export default SalesDashboard;
