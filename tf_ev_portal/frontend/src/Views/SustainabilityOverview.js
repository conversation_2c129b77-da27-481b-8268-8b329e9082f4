import { useContext, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import Center from '../Components/helper/Center';
import { serviceRequest } from '../utils/requester';
import QuestionMark from '../Components/QuestionMark';
import logger from '../utils/logger';
import CircularProgress from '../Components/CircularProgress';
import { Button } from '../Components';
import PageWrapper from '../Components/PageWrapper';
import jwtDecode from 'jwt-decode';
import {
  Row,
  RowBox,
  RowContainer,
  TitleRow,
} from '../Components/dashboard/RowTemplates';
import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../Components/dashboard/DashboardTemplates';
import { userContext } from '../ContextProvider';
import { useParams } from 'react-router-dom';
import styled from 'styled-components';
import ExcelJS from 'exceljs';
import FileSaver from 'file-saver';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import { secondsToHms, formatDateTime } from '../utils/helper';
import TagManager from 'react-gtm-module';

const InfoContainer = styled.div`
  display: flex;
  flex-direction: column;
  background-color: var(--trafineo-grau-20);
  margin-top: 1rem;
`;

const InfoText = styled.div`
  display: flex;
  align-items: center;
  text-align: left;
  & p {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }
`;

const InfoIcon = styled(InfoOutlinedIcon)`
  height: 16px;
  width: 16px;
  margin-left: 0.25rem;
  align-self: baseline;
  margin-top: 14px;
`;

const SustainabilityOverview = () => {
  const { id } = useParams();

  const [data, setData] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [loadingDownload, setLoadingDownload] = useState(false);
  const [pageSize, setPageSize] = useState(2);
  const [initialDataLoaded, setInitialDataLoaded] = useState(false);
  const { t } = useTranslation('overview');
  const tDriver = useTranslation('evDriver').t;
  const tDirectCustomer = useTranslation('directCustomer').t;
  const tExport = useTranslation('sustainabilityExport').t;
  const { user } = useContext(userContext);

  const monthNames = [
    t('january'),
    t('february'),
    t('march'),
    t('april'),
    t('may'),
    t('june'),
    t('july'),
    t('august'),
    t('september'),
    t('october'),
    t('november'),
    t('december'),
  ];

  const getData = useCallback(
    async (from, to, isMore) => {
      if (isMore) {
        setIsLoadingMore(true);
      } else {
        setIsLoading(true);
      }

      try {
        const decodedUser = jwtDecode(user.access_token);
        const rsp = await serviceRequest().get(
          `chargingsessions/v1/chargingsessions/aggregated?date_from=${from}&date_to=${to}`,
          {
            headers: {
              role: decodedUser.role,
              user_id: id ? id : decodedUser.sub,
            },
          },
        );
        if (isMore) {
          setIsLoadingMore(false);
        } else {
          setIsLoading(false);
        }
        return rsp;
      } catch (err) {
        if (isMore) {
          setIsLoadingMore(false);
        } else {
          setIsLoading(false);
        }
        logger().error(`Couldn't get data from api.\n${err.message}`);
        return undefined;
      }
    },
    [user.access_token, id],
  );

  //reset scroll to prevent infinite scrolling on page change
  useEffect(() => {
    window.scrollTo(0, 0);
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      let rspData = null;
      let date = new Date();
      let firstDay = new Date(
        date.getFullYear() - pageSize,
        date.getMonth(),
        1,
      );
      let lastDay = new Date(
        date.getFullYear(),
        date.getMonth() + 1,
        0,
        23,
        59,
        59,
      );

      // Adjust for time difference
      let splitTime = lastDay.toString().split('GMT');
      let firstChar = splitTime[1].substring(0, 1);
      let timeDiff = splitTime[1].substring(1, 5);
      let actualNumber = parseFloat(firstChar + timeDiff / 100);

      firstDay.setHours(firstDay.getHours() + actualNumber);
      lastDay.setHours(lastDay.getHours() + actualNumber);

      rspData = await getData(firstDay.toISOString(), lastDay.toISOString());
      if (rspData) {
        setData(rspData.data.data);
      }
      setInitialDataLoaded(true);
    };

    fetchData();
  }, []);

  useEffect(() => {
    const fetchMoreData = async () => {
      let rspData = null;

      let date = new Date();
      date = new Date(date.getFullYear() - pageSize, date.getMonth(), 1);
      let firstDay = new Date(date.getFullYear() - 1, date.getMonth(), 1);
      let lastDay = new Date(
        date.getFullYear(),
        date.getMonth() + 1,
        0,
        23,
        59,
        59,
        999,
      );

      // Adjust for time difference
      let splitTime = lastDay.toString().split('GMT');
      let firstChar = splitTime[1].substring(0, 1);
      let timeDiff = splitTime[1].substring(1, 5);
      let actualNumber = parseFloat(firstChar + timeDiff / 100);

      firstDay.setHours(firstDay.getHours() + actualNumber);
      lastDay.setHours(lastDay.getHours() + actualNumber);

      rspData = await getData(
        firstDay.toISOString(),
        lastDay.toISOString(),
        true,
      );

      setData(data.concat(rspData.data.data));
    };

    if (
      !isLoading &&
      !isLoadingMore &&
      initialDataLoaded &&
      pageSize * 13 > data.length
    ) {
      fetchMoreData();
    }
  }, [isLoading, getData, pageSize, data, isLoadingMore, initialDataLoaded]);

  useEffect(() => {
    const handleScroll = () => {
      const bottom =
        Math.ceil(window.innerHeight + window.scrollY) >=
        document.documentElement.scrollHeight;

      if (bottom) {
        setPageSize(pageSize + 1);
      }
    };
    window.addEventListener('scroll', handleScroll, {
      passive: true,
    });

    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, [pageSize]);

  const downloadReport = async (i, id, month, year) => {
    setLoadingDownload(i);
    TagManager.dataLayer({
      dataLayer: {
        event: 'generic_event',
        generic_event: {
          name: 'drivers_charges_sustainability_download',
          category: 'driver_sustaionability',
          action: 'sustainability_download',
          label: 'drivers_charges_sustainability_download',
        },
      },
    });
    const paddedMonth = month.toString().padStart(2, '0');
    const fileName = `${paddedMonth + '_' + year + '_'}${tExport('fileName')}`;

    try {
      const decodedUser = jwtDecode(user.access_token);
      let dateFrom = new Date(Date.UTC(year, month - 1, 1, 0, 0, 0, 0));
      let dateTo = new Date(Date.UTC(year, month, 0, 23, 59, 59, 999));
      const downloadInfo = await serviceRequest().get(
        `/chargingsessions/v1/chargingsessions/?date_from=${dateFrom.toISOString()}&date_to=${dateTo.toISOString()}`,
        {
          headers: {
            role: decodedUser.role,
            user_id: decodedUser.sub,
          },
        },
      );

      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet(tExport('fileName'));

      worksheet.columns = [
        { header: tExport('email'), key: 'email', width: 35 },
        { header: tExport('cardNumber'), key: 'card_number', width: 25 },
        { header: tExport('sessionStart'), key: 'sessionStart', width: 20 },
        { header: tExport('sessionEnd'), key: 'sessionEnd', width: 25 },
        { header: tExport('chargeTime'), key: 'chargeTime', width: 25 },
        {
          header: tExport('energyConsumption'),
          key: 'energyConsumption',
          width: 25,
        },
        { header: tExport('greenEnergy'), key: 'greenEnergy', width: 15 },
        { header: tExport('emissionRate'), key: 'emissionRate', width: 25 },
        { header: tExport('emission'), key: 'emission', width: 20 },
      ];

      downloadInfo.data.data.forEach((chargingSession) => {
        worksheet.addRow({
          email: chargingSession.email,
          card_number: chargingSession.token_visual_number,
          sessionStart: formatDateTime(chargingSession.session_start),
          sessionEnd: formatDateTime(chargingSession.session_end),
          chargeTime: secondsToHms(chargingSession.total_time),
          energyConsumption: (
            Math.round(chargingSession.cdr_total_energy * 100) / 100
          )
            .toFixed(2)
            .replace('.', ','),
          greenEnergy: chargingSession.energy_mix
            ? chargingSession.energy_mix.is_green_energy === null
              ? tExport('noInfo')
              : tExport(chargingSession.energy_mix.is_green_energy)
            : tExport('noInfo'),
          emissionRate: chargingSession.energy_mix
            ? chargingSession.energy_mix.environ_impact &&
              chargingSession.energy_mix.environ_impact.length > 0
              ? (
                  Math.round(
                    chargingSession.energy_mix.environ_impact.find(
                      (e) => e.category === 'CARBON_DIOXIDE',
                    ).amount * 100,
                  ) / 100
                )
                  .toFixed(2)
                  .replace('.', ',')
              : tExport('noInfo')
            : tExport('noInfo'),
          emission: chargingSession.energy_mix
            ? chargingSession.energy_mix.environ_impact &&
              chargingSession.energy_mix.environ_impact.length > 0
              ? (
                  Math.round(
                    ((chargingSession.energy_mix.environ_impact.find(
                      (e) => e.category === 'CARBON_DIOXIDE',
                    ).amount *
                      chargingSession.cdr_total_energy) /
                      1000) *
                      100,
                  ) / 100
                )
                  .toFixed(2)
                  .replace('.', ',')
              : tExport('noInfo')
            : tExport('noInfo'),
        });
      });

      // save file
      const buffer = await workbook.xlsx.writeBuffer();
      FileSaver.saveAs(new Blob([buffer]), fileName + '.xlsx');
      setLoadingDownload(false);
    } catch {
      console.error('Error while downloading sustainabililty data');
      setLoadingDownload(false);
    }
  };

  const formatDate = (month, year) => {
    const date = monthNames[month - 1] + ' ' + year;
    return date;
  };
  return (
    <PageWrapper
      backButton
      minWidth="1230px"
      title={t('sustainabilityHeadline')}
      helpText={
        <InfoContainer>
          <InfoText>
            <InfoIcon alt="info" />
            <p>{tDriver('sustainabilityInfo')}</p>
          </InfoText>
        </InfoContainer>
      }
    >
      {(isLoading && (
        <LoadingCircleContainer>
          <CircularProgress />
        </LoadingCircleContainer>
      )) || (
        <Wrapper>
          <TableContainer>
            <TitleRow>
              <HeadlineBox>
                <RowBox bold center width={10}>
                  {t('paidDate')}
                </RowBox>
                <RowBox bold center width={25}>
                  {t('sustainabilityDataEntryRate')}
                  <QuestionMark
                    tooltip={{ content: t('sustainabilityHelpText') }}
                  />
                </RowBox>
                <RowBox bold center width={20}>
                  {t('emissions')}
                  <QuestionMark tooltip={{ content: t('emissionsHelpText') }} />
                </RowBox>
                <RowBox bold center width={20}>
                  {t('greenConsumption')}
                  <QuestionMark
                    tooltip={{ content: t('greenConsumptionHelpText') }}
                  />
                </RowBox>
                <RowBox bold center width={20}>
                  {t('totalConsumption')}
                  <QuestionMark
                    tooltip={{ content: t('totalConsumptionHelpText') }}
                  />
                </RowBox>
                <RowBox bold center width={20} />
              </HeadlineBox>
            </TitleRow>
            {(isLoading && (
              <LoadingCircleContainer>
                <CircularProgress />
              </LoadingCircleContainer>
            )) || (
              <>
                {data.length > 0 &&
                  data.map((row, i) => {
                    return (
                      <Row>
                        <RowContainer>
                          <RowBox center width={10}>
                            {formatDate(row.month, row.year)}
                          </RowBox>
                          <RowBox center width={25}>
                            {row.total_tariff_cnt > 0
                              ? row.sustainability_data_tariff_cnt > 0
                                ? (
                                    Math.round(
                                      (row.sustainability_data_tariff_cnt /
                                        row.total_tariff_cnt) *
                                        100 *
                                        100,
                                    ) / 100
                                  )
                                    .toFixed(2)
                                    .replace('.', ',')
                                : '0,00'
                              : '-'}
                          </RowBox>
                          <RowBox center width={20}>
                            {(Math.round((row.total_co2 / 100000) * 100) / 100)
                              .toFixed(2)
                              .replace('.', ',')}
                          </RowBox>
                          <RowBox center width={20}>
                            {(Math.round((row.green_energy / 1000) * 100) / 100)
                              .toFixed(2)
                              .replace('.', ',')}
                          </RowBox>
                          <RowBox center width={20}>
                            {(Math.round((row.total_energy / 1000) * 100) / 100)
                              .toFixed(2)
                              .replace('.', ',')}
                          </RowBox>
                          <RowBox center width={19}>
                            {loadingDownload === i &&
                              !row.total_tariff_cnt <= 0 && (
                                <Button
                                  disabled={
                                    row.total_tariff_cnt <= 0 ||
                                    loadingDownload ||
                                    loadingDownload === 0
                                  }
                                  index={i}
                                  variant="primary"
                                >
                                  <LoadingCircleContainer>
                                    <CircularProgress />
                                  </LoadingCircleContainer>
                                </Button>
                              )}
                            {loadingDownload !== i && (
                              <Button
                                disabled={
                                  row.total_tariff_cnt <= 0 ||
                                  loadingDownload ||
                                  loadingDownload === 0
                                }
                                index={i}
                                onClick={() => {
                                  downloadReport(
                                    i,
                                    row.id,
                                    row.month,
                                    row.year,
                                  );
                                }}
                                variant="primary"
                              >
                                {t('download')}
                              </Button>
                            )}
                          </RowBox>
                          <RowBox center width={1}></RowBox>
                        </RowContainer>
                      </Row>
                    );
                  })}
              </>
            )}
            {isLoadingMore && <CircularProgress />}
            {!isLoading && (!data || data.length === 0) && (
              <Center>
                <h2>{tDirectCustomer('noDataAvailable')}</h2>
              </Center>
            )}
          </TableContainer>
        </Wrapper>
      )}
    </PageWrapper>
  );
};

export default SustainabilityOverview;
