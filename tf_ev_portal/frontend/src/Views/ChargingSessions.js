import { useContext, useEffect, useState, useCallback } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import Center from '../Components/helper/Center';
import requester from '../utils/requester';
import logger from '../utils/logger';
import CircularProgress from '../Components/CircularProgress';
import Pagination from '../Components/dashboard/Pagination';
import PageWrapper from '../Components/PageWrapper';
import { RowBox, TitleRow } from '../Components/dashboard/RowTemplates';
import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../Components/dashboard/DashboardTemplates';
import { ButtonWrapper } from './MyTeam';
import RowFilterServer from '../Components/dashboard/RowFilterServer';
import DateField from '../Components/evDriver/DateField';
import {
  Description,
  InputWrapper,
  Placeholder,
} from '../Components/evDriver/PageTemplate';
import qs from 'querystring';

import DataRow from '../Components/DataRow';
import { IconButton } from '@mui/material';
import ClearIcon from '@material-ui/icons/Clear';
import styled from 'styled-components';
import DownloadMenu from '../Components/DownloadMenu';
import CdrTable from '../Components/CdrTable';
import { downloadExcel, formatDateTime, secondsToHms } from '../utils/helper';
import Select from '../Components/Select';
import MenuItem from '../Components/MenuItem';

import { evDriverContext } from '../ContextProvider';
import HelpIcon from '@mui/icons-material/Help';
import DialogWrapper from '../Components/DialogWrapper';
import { Status } from '../Components/DataRow';
import TagManager from 'react-gtm-module';

const HintWrapper = styled.div`
  cursor: pointer;
  padding-left: 5px;
`;

const StyledIconButton = styled(IconButton)`
  position: absolute !important;
  bottom: 5px;
  right: 0px;
`;

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
  background-color: #fff !important;
`;

const LayoutRow = styled.div`
  display: flex;
  flex-direction: row;
  width: ${(props) => (props.width ? props.width : '100%')};
  margin-bottom: ${(props) => (props.width ? '2rem' : '0')};
`;

const CardText = styled.p`
  padding-left: ${(props) => (props.isPlaceholder ? '' : '7px')};
  color: ${(props) => (props.isPlaceholder ? '#000' : '')};
`;

const HintHeader = styled.p`
  padding-left: 5px;
  font-weight: 600;
  font-size: 15px;
`;

const ReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  padding: 0;
  margin-top: 2px;
`;

const TitleRowWrapper = ({ sort, setSort }) => {
  const { t } = useTranslation('overview');
  const { t: tEvDriver } = useTranslation('evDriver');

  const [hintOpen, setHintOpen] = useState(false);

  const redReasons = [
    t('chargingSessionHintRed1'),
    t('chargingSessionHintRed2'),
    t('chargingSessionHintRed3'),
    t('chargingSessionHintRed4'),
    t('chargingSessionHintRed5'),
    t('chargingSessionHintRed6'),
  ];

  return (
    <TitleRow>
      {hintOpen && (
        <DialogWrapper
          open={hintOpen}
          onClose={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'popup_interaction',
                popup: {
                  name: `popup_charging_status`,
                  interaction_type: 'close',
                },
              },
            });
            setHintOpen(false);
          }}
          headline={t('chargingSessionStatusHeader')}
          width="800px"
        >
          <RowBox>
            <Status statusId={12}></Status>
            <HintHeader>{t('chargingSessionHintWhiteHeader')}</HintHeader>
          </RowBox>
          <RowBox>
            <p>{t('chargingSessionHintWhiteText')}</p>
          </RowBox>
          <RowBox>
            <Status statusId={1}></Status>
            <HintHeader>{t('chargingSessionHintYellowHeader')}</HintHeader>
          </RowBox>
          <RowBox>
            <p>{t('chargingSessionHintYellowText')}</p>
          </RowBox>
          <RowBox>
            <Status statusId={11}></Status>
            <HintHeader>{t('chargingSessionHintGreenHeader')}</HintHeader>
          </RowBox>
          <RowBox>
            <p>{t('chargingSessionHintGreenText')}</p>
          </RowBox>
          <RowBox>
            <Status statusId={2}></Status>
            <HintHeader>{t('chargingSessionHintRedHeader')}</HintHeader>
          </RowBox>
          <LayoutRow>
            <p>{t('chargingSessionHintRedText')}</p>
          </LayoutRow>
          {redReasons.map((translation, i) => {
            return (
              <LayoutRow>
                <ReasonText>{translation}</ReasonText>
              </LayoutRow>
            );
          })}
        </DialogWrapper>
      )}
      <HeadlineBox>
        <RowBox bold center width={7}>
          {t('status')}
          <HintWrapper
            onClick={() => {
              TagManager.dataLayer({
                dataLayer: {
                  event: 'popup_interaction',
                  popup: {
                    name: `popup_charging_status`,
                    interaction_type: 'open',
                  },
                },
              });
              setHintOpen(true);
            }}
          >
            <HelpIcon />
          </HintWrapper>
        </RowBox>
        <RowBox bold center width={16}>
          {t('cardNumber')}
        </RowBox>
        <RowBox bold center width={8}>
          {tEvDriver('validUntil')}
        </RowBox>
        <RowBox bold center width={19}>
          {t('evse')}
        </RowBox>
        <RowBox bold center width={16}>
          <RowFilterServer
            sortAttr="cdr_session_start"
            text={t('startTime')}
            sort={sort}
            setSort={setSort}
          ></RowFilterServer>
        </RowBox>
        <RowBox bold center width={16}>
          {t('stopTime')}
        </RowBox>
        <RowBox bold center width={10}>
          {t('totalEnergy')}
        </RowBox>
        <RowBox bold center width={8}>
          {t('totalTime')}
        </RowBox>
      </HeadlineBox>
    </TitleRow>
  );
};

const ChargingSessions = () => {
  const { t } = useTranslation('overview');
  const tDirectCustomer = useTranslation('directCustomer').t;
  const { t: tEvDriver } = useTranslation('evDriver');

  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { email, cards } = parsedQueryString;

  const { evDriverData } = useContext(evDriverContext);

  const pageSize = 10;
  const [data, setData] = useState([]);
  const [recordCount, setRecordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [startDate, setStartDate] = useState(null);
  const [endDate, setEndDate] = useState(null);
  const [sort, setSort] = useState({
    attr: 'cdr_session_start',
    order: 'desc',
  });
  const [currentPage, setCurrentPage] = useState(0);
  const [cardFilter, setCardFilter] = useState(null);
  const [allCards, setAllCardNumbers] = useState([]);

  const { id } = useParams();
  const superUser = id;

  useEffect(() => {
    let cardNumbers = [];

    if (!superUser) {
      evDriverData.cards.forEach((card) => {
        cardNumbers.push({
          card_number: card.card_number,
          expiry_date:
            card.card_expiry_date.split('-')[1] +
            '/' +
            card.card_expiry_date.split('-')[0].slice(-2),
        });
      });
    } else {
      cardNumbers = JSON.parse(cards);
    }

    setAllCardNumbers(cardNumbers);
  }, [evDriverData, superUser, cards]);

  const handleDstTimes = (date, isStartDate) => {
    // use getTimezoneOffset to determine if Daylight Saving Time (DST)
    const stdTimezoneOffset = (date) => {
      var jan = new Date(date.getFullYear(), 0, 1);
      var jul = new Date(date.getFullYear(), 6, 1);
      return Math.max(jan.getTimezoneOffset(), jul.getTimezoneOffset());
    };

    const isDst = (date) => {
      return date.getTimezoneOffset() < stdTimezoneOffset(date);
    };
    const pickedDateIsDst = isDst(date);
    const todayIsDst = isDst(new Date());

    if (pickedDateIsDst === todayIsDst) {
      // change nothing if both timezones are the same
      if (isStartDate) {
        setStartDate(date);
      } else {
        setEndDate(date);
      }
    } else {
      if (pickedDateIsDst) {
        if (isStartDate) {
          setStartDate(new Date(date.setHours(date.getHours() + 1)));
        } else {
          //prevent switching of days
          setEndDate(new Date(date.setHours(date.getHours())));
        }
      } else {
        if (isStartDate) {
          //prevent switching of days
          setStartDate(new Date(date.setHours(date.getHours())));
        } else {
          setEndDate(new Date(date.setHours(date.getHours() - 1)));
        }
      }
    }
  };

  const handleDateChange = (date, isStartDate) => {
    if (isStartDate) {
      const fullDay = new Date(date.setHours(0, 0, 0, 0));
      // Datepicker sets summertime depending on picked date and not current date
      handleDstTimes(fullDay, isStartDate);
    } else {
      // Datepicker sets summertime depending on picked date and not current date
      const fullDay = new Date(date.setHours(23, 59, 59, 99));
      handleDstTimes(fullDay, isStartDate);
    }
    setCurrentPage(0);
  };

  const getData = useCallback(
    async (offset, limit) => {
      setIsLoading(true);
      try {
        const rsp = await requester().get(`/chargingsessions/`, {
          params: {
            offset: offset,
            limit: limit,
            order_by: sort.attr + ':' + sort.order,
            user_idp_id: id ? id.trim() : '',
            date_from: startDate ? startDate.toISOString() : '',
            date_to: endDate ? endDate.toISOString() : '',
            cdr_type: 'home',
            token_visual_number: cardFilter ? cardFilter.card_number : '',
          },
        });
        setIsLoading(false);
        return rsp;
      } catch (err) {
        setIsLoading(false);
        logger().error(`Couldn't get data from api.\n${err.message}`);
        return undefined;
      }
    },
    [sort, id, startDate, endDate, cardFilter],
  );

  useEffect(() => {
    const fetchData = async () => {
      const paginationFrom = currentPage * pageSize;
      let rspData = null;

      rspData = await getData(
        paginationFrom === 0 ? 0 : paginationFrom,
        pageSize,
      );

      if (rspData) {
        setData(rspData.data.data);
        setRecordCount(rspData.headers['x-total-count']);
      }
    };
    fetchData();
  }, [currentPage, getData, cardFilter]);

  const HeadLine = () => {
    return `${t(
      superUser ? 'chargingSessionsForUser' : 'myChargingSessions',
    )}  ${email || ''}`;
  };

  const getDownloadData = async () => {
    try {
      const rsp = (
        await requester().get(
          `/chargingsessions/${
            id ? `?user_idp_id=${id}&cdr_type=home` : '?cdr_type=home'
          }`,
        )
      ).data?.data;

      return rsp;
    } catch (err) {
      logger().error(`Couldn't get data from api.\n${err.message}`);
      return undefined;
    }
  };

  const onExcelDownloadClick = async (data) => {
    const rows = JSON.parse(JSON.stringify(data)).map((row) => {
      const transformedRow = {
        [t('status')]: t(`reimbursementStatus${row.cdr_status_id}`),
        [t('cardNumber')]: row.token_visual_number,
        [tEvDriver('validUntil')]:
          row.expiry_date.split('-')[1] +
          '/' +
          row.expiry_date.split('-')[0].slice(-2),

        [t('evse')]: row.evse_id,
        [t('startTime')]: formatDateTime(row.session_start),
        [t('stopTime')]: formatDateTime(row.session_end),
        [t('totalEnergy')]: row.cdr_total_energy,
        [t('totalTime')]: secondsToHms(row.total_time),
      };
      return transformedRow;
    });
    downloadExcel(`charging_sessions.xlsx`, rows);
  };

  return (
    <PageWrapper backButton minWidth="1230px" title={<HeadLine />}>
      <ButtonWrapper>
        <>
          <DownloadMenu
            onExcelDownloadClick={onExcelDownloadClick}
            getDownloadData={getDownloadData}
            pdfFileName="charging_sessions.pdf"
            PdfTable={CdrTable}
            disabled={data?.length === 0}
          />
        </>
      </ButtonWrapper>

      <LayoutRow>
        <Description
          style={{
            fontWeight: 'bold',
            margin: '2rem 0 1rem 0',
            fontSize: '16px',
            width: '246px',
          }}
        >
          {tDirectCustomer('cards')}
          {':'}
        </Description>
        <Description
          style={{
            fontWeight: 'bold',
            margin: '2rem 0 1rem 0',
            fontSize: '16px',
          }}
        >
          {t('timespan')}
          {':'}
        </Description>
      </LayoutRow>
      <LayoutRow>
        <InputWrapper
          style={{ width: '230px', marginRight: '1rem', position: 'relative' }}
        >
          <Description>{tDirectCustomer('selectedCard')}</Description>
          <Select
            big
            value={cardFilter}
            displayEmpty
            renderValue={
              cardFilter === null
                ? () => <Placeholder>{tDirectCustomer('allCards')}</Placeholder>
                : () => (
                    <CardText isPlaceholder>
                      {cardFilter.card_number}
                      <i>{`  (${cardFilter.expiry_date})`}</i>
                    </CardText>
                  )
            }
          >
            <StyledMenuItem
              onClick={() => {
                setCardFilter(null);
              }}
            >
              <CardText>{tDirectCustomer('allCards')}</CardText>
            </StyledMenuItem>
            {allCards.map((entry) => (
              <StyledMenuItem
                onClick={() => {
                  setCardFilter(
                    allCards.find(
                      (card) => card.card_number === entry.card_number,
                    ),
                  );

                  setCurrentPage(0);
                }}
              >
                <CardText>
                  {entry.card_number}
                  <i>{`  (${entry.expiry_date})`}</i>
                </CardText>
              </StyledMenuItem>
            ))}
          </Select>
        </InputWrapper>

        <InputWrapper
          style={{ width: '200px', marginRight: '1rem', position: 'relative' }}
        >
          <Description>{t('from')}</Description>
          <DateField
            value={startDate}
            onChange={(e) => {
              handleDateChange(e, true);
            }}
          ></DateField>
          {startDate && (
            <StyledIconButton
              onClick={() => {
                setStartDate(null);
                setCurrentPage(0);
              }}
            >
              <ClearIcon fontSize="small" />
            </StyledIconButton>
          )}
        </InputWrapper>
        <InputWrapper style={{ width: '200px', position: 'relative' }}>
          <Description>{t('to')}</Description>
          <DateField
            value={endDate}
            onChange={(e) => handleDateChange(e, false)}
          ></DateField>
          {endDate && (
            <StyledIconButton
              onClick={() => {
                setEndDate(null);
                setCurrentPage(0);
              }}
            >
              <ClearIcon fontSize="small" />
            </StyledIconButton>
          )}
        </InputWrapper>
      </LayoutRow>

      <Wrapper>
        <TableContainer>
          <TitleRowWrapper sort={sort} setSort={setSort} />
          {(isLoading && (
            <LoadingCircleContainer>
              <CircularProgress />
            </LoadingCircleContainer>
          )) || (
            <div>
              {data &&
                data.map((row, i) => {
                  return <DataRow rowData={row} key={i} index={i} />;
                })}
            </div>
          )}
          {!isLoading &&
            (!data || recordCount === 0 || recordCount === '0') && (
              <Center>
                <h2>{tDirectCustomer('noDataAvailable')}</h2>
              </Center>
            )}
        </TableContainer>
      </Wrapper>
      {!isLoading && recordCount !== 0 && recordCount !== '0' && (
        <Center>
          <Pagination
            onChange={(pos) => setCurrentPage(pos)}
            position={currentPage}
            count={Number(recordCount)}
            pageSize={pageSize}
          />
        </Center>
      )}
    </PageWrapper>
  );
};

export default ChargingSessions;
