import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { getBranding, mailRegex, prohibitedDomains } from '../utils/helper';
import { useTranslation } from 'react-i18next';
import RadioGroup from '@material-ui/core/RadioGroup';
import Radio from '@material-ui/core/Radio';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import requester from '../utils/requester';
import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';
import AddBoxIcon from '@material-ui/icons/AddBox';
import infoIcon from '../static/img/icons/Info_Icon.svg';

import DialogWrapper from '../Components/DialogWrapper';
import bpCompanyIdDE from '../static/img/bp-companyid-DE.png';
import bpCompanyIdEN from '../static/img/bp-companyid-EN.png';
import bpCompanyIdNL from '../static/img/bp-companyid-NL.png';
import aralCompanyIdDE from '../static/img/aral-companyid-DE.png';

import { TextField, Button } from '../Components';

import { Background, Blank, Page, Row } from './HomeChargingAustria/Styles';
import { FormControl, IconButton } from '@material-ui/core';
import Select from '../Components/Select';
import CountryFlag from '../Components/evDriver/CountryFlag';
import {
  Error,
  InformationContainer,
  InformationIcon,
  InformationText,
  Placeholder,
  Description,
  StyledMenuItem,
} from '../Components/evDriver/PageTemplate';
import SuccessMessage from '../Components/SuccessMessage';
import CircularProgress from '../Components/CircularProgress';
import { Headline, Wrapper } from './DriverOnboarding';
import { isMobile } from 'react-device-detect';
import TagManager from 'react-gtm-module';

const Hint = styled.div`
  color: rgb(102, 102, 102);
  text-align: left;
  font-weight: 400;
  font-size: 12px;
  padding-top: 0.25rem;
  margin-bottom: 1rem;
`;

const FailureMessageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  a {
    color: var(--trafineo-rot-100);
    font-size: 12px;
    font-weight: bold;
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-align: left;
    margin: 0.5rem 0;
    font-size: 14px;
  }
`;

const FleetManagerMail = styled.div`
  ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  height: 48px;
`;

const ErrorCompanyId = styled.div`
  color: var(--error-color);
  display: ${(props) => (props.display ? 'flex' : 'none')};
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  height: 48px;
`;

const AddCompanyHint = styled.p`
  display: inline-block;
  font-weight: 600;
  text-align: left;
`;

const CompanyIdInputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  input {
    width: 112px;
    margin-right: 1rem;
  }
  margin-bottom: 1rem;
`;

const ColRadio = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2rem;
`;

const IconButtonWrapper = styled.div`
  display: flex;
  margin-left: 0.5rem;
  justify-content: flex-start;
  align-items: flex-end;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const ButtonContainer = styled.div`
  margin: 2rem 0 4rem 0;
  display: flex;
  width: 100%;
  justify-content: flex-end;
  button {
    margin-left: 0.5rem;
    width: fit-content;
  }
`;

const CompanyIconButtonContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  margin-bottom: -9px;
  align-items: center;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const InputWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
`;

const Spacer = styled.div`
  height: 30px;
`;

const FleetManagersWrapper = styled.div`
  width: 100%;
  display: flex;
  margin-bottom: 2rem;
  flex-direction: column;
`;
const FleetManagerMailInputWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

export const Headline2 = styled.div`
  font-size: 16px;
  text-align: left;
  color: black;
  margin: 1rem 0 2rem 0;
  line-height: 20px;
  font-weight: bold;
  white-space: pre-line;
`;

const Label = styled.div`
  color: var(--default-text);
  font-size: 14px;
  line-height: 21px;
  text-align: left;
  font-weight: 400;
  margin-bottom: 0.5rem;
  ${(props) => props.error === true && 'color: var(--error-color)'};
`;

const FailureMessage = styled.div`
  color: var(--default-text);
  white-space: pre-line;
  font-size: 14px;
  text-align: left;
  font-weight: 400;
`;

const StyledRadio = styled(Radio)`
  color: var(--default-text) !important;
  svg {
    height: 20px;
  }
  &.Mui-checked {
    color: var(--trafineo-rot-100);
    &:hover {
      background-color: transparent;
    }
  }
  &:hover {
    background-color: transparent;
  }
`;

const StyledFormControl = styled(FormControl)`
  .MuiTypography-body1 {
    color: var(--default-text);
    font-family: var(--font-family);
    font-size: 14px;
  }
`;
const StyledRadioError = styled(Radio)`
  color: var(--error-color);
  font-family: var(--font-family);
  &.Mui-checked {
    color: black;
    &:hover {
      background-color: transparent;
    }
  }
  &:hover {
    background-color: transparent;
  }
`;

const TooltipHint = styled.p`
  text-decoration: underline;
  &:hover {
    cursor: pointer;
  }
`;

const TooltipImage = styled.img`
  max-width: 65vw;
`;

const ColoredAddBoxIcon = styled(AddBoxIcon)`
  color: var(--trafineo-rot-100);
  margin-bottom: -5px;
`;

const FleetManagerOnboarding = () => {
  const languages = ['nl', 'at'];
  const { t } = useTranslation('onboarding');
  const tLocalization = useTranslation('localization').t;
  const tServiceProvider = useTranslation('serviceProvider').t;

  const [page, setPage] = useState(1);
  const [fleetManagerMail, setFleetManagerMail] = useState({
    value: '',
    error: false,
  });
  const [companyId, setCompanyId] = useState({
    value: '',
    error: false,
  });
  const [country, setCountry] = useState('de');
  const [companyIdPrefix, setCompanyIdPrefix] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [companyTooltipVisible, setCompanyTooltipVisible] = useState(false);
  const [duplicateEmail, setDuplicateEmail] = useState(null);

  useEffect(() => {
    if (getBranding() === 'aral') {
      setCompanyIdPrefix('700674');
    } else if (country) {
      if (country === 'at') {
        setCompanyIdPrefix('700673');
      } else {
        setCompanyIdPrefix('700678');
      }
    }
  }, [country]);

  const [formState, setFormState] = useState({
    questionCompany: {
      value: null,
      error: false,
    },
    question1: {
      value: null,
      error: false,
    },
    question2: {
      value: null,
      error: false,
    },
    corporateMail: {
      value: '',
      error: false,
    },
    firstName: {
      value: '',
      error: false,
    },
    lastName: {
      value: '',
      error: false,
    },
    companyName: {
      value: '',
      error: false,
    },
    phoneNumer: {
      value: '',
      error: false,
    },
    companyId: {
      value: [],
      error: false,
    },
    emails: {
      value: [],
      error: false,
    },
    principle: {
      value: '',
      error: false,
    },
    keyAccountManager: {
      value: '',
      error: false,
    },
  });

  const addFleetManagerMail = () => {
    if (formState.emails.value.indexOf(fleetManagerMail.value) !== -1) {
      setDuplicateEmail(formState.emails.value.indexOf(fleetManagerMail.value));
      return;
    }
    if (
      fleetManagerMail.value.match(mailRegex) &&
      prohibitedDomains.indexOf(
        fleetManagerMail.value.split('@')[1].split('.')[0],
      ) === -1
    ) {
      formState.emails.value.push(fleetManagerMail.value);
      setFleetManagerMail({ ...fleetManagerMail, value: '' });
    } else {
      setFleetManagerMail({ ...fleetManagerMail, error: true });
    }
  };

  const addCompanyId = () => {
    let isNum = /^\d+$/.test(companyId.value);
    let alreadyExists = false;

    if (formState.companyId.value.length > 0) {
      formState.companyId.value.forEach((id) => {
        if (formState.companyId.value.includes(companyId.value)) {
          alreadyExists = true;
        }
      });
    }

    if (
      !isNum ||
      companyId.value.length !== 12 ||
      companyId.value === '' ||
      alreadyExists
    ) {
      setFormState({
        ...formState,
        companyId: { ...formState.companyId, error: true },
      });
    } else {
      formState.companyId.value.push(companyId.value);
      setCompanyId({ ...companyId, value: '' });
    }
  };

  const changeCompanyId = (value) => {
    if (
      value.length >= 6 &&
      value.length <= 12 &&
      value.slice(0, 6) === companyIdPrefix
    ) {
      setCompanyId({
        ...companyId,
        value: value,
      });
      if (formState.companyId.error) {
        setFormState({
          ...formState,
          companyId: { ...formState.companyId, error: false },
        });
      }
    } else {
      if (value.length > 12) {
        setFormState({
          ...formState,
          companyId: { ...formState.companyId, error: 'maximalLengthExceeded' },
        });
      } else {
        setFormState({
          ...formState,
          companyId: { ...formState.companyId, error: true },
        });
      }
    }
  };

  const moveToNextPage = () => {
    let valid = true;

    const tempFormState = JSON.parse(JSON.stringify(formState));
    Object.keys(tempFormState)
      .slice(3, 7)
      .forEach((e) => {
        if (formState[e].value === '') {
          valid = false;
          Object.assign(tempFormState, {
            ...tempFormState,
            [e]: {
              ...tempFormState[e],
              error: true,
            },
          });
        }
      });
    if (
      !tempFormState.corporateMail.value.match(mailRegex) ||
      prohibitedDomains.indexOf(
        tempFormState.corporateMail.value.split('@')[1].split('.')[0],
      ) !== -1
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        corporateMail: {
          ...tempFormState.corporateMail,
          error: true,
        },
      });
    }
    if (valid) {
      setPage(2);
      window.scrollTo(0, 0);
    } else {
      setFormState(tempFormState);
    }
  };

  const redirectToLeasingOnboarding = () => {
    const isLocal = window.location.href.includes('localhost');
    const isDev = window.location.href.includes('dev');
    const isStaging = window.location.href.includes('staging');
    const isQa = window.location.href.includes('qa');

    if (isLocal) {
      window.location.replace('http://localhost:3000/leasing');
    } else if (isDev) {
      window.location.replace('https://reimbursement-dev.trafineo.com/leasing');
    } else if (isStaging) {
      window.location.replace(
        'https://reimbursement-staging.trafineo.com/leasing',
      );
    } else if (isQa) {
      window.location.replace('https://reimbursement-qa.trafineo.com/leasing');
    } else {
      window.location.replace('https://reimbursement.trafineo.com/leasing');
    }
  };

  const submit = async () => {
    let invalid = null;
    let valid = true;
    const tempFormState = JSON.parse(JSON.stringify(formState));
    const countryCodeMap = {
      700674: 'de',
      700678: 'nl',
      700673: 'at',
    };

    if (tempFormState.principle?.value === '') {
      invalid = 'principle';
      valid = false;
    }

    if (companyId.value !== companyIdPrefix && companyId.value !== '') {
      valid = false;
      invalid = 'companyId';
    }

    if (fleetManagerMail.value !== '') {
      valid = false;
      invalid = 'email';
    }

    Object.keys(tempFormState)
      .slice(7)
      .forEach((e) => {
        if (e === 'emails') {
          if (formState[e]?.value?.length === 0) {
            valid = false;
            Object.assign(tempFormState, {
              ...tempFormState,
              [e]: {
                ...tempFormState[e],
                error: true,
              },
            });
            invalid = 'email';
          }
        }
      });

    //check if aral IDs start with 700674
    if (getBranding() === 'aral') {
      //check if start of all Ids is the same
      if (formState.companyId.value.length > 0) {
        formState.companyId.value.forEach((id) => {
          if (id.indexOf('700674') === -1) {
            valid = false;
            invalid = 'companyId';
          }
        });
      } else {
        valid = false;
        invalid = 'companyId';
      }

      //check bp company Ids
    } else if (getBranding() === 'bp') {
      if (formState.companyId.value.find((id) => id.indexOf('700673') !== -1)) {
        //check if start of all Ids is the same
        formState.companyId.value.forEach((id) => {
          if (id.indexOf('700673') === -1) {
            valid = false;
            invalid = 'companyId';
          }
        });
      } else if (
        formState.companyId.value.find((id) => id.indexOf('700678') !== -1)
      ) {
        //check if start of all Ids is the same
        formState.companyId.value.forEach((id) => {
          if (id.indexOf('700678') === -1) {
            valid = false;
            invalid = 'companyId';
          }
        });
      } else {
        //check if at least one valid form for companyIds is used
        valid = false;
        invalid = 'companyId';
      }
    }

    const keyManagerRegex =
      /^[a-zA-Z0-9_.+-]+@(?:(?:[a-zA-Z0-9-]+\.)?[a-zA-Z]+\.)?(.*[aA][rR][aA][lL].*|.*[bB][pP].*)$/;

    if (!formState.keyAccountManager.value.match(keyManagerRegex)) {
      valid = false;
      invalid = 'keyAccountManager';
    }
    if (!valid) {
      if (invalid === 'email') {
        setFormState({
          ...formState,
          emails: { ...formState.emails, error: true },
        });
      }

      if (invalid === 'companyId') {
        setFormState({
          ...formState,
          companyId: { ...formState.companyId, error: true },
        });
      }

      if (
        invalid === 'companyId' &&
        companyId.value !== companyIdPrefix &&
        companyId.value !== ''
      ) {
        setFormState({
          ...formState,
          companyId: { ...formState.companyId, error: 'confirmOrRemoveInput' },
        });
      }

      if (invalid === 'principle') {
        setFormState({
          ...formState,
          principle: { ...formState.principle, error: true },
        });
      }
      if (invalid === 'keyAccountManager') {
        setFormState({
          ...formState,
          keyAccountManager: { ...formState.keyAccountManager, error: true },
        });
      }
    } else {
      setIsLoading(true);
      try {
        await requester().post(
          `/onboarding/registration`,
          {
            country_code:
              countryCodeMap[tempFormState.companyId.value[0].slice(0, 6)],
            companies: [
              {
                leasingcompany_id: null,
                company_ids: formState.companyId.value.map((id) => Number(id)),
              },
            ],
            emailadresses: formState.emails.value,
            customer_type: 'direct',
            principle: formState.principle.value,
            contact: {
              firstname: formState.firstName.value,
              lastname: formState.lastName.value,
              email: formState.corporateMail.value,
              company_name: formState.companyName.value,
              phonenumber: formState.phoneNumer.value,
            },
            keyaccountmanager: {
              email: formState.keyAccountManager.value,
            },
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
        setIsErrorVisible(false);
        setIsLoading(false);
        setShowSuccess(true);
      } catch (err) {
        console.error(err);
        setShowSuccess(false);
        setIsErrorVisible(true);
        setIsLoading(false);
      }
    }
  };

  const { i18n } = useTranslation('home');

  const language =
    window.localStorage.getItem('selected-language')?.substr(1, 2) ||
    i18n.language;

  return (
    <>
      <DialogWrapper
        open={companyTooltipVisible}
        onClose={() => {
          TagManager.dataLayer({
            dataLayer: {
              event: 'popup_interaction',
              popup: {
                name: 'FM_onbaording_company_ID',
                interaction_type: 'close',
              },
            },
          });
          setCompanyTooltipVisible(false);
        }}
        width={'70vw'}
        headline={t('companyIdShow')}
      >
        <div
          dangerouslySetInnerHTML={{
            __html: t('tooltipCompany'),
          }}
        />
        <TooltipImage
          src={
            getBranding() === 'aral'
              ? aralCompanyIdDE
              : language === 'en'
              ? bpCompanyIdEN
              : language === 'de'
              ? bpCompanyIdDE
              : bpCompanyIdNL
          }
        />
      </DialogWrapper>
      <Blank />
      <Background>
        <Wrapper>
          {showSuccess ? (
            <SuccessMessage
              message={t('onboardingSucess')}
              email="<EMAIL>"
            />
          ) : (
            <>
              <Headline isMobile={isMobile}>{t('signUp')}</Headline>
              {(page === 1 && (
                <Page>
                  <Headline2>{t('onboardingDescription')}</Headline2>
                  <Row>
                    <ColRadio>
                      <Label>{t('questionCompany')}</Label>
                      <StyledFormControl component="fieldset">
                        <RadioGroup
                          value={formState.questionCompany.value}
                          onChange={(e) => {
                            setFormState({
                              ...formState,
                              questionCompany: {
                                value: e.target.value === 'true',
                                error: false,
                              },
                            });
                          }}
                        >
                          <FormControlLabel
                            control={
                              formState.questionCompany.error ? (
                                <StyledRadioError />
                              ) : (
                                <StyledRadio />
                              )
                            }
                            value={true}
                            label={t('yes')}
                          />
                          <FormControlLabel
                            control={
                              formState.questionCompany.error ? (
                                <StyledRadioError />
                              ) : (
                                <StyledRadio />
                              )
                            }
                            value={false}
                            label={t('no')}
                          />
                        </RadioGroup>
                      </StyledFormControl>
                    </ColRadio>
                  </Row>
                  <Row>
                    {formState.questionCompany.value === false ? (
                      <ColRadio>
                        <Label>
                          {t('question1', {
                            branding: getBranding() === 'aral' ? 'Aral' : 'BP',
                          })}
                        </Label>
                        <StyledFormControl component="fieldset">
                          <RadioGroup
                            value={formState.question1.value}
                            onChange={(e) => {
                              setFormState({
                                ...formState,
                                question1: {
                                  value: e.target.value === 'true',
                                  error: false,
                                },
                              });
                            }}
                          >
                            <FormControlLabel
                              control={
                                formState.question1.error ? (
                                  <StyledRadioError />
                                ) : (
                                  <StyledRadio />
                                )
                              }
                              value={true}
                              label={t('yes')}
                            />
                            <FormControlLabel
                              control={
                                formState.question1.error ? (
                                  <StyledRadioError />
                                ) : (
                                  <StyledRadio />
                                )
                              }
                              value={false}
                              label={t('no')}
                            />
                          </RadioGroup>
                        </StyledFormControl>
                      </ColRadio>
                    ) : (
                      formState.questionCompany.value === true && (
                        <FailureMessageWrapper>
                          <FailureMessage>
                            {t('questionCompanyFailure')}
                          </FailureMessage>
                        </FailureMessageWrapper>
                      )
                    )}
                  </Row>
                  <Row>
                    {formState.questionCompany.value === false &&
                    formState.question1.value === true ? (
                      <ColRadio>
                        <Label>{t('question2')}</Label>
                        <StyledFormControl component="fieldset">
                          <RadioGroup
                            value={formState.question2.value}
                            onChange={(e) => {
                              setFormState({
                                ...formState,
                                question2: {
                                  value: e.target.value === 'true',
                                  error: false,
                                },
                              });
                            }}
                          >
                            <FormControlLabel
                              control={
                                formState.question2.error ? (
                                  <StyledRadioError />
                                ) : (
                                  <StyledRadio />
                                )
                              }
                              value={true}
                              label={t('yes')}
                            />
                            <FormControlLabel
                              control={
                                formState.question2.error ? (
                                  <StyledRadioError />
                                ) : (
                                  <StyledRadio />
                                )
                              }
                              value={false}
                              label={t('no')}
                            />
                          </RadioGroup>
                        </StyledFormControl>
                      </ColRadio>
                    ) : (
                      formState.question1.value === false && (
                        <FailureMessageWrapper>
                          <FailureMessage>
                            {t('FailureMessage1')}
                          </FailureMessage>
                          <a href="https://www.aral.de/de/global/fleet_solutions/aral-tankkarten/aral-fuel-und-charge.html">
                            {tLocalization(`deCountryName`)}
                          </a>
                          <a href="https://www.bp.com/nl_nl/netherlands/home/<USER>/wagenparkoplossingen/bp-fuel-charge.html">
                            {tLocalization(`nlCountryName`)}
                          </a>
                          <a href="https://www.bp.com/de_at/austria/home/<USER>/bp-flottenlosungen/mobilitaetsloesungen/kraftstoff-ladung.html">
                            {tLocalization(`atCountryName`)}
                          </a>
                        </FailureMessageWrapper>
                      )
                    )}
                  </Row>
                  {formState.questionCompany.value === false &&
                    formState.question1.value === true &&
                    formState.question2.value === false && (
                      <>
                        <Row>
                          <Headline2>{t('addInformation')}</Headline2>
                        </Row>
                        <Row>
                          <InputWrapper>
                            <TextField
                              newDriver
                              error={formState.firstName.error}
                              onFocus={() =>
                                setFormState({
                                  ...formState,
                                  firstName: {
                                    ...formState.firstName,
                                    error: false,
                                  },
                                })
                              }
                              data-cy="firstNameInput"
                              value={formState.firstName.value}
                              placeholder={t('firstNamePlaceholder')}
                              label={t('firstName')}
                              name="firstName"
                              onChange={(e) => {
                                setFormState({
                                  ...formState,
                                  firstName: {
                                    ...formState.firstName,
                                    value: e.target.value,
                                  },
                                });
                              }}
                            />
                          </InputWrapper>
                        </Row>
                        <Row>
                          <InputWrapper>
                            <TextField
                              newDriver
                              error={formState.lastName.error}
                              onFocus={() =>
                                setFormState({
                                  ...formState,
                                  lastName: {
                                    ...formState.lastName,
                                    error: false,
                                  },
                                })
                              }
                              data-cy="lastNameInput"
                              value={formState.lastName.value}
                              placeholder={t('lastNamePlaceholder')}
                              label={t('lastName')}
                              name="lastName"
                              onChange={(e) => {
                                setFormState({
                                  ...formState,
                                  lastName: {
                                    ...formState.lastName,
                                    value: e.target.value,
                                  },
                                });
                              }}
                            />
                          </InputWrapper>
                        </Row>
                        <Row>
                          <InputWrapper>
                            <TextField
                              newDriver
                              error={formState.corporateMail.error}
                              onFocus={() =>
                                setFormState({
                                  ...formState,
                                  corporateMail: {
                                    ...formState.corporateMail,
                                    error: false,
                                  },
                                })
                              }
                              data-cy="corporateMailInput"
                              value={formState.corporateMail.value}
                              placeholder={t('corporateMailPlaceholder')}
                              label={t('corporateMail')}
                              name="corporateMail"
                              onChange={(e) => {
                                setFormState({
                                  ...formState,
                                  corporateMail: {
                                    ...formState.corporateMail,
                                    value: e.target.value,
                                  },
                                });
                              }}
                            />
                            {formState.corporateMail.error && (
                              <Error>{t('emailError')}</Error>
                            )}
                          </InputWrapper>
                        </Row>
                        <Row>
                          <InputWrapper>
                            <TextField
                              newDriver
                              error={formState.companyName.error}
                              onFocus={() =>
                                setFormState({
                                  ...formState,
                                  companyName: {
                                    ...formState.companyName,
                                    error: false,
                                  },
                                })
                              }
                              data-cy="companyNameInput"
                              value={formState.companyName.value}
                              placeholder={t('companyNamePlaceholder')}
                              label={t('companyName')}
                              name="companyName"
                              onChange={(e) => {
                                setFormState({
                                  ...formState,
                                  companyName: {
                                    ...formState.companyName,
                                    value: e.target.value,
                                  },
                                });
                              }}
                            />
                          </InputWrapper>
                        </Row>
                        <Row>
                          <InputWrapper>
                            <TextField
                              newDriver
                              error={formState.phoneNumer.error}
                              onFocus={() =>
                                setFormState({
                                  ...formState,
                                  phoneNumer: {
                                    ...formState.phoneNumer,
                                    error: false,
                                  },
                                })
                              }
                              data-cy="phoneNumerInput"
                              value={formState.phoneNumer.value}
                              placeholder={t('phoneNumerPlaceholder')}
                              label={t('phoneNumer')}
                              name="phoneNumer"
                              onChange={(e) => {
                                setFormState({
                                  ...formState,
                                  phoneNumer: {
                                    ...formState.phoneNumer,
                                    value: e.target.value,
                                  },
                                });
                              }}
                            />
                          </InputWrapper>
                        </Row>
                        <Row>
                          <ButtonContainer>
                            <Button variant="primary" onClick={moveToNextPage}>
                              {t('continue')}
                            </Button>
                          </ButtonContainer>
                        </Row>
                      </>
                    )}

                  {formState.questionCompany.value === false &&
                    formState.question1.value === true &&
                    formState.question2.value === true && (
                      <ButtonContainer>
                        <Button
                          variant="primary"
                          onClick={redirectToLeasingOnboarding}
                        >
                          {t('continue')}
                        </Button>
                      </ButtonContainer>
                    )}
                </Page>
              )) || (
                <Page>
                  <Headline2>{t('companyInformation')}</Headline2>
                  {getBranding() === 'bp' && (
                    <Row>
                      <InputWrapper fullWidth>
                        <Description>{t('countrySelect')}</Description>
                        <Select
                          big
                          onChange={(e) => {
                            setCountry(e.target.value);
                          }}
                          disabled={formState.companyId.value.length !== 0}
                          displayEmpty
                          value={country}
                          renderValue={
                            country !== ''
                              ? undefined
                              : () => (
                                  <Placeholder>
                                    {tServiceProvider(
                                      'inviteLanguagePlaceholder',
                                    )}
                                  </Placeholder>
                                )
                          }
                        >
                          {languages.map((entry) => (
                            <StyledMenuItem
                              key={entry}
                              value={entry}
                              data-cy={`${entry}Select`}
                            >
                              <CountryFlag country={entry} />
                              {tLocalization(`${entry}CountryName`)}
                            </StyledMenuItem>
                          ))}
                        </Select>
                      </InputWrapper>
                    </Row>
                  )}

                  <Row>
                    <FleetManagersWrapper>
                      {formState.companyId.value.map((entry, index) => (
                        <FleetManagerMail
                          key={entry}
                          error={duplicateEmail === index}
                        >
                          {entry}
                          <IconButtonWrapper>
                            <IconButton
                              onClick={() => {
                                setFormState({
                                  ...formState,
                                  companyId: {
                                    ...formState.companyId,
                                    value: formState.companyId.value.filter(
                                      (e) => e !== entry,
                                    ),
                                  },
                                });
                                setDuplicateEmail(null);
                              }}
                            >
                              <IndeterminateCheckBoxIcon />
                            </IconButton>
                          </IconButtonWrapper>
                        </FleetManagerMail>
                      ))}
                    </FleetManagersWrapper>
                  </Row>

                  <AddCompanyHint>
                    {t('addCompanyHint')} <ColoredAddBoxIcon />
                    {t('addCompanyHint2')}
                  </AddCompanyHint>

                  <Row>
                    <CompanyIdInputWrapper>
                      <TextField
                        disabled={companyIdPrefix === null}
                        value={companyId.value || companyIdPrefix}
                        onChange={(e) => {
                          changeCompanyId(e.target.value);
                        }}
                        type="text"
                        maxLength="13"
                        newDriver
                        disableMinWidth
                        error={formState.companyId.error}
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            companyId: {
                              ...formState.companyId,
                              error: false,
                            },
                          })
                        }
                        label={t('companyId')}
                      />
                    </CompanyIdInputWrapper>
                    <CompanyIconButtonContainer>
                      <IconButton onClick={addCompanyId}>
                        <AddBoxIcon />
                      </IconButton>
                    </CompanyIconButtonContainer>
                  </Row>
                  <ErrorCompanyId
                    error={formState.companyId.error}
                    display={formState.companyId.error.length > 0}
                  >
                    {formState.companyId.error === 'maximalLengthExceeded' &&
                      t('maximalLengthExceeded')}
                    {formState.companyId.error === 'confirmOrRemoveInput' &&
                      t('confirmOrRemoveInput')}
                  </ErrorCompanyId>
                  <Row>
                    <Hint>{t('companyIdHint')}</Hint>
                  </Row>
                  <Row>
                    <TooltipHint
                      onClick={() => {
                        TagManager.dataLayer({
                          dataLayer: {
                            event: 'popup_interaction',
                            popup: {
                              name: 'FM_onbaording_company_ID',
                              interaction_type: 'open',
                            },
                          },
                        });
                        setCompanyTooltipVisible(true);
                      }}
                    >
                      {t('companyIdShow')}
                    </TooltipHint>
                  </Row>
                  <Row>
                    <ColRadio>
                      <Label error={formState.principle.error}>
                        {t('principle')}
                      </Label>
                      <InformationContainer>
                        <InformationIcon>
                          <img src={infoIcon} alt="success" />
                        </InformationIcon>
                        <InformationText>
                          {t('principleTootip')}
                        </InformationText>
                      </InformationContainer>
                      <StyledFormControl component="fieldset">
                        <RadioGroup
                          value={formState.principle.value}
                          onChange={(e) => {
                            setFormState({
                              ...formState,
                              principle: {
                                value: e.target.value,
                                error: false,
                              },
                            });
                          }}
                        >
                          <FormControlLabel
                            control={
                              formState.principle.error ? (
                                <StyledRadioError />
                              ) : (
                                <StyledRadio />
                              )
                            }
                            value="foureye"
                            label={t('yes')}
                          />
                          <FormControlLabel
                            control={
                              formState.principle.error ? (
                                <StyledRadioError />
                              ) : (
                                <StyledRadio />
                              )
                            }
                            value="twoeye"
                            label={t('no')}
                          />
                        </RadioGroup>
                      </StyledFormControl>
                    </ColRadio>
                  </Row>
                  <Headline2>{t('addFleetmanagers')}</Headline2>
                  <FleetManagersWrapper>
                    {formState.emails.value.length === 0 && (
                      <FleetManagerMail error={formState.emails.error}>
                        {t('noFleetManagers')}
                      </FleetManagerMail>
                    )}
                    {formState.emails.value.map((entry, index) => (
                      <FleetManagerMail
                        key={entry}
                        error={duplicateEmail === index}
                      >
                        {entry}
                        <IconButtonWrapper>
                          <IconButton
                            onClick={() => {
                              setFormState({
                                ...formState,
                                emails: {
                                  ...formState.emails,
                                  value: formState.emails.value.filter(
                                    (e) => e !== entry,
                                  ),
                                },
                              });
                              setDuplicateEmail(null);
                            }}
                          >
                            <IndeterminateCheckBoxIcon />
                          </IconButton>
                        </IconButtonWrapper>
                      </FleetManagerMail>
                    ))}
                  </FleetManagersWrapper>
                  <Row>
                    <FleetManagerMailInputWrapper>
                      <TextField
                        value={fleetManagerMail.value}
                        onChange={(e) => {
                          setDuplicateEmail(null);
                          setFleetManagerMail({
                            ...fleetManagerMail,
                            value: e.target.value,
                          });
                        }}
                        onFocus={(e) => {
                          setDuplicateEmail(null);
                          setFleetManagerMail({
                            ...fleetManagerMail,
                            error: false,
                          });
                          setDuplicateEmail(null);
                        }}
                        type="text"
                        error={
                          fleetManagerMail.error ||
                          duplicateEmail ||
                          duplicateEmail === 0
                        }
                        newDriver
                        placeholder={t('emailPlaceholder')}
                        label={t('email')}
                      />
                    </FleetManagerMailInputWrapper>
                    <IconButtonWrapper>
                      <IconButton onClick={addFleetManagerMail}>
                        <AddBoxIcon />
                      </IconButton>
                    </IconButtonWrapper>
                  </Row>
                  <Spacer />
                  <Description>{t('fleetmanagersHint')}</Description>
                  {fleetManagerMail.error && <Error>{t('emailError')}</Error>}
                  {(duplicateEmail === 0 || duplicateEmail) && (
                    <Error>{t('mailAlreadyExists')}</Error>
                  )}
                  <Spacer />
                  <Headline2>{t('addKeyAccountManager')}</Headline2>
                  <Row>
                    <InputWrapper>
                      <TextField
                        newDriver
                        error={formState.keyAccountManager.error}
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            keyAccountManager: {
                              ...formState.keyAccountManager,
                              error: false,
                            },
                          })
                        }
                        data-cy="keyManagerInput"
                        value={formState.keyAccountManager.value}
                        placeholder={t('keyAccountManagerPlaceholder')}
                        label={t('keyAccountManager')}
                        name="keyAccountManager"
                        onChange={(e) => {
                          setFormState({
                            ...formState,
                            keyAccountManager: {
                              ...formState.keyAccountManager,
                              value: e.target.value,
                            },
                          });
                        }}
                      />
                    </InputWrapper>
                  </Row>
                  {formState.keyAccountManager.error && (
                    <Error>{t('keyAccountError')}</Error>
                  )}
                  {isErrorVisible && (
                    <Error>{tServiceProvider('requestError')}</Error>
                  )}
                  <Row>
                    <ButtonContainer>
                      {(isLoading && <CircularProgress />) || (
                        <>
                          <Button
                            onClick={() => {
                              setPage(1);
                              window.scrollTo(0, 0);
                            }}
                          >
                            {t('back')}
                          </Button>
                          <Button variant="primary" onClick={submit}>
                            {t('submit')}
                          </Button>
                        </>
                      )}
                    </ButtonContainer>
                  </Row>
                </Page>
              )}
            </>
          )}
        </Wrapper>
      </Background>
    </>
  );
};

export default FleetManagerOnboarding;
