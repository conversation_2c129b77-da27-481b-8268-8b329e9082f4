import { useContext, useEffect, useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import Center from '../Components/helper/Center';
import requester from '../utils/requester';
import QuestionMark from '../Components/QuestionMark';
import logger from '../utils/logger';
import CircularProgress from '../Components/CircularProgress';
import Pagination from '../Components/dashboard/Pagination';
import { Button } from '../Components';
import PageWrapper from '../Components/PageWrapper';
import jwtDecode from 'jwt-decode';
import {
  Row,
  RowBox,
  RowContainer,
  TitleRow,
} from '../Components/dashboard/RowTemplates';
import {
  HeadlineBox,
  LoadingCircleContainer,
  TableContainer,
  Wrapper,
} from '../Components/dashboard/DashboardTemplates';
import { userContext } from '../ContextProvider';
import { useLocation, useParams } from 'react-router-dom';
import { browserName } from 'react-device-detect';
import qs from 'querystring';

const PayoutOverview = () => {
  const pageSize = 7;

  const { id } = useParams();
  const superUser = id;

  const [data, setData] = useState([]);
  const [recordCount, setRecordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const { t } = useTranslation('overview');
  const tDirectCustomer = useTranslation('directCustomer').t;
  const { user } = useContext(userContext);

  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { mail, redirect } = parsedQueryString;

  const monthNames = [
    t('january'),
    t('february'),
    t('march'),
    t('april'),
    t('may'),
    t('june'),
    t('july'),
    t('august'),
    t('september'),
    t('october'),
    t('november'),
    t('december'),
  ];

  const getData = useCallback(
    async (from, to) => {
      setIsLoading(true);
      try {
        const decodedUser = jwtDecode(user.access_token);
        const rsp = await requester().get(
          `/payout_reports?offset=${from}&limit=${to}`,
          {
            headers: {
              role: decodedUser.role,
              user_id: id ? id : decodedUser.sub,
            },
          },
        );
        setIsLoading(false);
        return rsp;
      } catch (err) {
        setIsLoading(false);
        logger().error(`Couldn't get data from api.\n${err.message}`);
        return undefined;
      }
    },
    [user.access_token, id],
  );

  useEffect(() => {
    const fetchData = async () => {
      let rspData = null;
      rspData = await getData(currentPage * pageSize, pageSize);
      if (rspData) {
        setData(rspData.data.data);
        setRecordCount(rspData?.headers['x-total-count'] || 0);
      }
    };
    fetchData();
  }, [currentPage, getData]);

  const downloadPayoutReport = async (id, month, year) => {
    const paddedMonth = month.toString().padStart(2, '0');
    const fileName = `${paddedMonth + '/' + year + ' '}${t('payouts')}.pdf`;

    const decodedUser = jwtDecode(user.access_token);
    const base64Pdf = await requester().get(`/payout_reports/${id}`, {
      headers: {
        role: decodedUser.role,
        user_id: decodedUser.sub,
      },
    });

    if (browserName.indexOf('Safari') !== -1) {
      const base64 = await fetch(base64Pdf.data.pdfData);
      const blob = await base64.blob();
      let url = URL.createObjectURL(blob);
      let a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
    } else {
      const downloadLink = document.createElement('a');
      downloadLink.href = base64Pdf.data.pdfData;
      downloadLink.download = fileName;
      downloadLink.click();
    }
  };

  const formatDate = (month, year) => {
    const date = monthNames[month - 1] + ' ' + year;
    return date;
  };
  return (
    <PageWrapper
      backButton={redirect ? false : true}
      minWidth="1230px"
      title={superUser ? t('payoutsForUser') + ' ' + mail : t('payouts')}
    >
      {(isLoading && (
        <LoadingCircleContainer>
          <CircularProgress />
        </LoadingCircleContainer>
      )) || (
        <Wrapper>
          <TableContainer>
            <TitleRow>
              <HeadlineBox>
                <RowBox bold center width={20}>
                  {t('paidDate')}
                </RowBox>
                <RowBox bold center width={20}>
                  {t('paidAmmount')}
                </RowBox>
                <RowBox bold center width={20}>
                  {t('appliedTariff')}
                  <QuestionMark tooltip={{ content: t('appliedTariffHint') }} />
                </RowBox>
                <RowBox bold center width={20}>
                  {t('consumption')}
                </RowBox>
                <RowBox bold center width={20} />
              </HeadlineBox>
            </TitleRow>
            {(isLoading && (
              <LoadingCircleContainer>
                <CircularProgress />
              </LoadingCircleContainer>
            )) || (
              <>
                {data.map((row, i) => (
                  <Row>
                    <RowContainer>
                      <RowBox center width={20}>
                        {formatDate(row.report_month, row.report_year)}
                      </RowBox>
                      <RowBox center width={20}>
                        {row.total_cost}
                      </RowBox>
                      <RowBox center width={20}>
                        {row.workprice}
                      </RowBox>
                      <RowBox center width={20}>
                        {row.total_energy}
                      </RowBox>
                      <RowBox center width={19}>
                        <Button
                          index={i}
                          onClick={() => {
                            downloadPayoutReport(
                              row.id,
                              row.report_month,
                              row.report_year,
                            );
                          }}
                          variant="primary"
                        >
                          {t('download')}
                        </Button>
                      </RowBox>
                      <RowBox center width={1}></RowBox>
                    </RowContainer>
                  </Row>
                ))}
              </>
            )}
            {!isLoading &&
              (!data || recordCount === 0 || recordCount === '0') && (
                <Center>
                  <h2>{tDirectCustomer('noDataAvailable')}</h2>
                </Center>
              )}
          </TableContainer>
        </Wrapper>
      )}

      {!isLoading && data && recordCount !== 0 && recordCount !== '0' && (
        <Center>
          <Pagination
            onChange={(pos) => setCurrentPage(pos)}
            position={currentPage}
            count={Number(recordCount)}
            pageSize={pageSize}
          />
        </Center>
      )}
    </PageWrapper>
  );
};

export default PayoutOverview;
