import { useEffect, useState, Fragment } from 'react';
import styled from 'styled-components';
import { getBranding, mailRegex, prohibitedDomains } from '../../utils/helper';
import { useTranslation } from 'react-i18next';
import requester from '../../utils/requester';
import infoIcon from '../../static/img/icons/Info_Icon.svg';
import { TextField, Button, QuestionMark } from '../../Components';
import { Row } from '../HomeChargingAustria/Styles';
import SuccessMessage from '../../Components/SuccessMessage';

import {
  Error,
  InformationContainer,
  InformationIcon,
  InformationText,
} from '../../Components/evDriver/PageTemplate';

import CircularProgress from '../../Components/CircularProgress';
import { Wrapper } from '../DriverOnboarding';
import PageWrapper from '../../Components/PageWrapper';
import { Checkbox } from '../../Components';
import LeasingCompanyInfo from './LeasingCompanyInfo';
import CloseButton from './CloseButton';
import { useNavigate } from 'react-router-dom';
import LeasingFleetManager from './LeasingFleetmanager';

const ColRadio = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2rem;
`;

const TextCheckbox = styled.p`
  line-height: 24px;
  margin: 0;
  padding-left: 15px;
  cursor: pointer;
`;

const ButtonContainer = styled.div`
  margin: 2rem 0 4rem 0;
  display: flex;
  width: 100%;
  justify-content: flex-end;
  button {
    margin-left: 0.5rem;
    width: fit-content;
  }
`;

const InputWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
`;

const Spacer = styled.div`
  height: 30px;
`;

const Headline2 = styled.div`
  font-size: 16px;
  text-align: left;
  color: black;
  margin: 1rem 0 2rem 0;
  line-height: 20px;
  font-weight: bold;
  white-space: pre-line;
`;

const CompanyHeader = styled.h2`
  width: 70%;
`;

const LeasingManagerOnboarding = () => {
  const { t } = useTranslation('onboarding');
  const tServiceProvider = useTranslation('serviceProvider').t;

  const navigate = useNavigate();

  useEffect(() => {
    if (getBranding() !== 'trafineo') {
      navigate('/error', { replace: true });
    }
  }, [navigate]);

  const [formState, setFormState] = useState({
    corporateMail: {
      value: '',
      error: false,
    },
    firstName: {
      value: '',
      error: false,
    },
    lastName: {
      value: '',
      error: false,
    },
    companyName: {
      value: '',
      error: false,
    },
    phoneNumer: {
      value: '',
      error: false,
    },
    companyId: {
      values: [[]],
      error: false,
    },
    emails: {
      value: [],
      error: false,
    },
    principle: {
      value: 'foureye',
      error: false,
    },
    keyAccountManager: {
      value: '',
      error: false,
    },
    leasingCompanyName: {
      value: '',
      error: false,
    },
  });

  const [page, setPage] = useState(1);
  const [companyId, setCompanyId] = useState([
    {
      value: '',
      error: false,
    },
  ]);
  const [companies, setCompanies] = useState([
    { name: { value: '', error: false }, company_ids: [] },
  ]);
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [acceptedDPS, setAcceptedDPS] = useState(false);

  const moveToNextPage = () => {
    let valid = true;

    const tempFormState = JSON.parse(JSON.stringify(formState));
    Object.keys(tempFormState)
      .slice(0, 5)
      .forEach((e) => {
        if (formState[e].value === '') {
          valid = false;
          Object.assign(tempFormState, {
            ...tempFormState,
            [e]: {
              ...tempFormState[e],
              error: true,
            },
          });
        }
      });
    if (
      !tempFormState.corporateMail.value.match(mailRegex) ||
      prohibitedDomains.indexOf(
        tempFormState.corporateMail.value.split('@')[1].split('.')[0],
      ) !== -1
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        corporateMail: {
          ...tempFormState.corporateMail,
          error: true,
        },
      });
    }
    if (valid) {
      setPage(2);
      window.scrollTo(0, 0);
    } else {
      setFormState(tempFormState);
    }
  };

  const removeCompany = (i) => {
    //remove companies entry
    let newArr = [...companies];
    newArr.splice(i, 1);
    setCompanies(newArr);

    let newJSON = { ...companyId };
    delete newJSON[i];
    //change JSON keys to fit new Object
    if (i !== Object.keys(newJSON).length) {
      Object.keys(newJSON).forEach((originalKey) => {
        if (originalKey > i) {
          newJSON[originalKey - 1] = newJSON[originalKey];
        }
      });
      delete newJSON[Object.keys(newJSON).length - 1];
    }
    setCompanyId(newJSON);
  };

  const submit = async () => {
    if (formState.emails.value.length < 1) {
      setFormState({
        ...formState,
        emails: {
          ...formState.emails,
          error: true,
        },
      });
      return;
    }

    const tempFormState = JSON.parse(JSON.stringify(formState));
    tempFormState.companies = [];
    companies.forEach((company) => {
      let newIds = [];
      company.company_ids.forEach((companyId) => {
        newIds.push(Number(companyId));
      });
      tempFormState.companies.push({
        leasingcompany_id: company.name.value,
        company_ids: newIds,
      });
    });
    setIsLoading(true);
    try {
      await requester().post(
        `/onboarding/registration`,
        {
          country_code: 'de',
          companies: tempFormState.companies,
          emailadresses: tempFormState.emails.value,
          customer_type: 'indirect',
          principle: tempFormState.principle.value,
          contact: {
            firstname: tempFormState.firstName.value,
            lastname: tempFormState.lastName.value,
            email: tempFormState.corporateMail.value,
            company_name: tempFormState.companyName.value,
            phonenumber: tempFormState.phoneNumer.value,
          },
        },
        {
          headers: {
            'Content-Type': 'application/json',
          },
        },
      );
      setIsErrorVisible(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (err) {
      console.error(err);
      setShowSuccess(false);
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  const addLeasingCompany = () => {
    setCompanies((companies) => [
      ...companies,
      { name: { value: '', error: false }, company_ids: [] },
    ]);
    setCompanyId({
      ...companyId,
      [companies.length]: {
        value: '',
        error: false,
      },
    });
  };

  const moveToThirdPage = () => {
    let valid = true;
    const tempCompanies = JSON.parse(JSON.stringify(companies));
    const tempCompanyId = JSON.parse(JSON.stringify(companyId));
    tempCompanies.forEach((e, i) => {
      if (e.name.value === '') {
        valid = false;
        tempCompanies[i].name.error = true;
      }
      if (e.company_ids.length === 0) {
        valid = false;
        tempCompanyId[i].error = true;
      }
    });
    if (!valid) {
      setCompanies(tempCompanies);
      setCompanyId(tempCompanyId);
    } else {
      setPage(3);
      window.scrollTo(0, 0);
    }
  };

  return (
    <PageWrapper title={t('signUp')} subtitle={t('leasingBuisness')}>
      {showSuccess ? (
        <SuccessMessage
          message={t('onboardingSucess')}
          email="<EMAIL>"
        />
      ) : (
        <>
          {page === 1 && (
            <Wrapper>
              <Row>
                <Headline2>{t('addInformation')}</Headline2>
              </Row>
              <Row>
                <InputWrapper>
                  <TextField
                    newDriver
                    error={formState.firstName.error}
                    onFocus={() =>
                      setFormState({
                        ...formState,
                        firstName: {
                          ...formState.firstName,
                          error: false,
                        },
                      })
                    }
                    data-cy="firstNameInput"
                    value={formState.firstName.value}
                    placeholder={t('firstNamePlaceholder')}
                    label={t('firstName')}
                    name="firstName"
                    onChange={(e) => {
                      setFormState({
                        ...formState,
                        firstName: {
                          ...formState.firstName,
                          value: e.target.value,
                        },
                      });
                    }}
                  />
                </InputWrapper>
              </Row>
              <Row>
                <InputWrapper>
                  <TextField
                    newDriver
                    error={formState.lastName.error}
                    onFocus={() =>
                      setFormState({
                        ...formState,
                        lastName: {
                          ...formState.lastName,
                          error: false,
                        },
                      })
                    }
                    data-cy="lastNameInput"
                    value={formState.lastName.value}
                    placeholder={t('lastNamePlaceholder')}
                    label={t('lastName')}
                    name="lastName"
                    onChange={(e) => {
                      setFormState({
                        ...formState,
                        lastName: {
                          ...formState.lastName,
                          value: e.target.value,
                        },
                      });
                    }}
                  />
                </InputWrapper>
              </Row>
              <Row>
                <InputWrapper>
                  <TextField
                    newDriver
                    error={formState.corporateMail.error}
                    onFocus={() =>
                      setFormState({
                        ...formState,
                        corporateMail: {
                          ...formState.corporateMail,
                          error: false,
                        },
                      })
                    }
                    data-cy="corporateMailInput"
                    value={formState.corporateMail.value}
                    placeholder={t('corporateMailPlaceholder')}
                    label={t('corporateMail')}
                    name="corporateMail"
                    onChange={(e) => {
                      setFormState({
                        ...formState,
                        corporateMail: {
                          ...formState.corporateMail,
                          value: e.target.value,
                        },
                      });
                    }}
                  />
                  {formState.corporateMail.error && (
                    <Error>{t('emailError')}</Error>
                  )}
                </InputWrapper>
              </Row>
              <Row>
                <InputWrapper>
                  <TextField
                    newDriver
                    error={formState.companyName.error}
                    onFocus={() =>
                      setFormState({
                        ...formState,
                        companyName: {
                          ...formState.companyName,
                          error: false,
                        },
                      })
                    }
                    data-cy="companyNameInput"
                    value={formState.companyName.value}
                    placeholder={t('companyNamePlaceholder')}
                    label={t('companyName')}
                    name="companyName"
                    onChange={(e) => {
                      setFormState({
                        ...formState,
                        companyName: {
                          ...formState.companyName,
                          value: e.target.value,
                        },
                      });
                    }}
                  />
                </InputWrapper>
              </Row>
              <Row>
                <InputWrapper>
                  <TextField
                    newDriver
                    error={formState.phoneNumer.error}
                    onFocus={() =>
                      setFormState({
                        ...formState,
                        phoneNumer: {
                          ...formState.phoneNumer,
                          error: false,
                        },
                      })
                    }
                    data-cy="phoneNumerInput"
                    value={formState.phoneNumer.value}
                    placeholder={t('phoneNumerPlaceholder')}
                    label={t('phoneNumer')}
                    name="phoneNumer"
                    onChange={(e) => {
                      setFormState({
                        ...formState,
                        phoneNumer: {
                          ...formState.phoneNumer,
                          value: e.target.value,
                        },
                      });
                    }}
                  />
                </InputWrapper>
              </Row>
              <p>{t('phonenumberHint')}</p>
              <Row>
                <ButtonContainer>
                  <Button
                    onClick={() => {
                      window.history.back();
                    }}
                  >
                    {t('back')}
                  </Button>
                  <Button variant="primary" onClick={moveToNextPage}>
                    {t('continue')}
                  </Button>
                </ButtonContainer>
              </Row>
            </Wrapper>
          )}
          {page === 2 && (
            <Wrapper>
              <Headline2>
                {t('leasingCompanyInformation')}
                <QuestionMark
                  tooltip={{ content: t('leasingCompanyTooltip') }}
                ></QuestionMark>
              </Headline2>
              {companies.map((row, i) => {
                return (
                  <Fragment key={row + i}>
                    <Row key={row + i}>
                      <CompanyHeader>
                        {t('company') + ' ' + (i + 1)}
                      </CompanyHeader>
                      {i > 0 && (
                        <CloseButton onClick={() => removeCompany(i)} />
                      )}
                    </Row>
                    <LeasingCompanyInfo
                      key={row}
                      companies={companies}
                      setCompanies={setCompanies}
                      companyId={companyId}
                      setCompanyId={setCompanyId}
                      formState={formState}
                      index={i}
                    />
                  </Fragment>
                );
              })}
              <Button onClick={addLeasingCompany}>
                {'+ ' + t('addLeasingCompany')}
              </Button>
              <Spacer />
              <Row>
                <Checkbox
                  id="principleCheckBox"
                  checked={formState.principle.value === 'foureye'}
                  onChange={() =>
                    setFormState({
                      ...formState,
                      principle: {
                        ...formState.principle,

                        value:
                          formState.principle.value === 'twoeye'
                            ? 'foureye'
                            : 'twoeye',
                      },
                    })
                  }
                />
                <TextCheckbox
                  onClick={() =>
                    setFormState({
                      ...formState,
                      principle: {
                        ...formState.principle,
                        value:
                          formState.principle.value === 'twoeye'
                            ? 'foureye'
                            : 'twoeye',
                      },
                    })
                  }
                >
                  {t('principleText')}
                </TextCheckbox>
              </Row>
              <Row>
                <ColRadio>
                  <InformationContainer>
                    <InformationIcon>
                      <img src={infoIcon} alt="success" />
                    </InformationIcon>
                    <InformationText>
                      {t('principleTootipSales')}
                    </InformationText>
                  </InformationContainer>
                </ColRadio>
              </Row>
              <Row>
                <ButtonContainer>
                  {(isLoading && <CircularProgress />) || (
                    <>
                      <Button
                        onClick={() => {
                          setPage(page - 1);
                          window.scrollTo(0, 0);
                        }}
                      >
                        {t('back')}
                      </Button>
                      <Button variant="primary" onClick={moveToThirdPage}>
                        {t('continue')}
                      </Button>
                    </>
                  )}
                </ButtonContainer>
              </Row>
            </Wrapper>
          )}
          {page === 3 && (
            <Wrapper>
              <Headline2>{t('addFleetmanagers')}</Headline2>
              <LeasingFleetManager
                acceptedDPS={acceptedDPS}
                setAcceptedDPS={setAcceptedDPS}
                formState={formState}
                setFormState={setFormState}
              />
              <Row>
                <ButtonContainer>
                  {(isLoading && <CircularProgress />) || (
                    <>
                      <Button
                        onClick={() => {
                          setPage(page - 1);
                          window.scrollTo(0, 0);
                        }}
                      >
                        {t('back')}
                      </Button>
                      <Button
                        variant="primary"
                        disabled={!acceptedDPS}
                        onClick={submit}
                      >
                        {t('submit')}
                      </Button>
                    </>
                  )}
                </ButtonContainer>
              </Row>
              {isErrorVisible && (
                <Error>{tServiceProvider('requestError')}</Error>
              )}
            </Wrapper>
          )}
        </>
      )}
    </PageWrapper>
  );
};

export default LeasingManagerOnboarding;
