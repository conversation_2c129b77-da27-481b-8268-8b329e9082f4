import { useTranslation } from 'react-i18next';

const LeasingCompanies = () => {
  const { t } = useTranslation('onboarding');
  return [
    {
      name: 'abcfinance GmbH',
      id: 1,
    },
    {
      name: 'akf servicelease Gmbh & Co. KG',
      id: 2,
    },
    {
      name: 'ALD International GmbH',
      id: 3,
    },
    {
      name: 'ALD Lease Finanz GmbH',
      id: 4,
    },
    {
      name: 'Allane SE',
      id: 5,
    },
    {
      name: 'Allane Mobility Consulting GmbH',
      id: 6,
    },
    {
      name: 'Alphabet Fuhrparkmanagement GmbH',
      id: 7,
    },
    {
      name: 'Holman GmbH',
      id: 8,
    },
    {
      name: 'Arval Deutschland GmbH',
      id: 9,
    },
    {
      name: 'Athlon Germany GmbH',
      id: 10,
    },
    {
      name: 'ATLAS AUTO-LEASING GmbH',
      id: 11,
    },
    {
      name: 'BMW BANK GMBH',
      id: 12,
    },
    {
      name: 'Deutsche Leasing AG',
      id: 13,
    },
    {
      name: 'DirectLease.de GmbH',
      id: 14,
    },
    {
      name: 'HHL Hamburg Leasing GmbH',
      id: 15,
    },
    {
      name: 'LeasePlan Deutschland GmbH',
      id: 16,
    },
    {
      name: 'LogPay Transport Services GmbH',
      id: 17,
    },
    {
      name: 'Mobexo GmbH',
      id: 18,
    },
    {
      name: 'MOBILITY CONCEPT GMBH',
      id: 19,
    },
    {
      name: 'Opel Bank S.A. Niederlassung Deutschland',
      id: 20,
    },
    {
      name: 'Raiffeisen-IMPULS Fuhrparkmanagement GmbH & Co. KG',
      id: 21,
    },
    {
      name: 'Schwabengarage GmbH',
      id: 22,
    },
    {
      name: 'SIXT Leasing Aktiengesellschaft',
      id: 23,
    },
    {
      name: t('other'),
      id: 24,
    },
  ];
};

export default LeasingCompanies;
