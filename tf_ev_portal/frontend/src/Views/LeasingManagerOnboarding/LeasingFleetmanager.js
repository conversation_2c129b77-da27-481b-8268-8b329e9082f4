import { useState } from 'react';
import styled from 'styled-components';
import { mailRegex, prohibitedDomains } from '../../utils/helper';
import { useTranslation } from 'react-i18next';
import { Error, Description } from '../../Components/evDriver/PageTemplate';
import AddBoxIcon from '@material-ui/icons/AddBox';
import { IconButton } from '@material-ui/core';
import { TextField } from '../../Components';
import { Checkbox } from '../../Components';
import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';

const TextCheckbox = styled.p`
  line-height: 24px;
  margin: 0;
  padding-left: 15px;
  cursor: pointer;
`;

const FleetManagerMailInputWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
`;

const Row = styled.div`
  display: flex;
  width: 100%;
`;

const IconButtonWrapper = styled.div`
  display: flex;
  margin-left: 0.5rem;
  justify-content: flex-start;
  align-items: flex-end;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const FleetManagersWrapper = styled.div`
  width: 100%;
  display: flex;
  margin-bottom: 2rem;
  flex-direction: column;
`;

const FleetManagerMail = styled.div`
  ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  height: 48px;
`;

const Spacer = styled.div`
  height: 30px;
`;

const Headline2 = styled.div`
  font-size: 16px;
  text-align: left;
  color: black;
  margin: 1rem 0 2rem 0;
  line-height: 20px;
  font-weight: bold;
  white-space: pre-line;
`;

const LeasingFleetManager = ({
  formState,
  setFormState,
  acceptedDPS,
  setAcceptedDPS,
}) => {
  const { t } = useTranslation('onboarding');
  const [fleetManagerMail, setFleetManagerMail] = useState({
    value: '',
    error: false,
  });
  const [duplicateEmail, setDuplicateEmail] = useState(null);

  const addFleetManagerMail = () => {
    if (formState.emails.value.indexOf(fleetManagerMail.value) !== -1) {
      setDuplicateEmail(formState.emails.value.indexOf(fleetManagerMail.value));
      return;
    }

    if (
      fleetManagerMail.value.match(mailRegex) &&
      prohibitedDomains.indexOf(
        fleetManagerMail.value.split('@')[1].split('.')[0],
      ) === -1
    ) {
      formState.emails.value.push(fleetManagerMail.value);
      setFleetManagerMail({ ...fleetManagerMail, value: '' });
    } else {
      setFleetManagerMail({ ...fleetManagerMail, error: true });
    }
  };

  return (
    <>
      <FleetManagersWrapper>
        {formState.emails.value.length === 0 && (
          <FleetManagerMail error={formState.emails.error}>
            {t('noFleetManagers')}
          </FleetManagerMail>
        )}
        {formState.emails.value.map((entry, index) => {
          return (
            <FleetManagerMail key={entry} error={duplicateEmail === index}>
              {entry}
              <IconButtonWrapper>
                <IconButton
                  onClick={() => {
                    setFormState({
                      ...formState,
                      emails: {
                        ...formState.emails,
                        value: formState.emails.value.filter(
                          (e) => e !== entry,
                        ),
                      },
                    });
                  }}
                >
                  <IndeterminateCheckBoxIcon />
                </IconButton>
              </IconButtonWrapper>
            </FleetManagerMail>
          );
        })}
      </FleetManagersWrapper>
      <Row>
        <FleetManagerMailInputWrapper>
          <TextField
            value={fleetManagerMail.value}
            onChange={(e) => {
              setFleetManagerMail({
                ...fleetManagerMail,
                value: e.target.value,
              });
            }}
            onFocus={(e) => {
              setFleetManagerMail({
                ...fleetManagerMail,
                error: false,
              });
              setDuplicateEmail(null);
            }}
            type="text"
            error={
              fleetManagerMail.error || duplicateEmail || duplicateEmail === 0
            }
            newDriver
            placeholder={t('emailPlaceholder')}
            label={t('emailLeasing')}
          />
        </FleetManagerMailInputWrapper>
        <IconButtonWrapper>
          <IconButton onClick={addFleetManagerMail}>
            <AddBoxIcon />
          </IconButton>
        </IconButtonWrapper>
      </Row>
      <Spacer />
      <Description>{t('fleetmanagersHint')}</Description>
      {fleetManagerMail.error && <Error>{t('emailError')}</Error>}
      {(duplicateEmail === 0 || duplicateEmail) && (
        <Error>{t('mailAlreadyExists')}</Error>
      )}
      <Spacer />
      <Headline2>{t('dataProtectionNotice')}</Headline2>
      <Row>
        <Checkbox
          id="dpsCheckBox"
          checked={acceptedDPS}
          onChange={() => setAcceptedDPS(!acceptedDPS)}
        />
        <TextCheckbox onClick={() => setAcceptedDPS(!acceptedDPS)}>
          {t('dataProtectionNoticeText')}
        </TextCheckbox>
      </Row>
    </>
  );
};

export default LeasingFleetManager;
