import styled from 'styled-components';

import IndeterminateCheckBoxIcon from '@material-ui/icons/IndeterminateCheckBox';
import AddBoxIcon from '@material-ui/icons/AddBox';

import { TextField } from '../../Components';
import { useTranslation } from 'react-i18next';
import { IconButton } from '@material-ui/core';

import { Row } from '../HomeChargingAustria/Styles';
import Select from '../../Components/Select';
import {
  Placeholder,
  StyledMenuItem,
} from '../../Components/evDriver/PageTemplate';
import LeasingCompanies from './LeasingCompanies';

const FleetManagerMail = styled.div`
  ${(props) => (props.error === true ? 'color: var(--error-color)' : '')};
  display: flex;
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  height: 48px;
`;

const CompanyIdInputWrapper = styled.div`
  display: flex;
  flex-direction: column;
  input {
    width: 112px;
    margin-right: 1rem;
  }
  margin-bottom: 1rem;
`;

const IconButtonWrapper = styled.div`
  display: flex;
  margin-left: 0.5rem;
  justify-content: flex-start;
  align-items: flex-end;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const CompanyIconButtonContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  margin-bottom: -9px;
  align-items: center;
  button {
    width: auto;
    :disabled {
      svg {
        color: gray;
      }
    }
  }
  svg {
    font-size: 1.5rem;
    color: var(--trafineo-rot-100);
  }
`;

const ColoredAddBoxIcon = styled(AddBoxIcon)`
  color: var(--trafineo-rot-100);
  margin-bottom: -5px;
`;

const AddCompanyHint = styled.p`
  display: inline-block;
  font-weight: 600;
  text-align: left;
`;

const InputWrapper = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  margin-bottom: 2rem;
`;

const Hint = styled.div`
  color: rgb(102, 102, 102);
  text-align: left;
  font-weight: 400;
  font-size: 12px;
  padding-top: 0.25rem;
  margin-bottom: 1rem;
`;

const ErrorCompanyId = styled.div`
  color: var(--error-color);
  display: ${(props) => (props.display ? 'flex' : 'none')};
  justify-content: flex-start;
  align-items: center;
  font-size: 14px;
  font-weight: 400;
  height: 48px;
`;

const FleetManagersWrapper = styled.div`
  width: 100%;
  display: flex;
  margin-bottom: 2rem;
  flex-direction: column;
`;

const LeasingCompanyInfo = ({
  companies,
  setCompanies,
  companyId,
  setCompanyId,
  index,
}) => {
  const { t } = useTranslation('onboarding');

  //defines prefix for all companycompany_ids. Added for better scaling and modification if needed
  const companyIdPrefix = '700674';

  const changeCompanyId = (value) => {
    if (
      value.length >= 6 &&
      value.length <= 12 &&
      value.slice(0, 6) === companyIdPrefix
    ) {
      setCompanyId({
        ...companyId,
        [index]: {
          ...companyId[index],
          value: value,
          error: false,
        },
      });
    } else {
      if (value.length > 12) {
        setCompanyId({
          ...companyId,
          [index]: {
            ...companyId[index],
            error: 'maximalLengthExceeded',
          },
        });
      } else {
        setCompanyId({
          ...companyId,
          [index]: {
            ...companyId[index],
            error: true,
          },
        });
      }
    }
  };

  const changeName = (e) => {
    let newArr = [...companies];
    newArr[index].name.value = e.target.value;
    setCompanies(newArr);
  };

  const resetError = () => {
    let newArr = [...companies];
    newArr[index].name.error = false;
    setCompanies(newArr);
  };

  const addCompanyId = () => {
    let isNum = /^\d+$/.test(companyId[index].value);
    let alreadyExists = false;

    if (companies[index].company_ids.length > 0) {
      companies[index].company_ids.forEach((id) => {
        if (companies[index].company_ids.includes(companyId[index].value)) {
          alreadyExists = true;
        }
      });
    }

    if (
      !isNum ||
      companyId[index].value.length !== 12 ||
      companyId[index].value === '' ||
      alreadyExists
    ) {
      setCompanyId({
        ...companyId,
        [index]: {
          ...companyId[index],
          error: true,
        },
      });
    } else {
      let newArr = [...companies];
      newArr[index].company_ids.push(companyId[index].value);
      setCompanies(newArr);
      setCompanyId({
        ...companyId,
        [index]: { value: '', error: false },
      });
    }
  };

  const removeId = (e, entry) => {
    let newArr = [...companies];
    let newcompany_ids = newArr[index].company_ids.filter((e) => e !== entry);
    newArr[index].company_ids = newcompany_ids;
    setCompanies(newArr);
  };

  return (
    <>
      <Row>
        <InputWrapper>
          <Row>
            <InputWrapper fullWidth>
              <Select
                error={companies[index].name.error}
                onFocus={resetError}
                big
                onChange={(e) => changeName(e)}
                displayEmpty
                value={companies[index].name.value}
                renderValue={
                  companies[index].name.value !== ''
                    ? undefined
                    : () => <Placeholder>{t('companySelect')}</Placeholder>
                }
              >
                {LeasingCompanies().map((entry) => (
                  <StyledMenuItem
                    key={entry.id}
                    value={entry.id}
                    data-cy={`${entry}Select`}
                  >
                    {entry.name}
                  </StyledMenuItem>
                ))}
              </Select>
            </InputWrapper>
          </Row>
        </InputWrapper>
      </Row>

      <Row>
        <FleetManagersWrapper>
          {/* formstate */}
          {companies[index].company_ids.map((entry) => {
            return (
              <FleetManagerMail key={entry}>
                {entry}
                <IconButtonWrapper>
                  <IconButton
                    onClick={(e) => {
                      removeId(e, entry);
                    }}
                  >
                    <IndeterminateCheckBoxIcon />
                  </IconButton>
                </IconButtonWrapper>
              </FleetManagerMail>
            );
          })}
        </FleetManagersWrapper>
      </Row>
      <AddCompanyHint>
        {t('addCompanyHint')} <ColoredAddBoxIcon />
        {t('addCompanyHint2')}
      </AddCompanyHint>

      <Row>
        <CompanyIdInputWrapper>
          <TextField
            value={companyId[index].value || companyIdPrefix}
            onChange={(e) => {
              changeCompanyId(e.target.value);
            }}
            type="text"
            maxLength="13"
            newDriver
            disableMinWidth
            error={companyId[index].error}
            onFocus={() =>
              setCompanyId({
                ...companyId,
                [index]: {
                  ...companyId[index],
                  error: false,
                },
              })
            }
            label={t('companyId')}
          />
        </CompanyIdInputWrapper>
        <CompanyIconButtonContainer>
          <IconButton onClick={addCompanyId}>
            <AddBoxIcon />
          </IconButton>
        </CompanyIconButtonContainer>
      </Row>
      <Row>
        <Hint>{t('companyIdHint')}</Hint>
      </Row>
      <ErrorCompanyId
        error={companyId[index].error}
        display={companyId[index].error.length > 0}
      >
        {companyId[index].error === 'maximalLengthExceeded' &&
          t('maximalLengthExceeded')}
        {companyId[index].error === 'confirmOrRemoveInput' &&
          t('confirmOrRemoveInput')}
      </ErrorCompanyId>
    </>
  );
};

export default LeasingCompanyInfo;
