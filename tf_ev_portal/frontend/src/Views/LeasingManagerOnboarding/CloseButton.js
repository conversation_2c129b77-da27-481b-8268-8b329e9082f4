import styled from 'styled-components';
import PropTypes from 'prop-types';
import Close from '@material-ui/icons/Close';
import { IconButton } from '@material-ui/core';

const CloseButtonWrapper = styled.div`
  color: var(--default-text);
  width: 100%;
  justify-content: end;
  align-items: center;
  display: flex;
  right: 1rem;
  top: 1rem;
  z-index: 1;
  cursor: pointer;
  :hover {
    opacity: 0.7;
  }
`;

const CloseButton = ({ onClick }) => {
  return (
    <CloseButtonWrapper>
      <IconButton data-cy="closeButton" onClick={onClick}>
        <Close />
      </IconButton>
    </CloseButtonWrapper>
  );
};
CloseButton.propTypes = {
  onClick: PropTypes.func,
};
export default CloseButton;
