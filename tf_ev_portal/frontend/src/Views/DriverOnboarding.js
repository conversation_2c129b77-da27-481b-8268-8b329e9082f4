import { useState } from 'react';
import styled from 'styled-components';
import { getBranding, mailRegex } from '../utils/helper';
import { useTranslation } from 'react-i18next';
import RadioGroup from '@material-ui/core/RadioGroup';
import Radio from '@material-ui/core/Radio';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import requester from '../utils/requester';
import infoIcon from '../static/img/icons/Info_Icon.svg';

import { TextField, Button, Checkbox } from '../Components';
import moment from 'moment';

import { Background, Blank, Page } from './HomeChargingAustria/Styles';
import { FormControl } from '@material-ui/core';
import {
  Description,
  Error,
  InformationContainer,
  InformationIcon,
  InformationText,
  InputWrapper,
  Placeholder,
  Row,
  StyledMenuItem,
} from '../Components/evDriver/PageTemplate';
import SuccessMessage from '../Components/SuccessMessage';
import { useNavigate } from 'react-router-dom';
import CircularProgress from '../Components/CircularProgress';
import Select from '../Components/Select';
import CountryFlag from '../Components/evDriver/CountryFlag';
import { languages } from '../constants/localization';
import { isMobile } from 'react-device-detect';
import TagManager from 'react-gtm-module';
import HelpIcon from '@mui/icons-material/Help';
import DialogWrapper from '../Components/DialogWrapper';
import { RowBox } from '../Components/dashboard/RowTemplates';

const FailureMessageWrapper = styled.div`
  display: flex;
  flex-direction: column;
  a {
    color: var(--trafineo-rot-100);
    font-size: 12px;
    font-weight: bold;
    -webkit-text-decoration: underline;
    text-decoration: underline;
    text-align: left;
    margin: 0.5rem 0;
    font-size: 14px;
  }
`;

const ReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  list-style-type: circle;
  padding: 0;
  margin-top: 2px;
`;

const SubReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  list-style-type: square;
  padding: 0;
  margin-top: 2px;
  padding-left: 5%;
`;

const ColRadio = styled.div`
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-bottom: 2rem;
`;

const StaticValue = styled.div`
  text-align: left;
  margin-top: 0.5rem;
`;

export const Wrapper = styled.div`
  max-width: 600px;
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
  font-family: var(--font-family);
`;

const ButtonContainer = styled.div`
  margin: 2rem 0 4rem 0;
  display: flex;
  width: 100%;
  justify-content: flex-end;
  button {
    margin-left: 0.5rem;
    width: fit-content;
  }
`;

const StyledHelp = styled(HelpIcon)`
  font-size: 16px !important;
`;

export const Headline = styled.div`
  font-family: var(--font-family);
  font-size: ${(props) => (props.isMobile ? '25px' : '33px')};
  justify-content: ${(props) => (props.isMobile ? 'center' : 'flex-start')};
  margin: ${(props) =>
    props.backButton
      ? props.isMobile
        ? ' 0.5rem 0 1.5rem 0'
        : ' 0.5rem 0 2.5rem 0'
      : props.isMobile
      ? '1.5rem 0'
      : '2.5rem 0'};
  font-weight: bold;
  font-stretch: normal;
  display: flex;
  align-items: center;
  font-style: normal;
  letter-spacing: 0.07px;
  color: #000;
`;

export const Headline2 = styled.div`
  font-size: 16px;
  text-align: left;
  color: black;
  margin: 1rem 0 2rem 0;
  line-height: 20px;
  white-space: pre-line;
`;

const FailureMessage = styled.div`
  color: var(--default-text);
  white-space: pre-line;
  font-size: 14px;
  line-height: 21px;
  text-align: left;
  font-weight: 400;
`;

const StyledRadio = styled(Radio)`
  color: var(--default-text);
  svg {
    height: 20px;
  }
  &.Mui-checked {
    color: var(--trafineo-rot-100) !important;
    &:hover {
      background-color: transparent;
    }
  }
  &:hover {
    background-color: transparent;
  }
`;

const StyledFormControl = styled(FormControl)`
  .MuiTypography-body1 {
    color: var(--default-text);
    font-family: var(--font-family);
    font-size: 14px;
  }
`;

const HintWrapper = styled.div`
  cursor: pointer;
  display: inline-flex;
  color: var(--default-text);
`;

const DriverOnboarding = () => {
  const { t } = useTranslation('onboarding');
  const tLocalization = useTranslation('localization').t;
  const tActions = useTranslation('actions').t;
  const tEvDriver = useTranslation('evDriver').t;
  const tServiceProvider = useTranslation('serviceProvider').t;

  const [page, setPage] = useState(1);
  const [question, setQuestion] = useState('');
  const [rspMail, setRspMail] = useState('');
  const [isAppUser, setIsAppUser] = useState('');
  const [confirmData, setConfirmData] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [checkCardError, setCheckCardError] = useState(false);
  const [submitError, setSubmitError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [hintOpen, setHintOpen] = useState(false);
  const navigate = useNavigate();

  const [formState, setFormState] = useState({
    cardNumber: {
      value: '',
      error: false,
    },
    cardNumberConfirm: {
      value: '',
      error: false,
    },
    month: {
      value: '',
      error: false,
    },
    year: {
      value: '',
      error: false,
    },
    corporateMail: {
      value: '',
      error: false,
    },
    firstName: {
      value: '',
      error: false,
    },
    lastName: {
      value: '',
      error: false,
    },
    language: {
      value: '',
      error: false,
    },
  });

  const reasons = [
    'cardErrorReason1',
    'cardErrorReason2',
    'cardErrorReason3',
    'cardErrorReason4',
  ];

  // useEffect(() => {
  //   if (getBranding() === 'trafineo') {
  //     navigate('/error', { replace: true });
  //   }
  // }, [navigate]);

  const checkCard = async () => {
    const setError = () => {
      setIsLoading(false);
      setCheckCardError(true);
    };
    let valid = true;
    const tempFormState = JSON.parse(JSON.stringify(formState));

    if (
      tempFormState.cardNumber.value !== tempFormState.cardNumberConfirm.value
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumber: {
          ...tempFormState.cardNumber,
          error: true,
        },
      });
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumberConfirm: {
          ...tempFormState.cardNumberConfirm,
          error: true,
        },
      });
    }

    if (tempFormState.cardNumber.value.length !== 18) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumber: {
          ...tempFormState.cardNumber,
          error: true,
        },
      });
    }
    if (
      tempFormState.year.value.length !== 2 ||
      Number(tempFormState.year.value) < new Date().getYear() - 100
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        year: {
          ...tempFormState.year,
          error: true,
        },
      });
    }
    if (
      tempFormState.month.value.length !== 2 ||
      Number(tempFormState.month.value) > 12 ||
      Number(tempFormState.month.value) < 1
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        month: {
          ...tempFormState.month,
          error: true,
        },
      });
    }

    if (!valid) {
      setFormState(tempFormState);
      return;
    }
    setIsLoading(true);

    const yearMonth = '20' + formState.year.value + '-' + formState.month.value;
    const days = moment(yearMonth, 'YYYY-MM').daysInMonth();
    const fullExpiryDate = yearMonth + '-' + days;

    try {
      const rsp = await requester().get(
        `/cards/${formState.cardNumber.value}/availability/?expiry_date=${fullExpiryDate}`,
      );
      const { state, email, type } = rsp.data;
      if (!state || state === 'error') {
        setError();
        return;
      }
      if (type === 'indirect') {
        navigate(
          `/indirectDriverOnboarding?cardNumber=${formState.cardNumber.value}&expiryDate=${fullExpiryDate}`,
        );
      }
      if (email) {
        setRspMail(email);
        setIsAppUser(true);
      } else {
        setIsAppUser(false);
      }
      setCheckCardError(false);
      setIsLoading(false);
      setPage(2);
    } catch (err) {
      setError();
    }
  };

  const register = async () => {
    let valid = true;
    const tempFormState = JSON.parse(JSON.stringify(formState));

    Object.keys(tempFormState)
      .slice(isAppUser ? 7 : 4, 7)
      .forEach((e) => {
        if (formState[e].value.length === 0) {
          valid = false;
          Object.assign(tempFormState, {
            ...tempFormState,
            [e]: {
              ...tempFormState[e],
              error: true,
            },
          });
        }
      });
    if (!tempFormState.corporateMail.value.match(mailRegex)) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        corporateMail: {
          ...tempFormState.corporateMail,
          error: true,
        },
      });
    }

    if (!valid) {
      setFormState(tempFormState);
      return;
    }
    try {
      setIsLoading(true);
      const yearMonth =
        '20' + formState.year.value + '-' + formState.month.value;
      const days = moment(yearMonth, 'YYYY-MM').daysInMonth();
      const fullExpiryDate = yearMonth + '-' + days;
      await requester()({
        method: 'put',
        url: '/driver_management',
        data: {
          first_name: formState.firstName.value,
          last_name: formState.lastName.value,
          email: formState.corporateMail.value,
          card_number: formState.cardNumber.value,
          expiry_date: fullExpiryDate,
          language: formState.language.value,
          data_correctness_confirmed: true,
        },
      });
      setSubmitError(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (error) {
      setSubmitError(true);
      setIsLoading(false);
    }
  };

  if (page === 1)
    return (
      <>
        {hintOpen && (
          <DialogWrapper
            open={hintOpen}
            onClose={() => {
              TagManager.dataLayer({
                dataLayer: {
                  event: 'popup_interaction',
                  popup: {
                    name: `popup_card_error`,
                    interaction_type: 'close',
                  },
                },
              });
              setHintOpen(false);
            }}
            headline={t('cardErrorPopupHeader')}
            width="800px"
          >
            <RowBox></RowBox>

            {reasons.map((reason, i) => {
              return (
                <>
                  <ReasonText>{t(reason)}</ReasonText>
                  <SubReasonText>{t(reason + 'Sub1')}</SubReasonText>
                </>
              );
            })}
          </DialogWrapper>
        )}
        <Background>
          <Blank />
          <Wrapper>
            <Headline isMobile={isMobile}>{t('signUp')}</Headline>
            <Page>
              <>
                <ColRadio>
                  <Headline2 style={{ margin: '1rem 0 1rem 0' }}>
                    {t('driverQuestion1', {
                      branding: getBranding() === 'aral' ? 'Aral' : 'BP',
                    })}
                  </Headline2>
                  <StyledFormControl component="fieldset">
                    <RadioGroup
                      value={question}
                      onChange={(e) => {
                        setCheckCardError(false);
                        setQuestion(e.target.value === 'true');
                      }}
                    >
                      <FormControlLabel
                        control={<StyledRadio />}
                        value={true}
                        label={t('yes')}
                      />
                      <FormControlLabel
                        control={<StyledRadio />}
                        value={false}
                        label={t('no')}
                      />
                    </RadioGroup>
                  </StyledFormControl>
                </ColRadio>
              </>

              {question === true ? (
                <>
                  <Headline2>{t('fcCardInformation')}</Headline2>
                  <Row>
                    <InputWrapper fullWidth>
                      <TextField
                        value={formState.cardNumber.value}
                        error={formState.cardNumber.error}
                        type="text"
                        maxLength="18"
                        newDriver
                        autoFocus
                        label={t('fcCardnumber')}
                        placeholder={t('fcCardnumberPlaceholder')}
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            cardNumber: {
                              ...formState.cardNumber,
                              error: false,
                            },
                          })
                        }
                        onChange={(e) => {
                          const { value } = e.target;
                          if (value.match(/^\d+$/) || value === '') {
                            setFormState({
                              ...formState,
                              cardNumber: {
                                ...formState.cardNumber,
                                value: value,
                              },
                            });
                          }
                        }}
                      />
                    </InputWrapper>
                  </Row>
                  <Row>
                    <InputWrapper fullWidth>
                      <TextField
                        value={formState.cardNumberConfirm.value}
                        error={formState.cardNumberConfirm.error}
                        type="text"
                        maxLength="18"
                        newDriver
                        autoFocus
                        label={t('fcCardnumberConfirm')}
                        placeholder={t('fcCardnumberPlaceholder')}
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            cardNumberConfirm: {
                              ...formState.cardNumberConfirm,
                              error: false,
                            },
                          })
                        }
                        onChange={(e) => {
                          const { value } = e.target;
                          if (value.match(/^\d+$/) || value === '') {
                            setFormState({
                              ...formState,
                              cardNumberConfirm: {
                                ...formState.cardNumberConfirm,
                                value: value,
                              },
                            });
                          }
                        }}
                      />
                    </InputWrapper>
                  </Row>

                  {formState.cardNumber.error && (
                    <Row>
                      <Error>
                        {t('cardError')}{' '}
                        <HintWrapper
                          onClick={() => {
                            TagManager.dataLayer({
                              dataLayer: {
                                event: 'popup_interaction',
                                popup: {
                                  name: `popup_card_error`,
                                  interaction_type: 'open',
                                },
                              },
                            });
                            setHintOpen(true);
                          }}
                        >
                          <StyledHelp />
                        </HintWrapper>
                      </Error>
                    </Row>
                  )}
                  <div>
                    <Description
                      horizontal
                      error={formState.month.error || formState.month.year}
                    >
                      {t('expiryDate')}
                    </Description>
                  </div>
                  <Row>
                    <InputWrapper style={{ width: 40, marginRight: '0.5rem' }}>
                      <TextField
                        value={formState.month.value}
                        error={formState.month.error}
                        disableMinWidth
                        style={{ textAlign: 'center' }}
                        type="text"
                        maxLength="2"
                        newDriver
                        placeholder={t('MM')}
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            month: {
                              ...formState.month,
                              error: false,
                            },
                          })
                        }
                        onChange={(e) => {
                          const { value } = e.target;
                          if (value.match(/^\d+$/) || value === '') {
                            setFormState({
                              ...formState,
                              month: {
                                ...formState.month,
                                value: value,
                              },
                            });
                          }
                        }}
                      />
                    </InputWrapper>
                    <InputWrapper style={{ width: 40 }}>
                      <TextField
                        value={formState.year.value}
                        error={formState.year.error}
                        disableMinWidth
                        style={{ textAlign: 'center' }}
                        type="text"
                        maxLength="2"
                        placeholder={t('YY')}
                        newDriver
                        onFocus={() =>
                          setFormState({
                            ...formState,
                            year: {
                              ...formState.year,
                              error: false,
                            },
                          })
                        }
                        onChange={(e) => {
                          const { value } = e.target;
                          if (value.match(/^\d+$/) || value === '') {
                            setFormState({
                              ...formState,
                              year: {
                                ...formState.year,
                                value: value,
                              },
                            });
                          }
                        }}
                      />
                    </InputWrapper>
                  </Row>
                </>
              ) : (
                question === false && (
                  <FailureMessageWrapper>
                    <FailureMessage>{t('FailureMessage1')}</FailureMessage>
                    {getBranding() === 'aral' ? (
                      <a href="https://www.aral.de/de/global/fleet_solutions/aral-tankkarten/aral-fuel-und-charge.html">
                        {tLocalization(`deCountryName`)}
                      </a>
                    ) : (
                      <>
                        <a href="https://www.bp.com/nl_nl/netherlands/home/<USER>/wagenparkoplossingen/bp-fuel-charge.html">
                          {tLocalization(`nlCountryName`)}
                        </a>
                        <a href="https://www.bp.com/de_at/austria/home/<USER>/bp-flottenlosungen/mobilitaetsloesungen/kraftstoff-ladung.html">
                          {tLocalization(`atCountryName`)}
                        </a>
                      </>
                    )}
                  </FailureMessageWrapper>
                )
              )}
              <Row>
                {checkCardError && <Error>{t('checkCardError')}</Error>}
              </Row>
              <Row>
                <ButtonContainer>
                  {(isLoading && <CircularProgress />) ||
                    (question === true && (
                      <>
                        <Button variant="primary" onClick={checkCard}>
                          {t('continue')}
                        </Button>
                      </>
                    ))}
                </ButtonContainer>
              </Row>
            </Page>
          </Wrapper>
        </Background>
      </>
    );

  return (
    <>
      <Blank />
      <Background>
        <Wrapper>
          {showSuccess ? (
            <SuccessMessage
              message={t(
                isAppUser
                  ? 'appDriverOnboardingSucess'
                  : 'driverOnboardingSucess',
              )}
            />
          ) : (
            <>
              <Headline>{t('signUp')}</Headline>
              <Page>
                <Headline2>{t('addInformation')}</Headline2>
                {!isAppUser ? (
                  <>
                    <Row>
                      <InputWrapper style={{ marginRight: '0.5rem' }}>
                        <TextField
                          newDriver
                          disableMinWidth
                          error={formState.firstName.error}
                          onFocus={() =>
                            setFormState({
                              ...formState,
                              firstName: {
                                ...formState.firstName,
                                error: false,
                              },
                            })
                          }
                          data-cy="firstNameInput"
                          value={formState.firstName.value}
                          placeholder={t('firstNamePlaceholder')}
                          label={t('firstName')}
                          name="firstName"
                          onChange={(e) => {
                            setFormState({
                              ...formState,
                              firstName: {
                                ...formState.firstName,
                                value: e.target.value,
                              },
                            });
                          }}
                        />
                      </InputWrapper>
                      <InputWrapper style={{ marginLeft: '0.5rem' }}>
                        <TextField
                          disableMinWidth
                          newDriver
                          error={formState.lastName.error}
                          onFocus={() =>
                            setFormState({
                              ...formState,
                              lastName: {
                                ...formState.lastName,
                                error: false,
                              },
                            })
                          }
                          data-cy="lastNameInput"
                          value={formState.lastName.value}
                          placeholder={t('lastNamePlaceholder')}
                          label={t('lastName')}
                          name="lastName"
                          onChange={(e) => {
                            setFormState({
                              ...formState,
                              lastName: {
                                ...formState.lastName,
                                value: e.target.value,
                              },
                            });
                          }}
                        />
                      </InputWrapper>
                    </Row>
                  </>
                ) : (
                  <Row>
                    <InputWrapper fullWidth>
                      <Description>{t('appMail')}</Description>
                      <StaticValue>{rspMail}</StaticValue>
                      <InformationContainer style={{ marginTop: '1rem' }}>
                        <InformationIcon>
                          <img src={infoIcon} alt="success" />
                        </InformationIcon>
                        <InformationText>{t('appUserHint')}</InformationText>
                      </InformationContainer>
                    </InputWrapper>
                  </Row>
                )}

                <Row>
                  <InputWrapper fullWidth>
                    <TextField
                      newDriver
                      error={formState.corporateMail.error}
                      onFocus={() =>
                        setFormState({
                          ...formState,
                          corporateMail: {
                            ...formState.corporateMail,
                            error: false,
                          },
                        })
                      }
                      data-cy="MailInput"
                      value={formState.corporateMail.value}
                      placeholder={
                        isAppUser
                          ? t('appMailPlaceholder')
                          : t('mailPlaceholder')
                      }
                      label={isAppUser ? t('appMailConfirm') : t('mail')}
                      name="mail"
                      onChange={(e) => {
                        setFormState({
                          ...formState,
                          corporateMail: {
                            ...formState.corporateMail,
                            value: e.target.value,
                          },
                        });
                      }}
                    />
                    {formState.corporateMail.error && (
                      <Error>{t('normalMailError')}</Error>
                    )}
                  </InputWrapper>
                </Row>
                <Row>
                  <InputWrapper fullWidth>
                    <Description error={formState.language.error}>
                      {t('language')}
                    </Description>
                    <Select
                      big
                      error={formState.language.error}
                      onFocus={() =>
                        setFormState({
                          ...formState,
                          language: {
                            ...formState.language,
                            error: false,
                          },
                        })
                      }
                      onChange={(e) => {
                        setFormState({
                          ...formState,
                          language: {
                            ...formState.language,
                            value: e.target.value,
                          },
                        });
                      }}
                      displayEmpty
                      value={formState.language.value}
                      renderValue={
                        formState.language.value !== ''
                          ? undefined
                          : () => (
                              <Placeholder>
                                {tServiceProvider('inviteLanguagePlaceholder')}
                              </Placeholder>
                            )
                      }
                    >
                      {Object.keys(languages).map((entry) => (
                        <StyledMenuItem
                          key={entry}
                          value={entry}
                          data-cy={`${entry}Select`}
                        >
                          <CountryFlag country={entry} />
                          {tLocalization(`${entry}Language`)}
                        </StyledMenuItem>
                      ))}
                    </Select>
                  </InputWrapper>
                </Row>
                <Row style={{ alignItems: 'center' }}>
                  <Checkbox
                    style={{ marginLeft: '0.125rem' }}
                    id="confirmData"
                    checked={confirmData}
                    onChange={() => {
                      setConfirmData(!confirmData);
                    }}
                  />
                  <p style={{ marginLeft: '0.5rem' }}>
                    {tEvDriver('confirmDataText')}
                  </p>
                </Row>
                <Row>
                  {submitError && (
                    <Error>{tActions('generalRequestError')}</Error>
                  )}
                </Row>
                <Row>
                  <ButtonContainer>
                    {(isLoading && <CircularProgress />) || (
                      <Button
                        disabled={!confirmData}
                        variant="primary"
                        onClick={register}
                      >
                        {t('submit')}
                      </Button>
                    )}
                  </ButtonContainer>
                </Row>
              </Page>
            </>
          )}
        </Wrapper>
      </Background>
    </>
  );
};

export default DriverOnboarding;
