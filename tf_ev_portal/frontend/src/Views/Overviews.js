import { useEffect, useState, useCallback } from 'react';
import { useLocation, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { downloadExcel, formatDateTime } from '../utils/helper';
import Center from '../Components/helper/Center';
import requester from '../utils/requester';
import QuestionMark from '../Components/QuestionMark';
import logger from '../utils/logger';
import CircularProgress from '../Components/CircularProgress';
import Pagination from '../Components/dashboard/Pagination';
import DialogWrapper from '../Components/DialogWrapper';
import { Button, TextField } from '../Components';
import PageWrapper from '../Components/PageWrapper';
import qs from 'querystring';
import {
  Row,
  RowBox,
  RowContainer,
  TitleRow,
} from '../Components/dashboard/RowTemplates';
import {
  HeadlineBox,
  LoadingCircleContainer,
  StatusDiv,
  TableContainer,
  Wrapper,
} from '../Components/dashboard/DashboardTemplates';
import { ButtonWrapper } from './MyTeam';

import ReimbursementTable from '../Components/ReimbursementTable';
import DownloadMenu from '../Components/DownloadMenu';
import TagManager from 'react-gtm-module';
import styled from 'styled-components';
import HelpIcon from '@mui/icons-material/Help';

const HintWrapper = styled.div`
  cursor: pointer;
  padding-left: 5px;
`;

const HintHeader = styled.p`
  padding-left: 5px;
  font-weight: 600;
  font-size: 15px;
`;

const ReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  padding: 0;
  margin-top: 2px;
`;

const LayoutRow = styled.div`
  display: flex;
  flex-direction: row;
  width: ${(props) => (props.width ? props.width : '100%')};
  margin-bottom: ${(props) => (props.width ? '2rem' : '0')};
`;

const Overviews = () => {
  const { t } = useTranslation('overview');
  const tDirectCustomer = useTranslation('directCustomer').t;

  const pageSize = 7;
  const pageSizeHomeCharging = 20;
  const reimbursementMode =
    window.location.pathname.indexOf('/reimbursements') === 0;
  const homeChargingMode =
    window.location.pathname.indexOf('/home-charging') === 0;
  const [data, setData] = useState([]);
  const [recordCount, setRecordCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [dialogData, setDialogData] = useState([]);
  const [currentPage, setCurrentPage] = useState(0);
  const [customerNo, setCustomerNo] = useState('');
  const [hintOpen, setHintOpen] = useState(false);

  const { id } = useParams();
  const superUser = id;

  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { email } = parsedQueryString;

  const getData = useCallback(
    async (from, to, id, download = false) => {
      if (!download) {
        setIsLoading(true);
      }

      try {
        const rsp = (
          await requester().get(
            homeChargingMode
              ? `/sales/leads?offset=${from}&limit=${to}${
                  id.length > 0 ? `&customer_no=${id}` : ''
                }`
              : `/Information_Overview/${
                  reimbursementMode ? 'reimbursement' : 'charging_sessions'
                }${id ? `/${id}` : ''}?from=${from + 1}&to=${to + 1}`,
          )
        ).data;
        setIsLoading(false);
        return rsp;
      } catch (err) {
        setIsLoading(false);
        logger().error(`Couldn't get data from api.\n${err.message}`);
        return undefined;
      }
    },
    [homeChargingMode, reimbursementMode],
  );

  useEffect(() => {
    const fetchData = async () => {
      const paginationFrom = currentPage * pageSize;
      let rspData = null;
      if (homeChargingMode) {
        rspData = await getData(
          currentPage * pageSizeHomeCharging,
          pageSizeHomeCharging,
          customerNo,
        );
      } else {
        rspData = await getData(
          paginationFrom === 0 ? 0 : paginationFrom,
          paginationFrom + pageSize - 1,
          id,
        );
      }

      if (rspData) {
        setData(
          rspData.reimbursments
            ? rspData.reimbursments
            : rspData.sales_leads
            ? rspData.sales_leads
            : rspData.charging_sessions,
        );
        setRecordCount(rspData.total_amount);
      }
    };
    fetchData();
  }, [customerNo, currentPage, getData, homeChargingMode, id]);

  const onClose = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'popup_interaction',
        popup: {
          name: `popup_reimbursementChargingSessions`,
          interaction_type: 'close',
        },
      },
    });
    setIsDialogOpen(false);
  };

  const Status = ({ statusId }) => {
    const yellowStatus = [1, 6, 7, 8];
    const greenStatus = [11];
    const whiteStatus = [12];
    let statusColor = '#e22525';
    if (yellowStatus.indexOf(statusId) !== -1) {
      statusColor = '#fbfb12';
    } else if (greenStatus.indexOf(statusId) !== -1) {
      statusColor = '#3fe03f';
    } else if (whiteStatus.indexOf(statusId) !== -1) {
      statusColor = '#FFFFFF';
    }
    return (
      <StatusDiv
        title={t(`reimbursementStatus${statusId}`)}
        style={{ background: `${statusColor}` }}
      />
    );
  };

  const redReasons = [
    t('chargingSessionHintRed1'),
    t('chargingSessionHintRed2'),
    t('chargingSessionHintRed3'),
    t('chargingSessionHintRed4'),
    t('chargingSessionHintRed5'),
    t('chargingSessionHintRed6'),
  ];

  const TitleRowWrapper = ({ reimbMode, homeChargingMode, short }) => {
    if (homeChargingMode) {
      return (
        <TitleRow>
          <HeadlineBox>
            <RowBox bold center width={16.6}>
              {t('company')}
            </RowBox>
            <RowBox bold center width={16.6}>
              {t('email')}
            </RowBox>
            <RowBox bold center width={16.6}>
              <TextField
                placeholder={t('customerNo')}
                value={customerNo}
                type="text"
                autoFocus
                search
                onChange={(e) => setCustomerNo(e.target.value)}
              />
            </RowBox>
            <RowBox bold center width={16.6}>
              {t('date')}
            </RowBox>
            <RowBox bold center width={16.6}>
              {t('source')}
            </RowBox>
            <RowBox bold center width={16.6}>
              {t('wallboxType')}
            </RowBox>
          </HeadlineBox>
        </TitleRow>
      );
    }
    if (reimbMode) {
      if (short) {
        return (
          <TitleRow>
            <HeadlineBox>
              <RowBox bold center width={25}>
                {t('datePaid')}
              </RowBox>
              <RowBox bold center width={25}>
                {t('paidAmmount')}
              </RowBox>
              <RowBox bold center width={25}>
                {t('appliedTariff')}
                <QuestionMark tooltip={{ content: t('appliedTariffHint') }} />
              </RowBox>
              <RowBox bold center width={25}>
                {t('consumption')}
              </RowBox>
            </HeadlineBox>
          </TitleRow>
        );
      }
      return (
        <TitleRow>
          <HeadlineBox>
            <RowBox bold center width={20}>
              {t('datePaid')}
            </RowBox>
            <RowBox bold center width={20}>
              {t('paidAmmount')}
            </RowBox>
            <RowBox bold center width={20}>
              {t('appliedTariff')}
              <QuestionMark tooltip={{ content: t('appliedTariffHint') }} />
            </RowBox>
            <RowBox bold center width={20}>
              {t('consumption')}
            </RowBox>
            <RowBox bold center width={20} />
          </HeadlineBox>
        </TitleRow>
      );
    }
    if (short) {
      return (
        <TitleRow>
          {hintOpen && (
            <DialogWrapper
              open={hintOpen}
              onClose={() => {
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'popup_interaction',
                    popup: {
                      name: `popup_charging_status`,
                      interaction_type: 'close',
                    },
                  },
                });
                setHintOpen(false);
              }}
              headline={t('chargingSessionStatusHeader')}
              width="800px"
            >
              <RowBox>
                <Status statusId={12}></Status>
                <HintHeader>{t('chargingSessionHintWhiteHeader')}</HintHeader>
              </RowBox>
              <RowBox>
                <p>{t('chargingSessionHintWhiteText')}</p>
              </RowBox>
              <RowBox>
                <Status statusId={1}></Status>
                <HintHeader>{t('chargingSessionHintYellowHeader')}</HintHeader>
              </RowBox>
              <RowBox>
                <p>{t('chargingSessionHintYellowText')}</p>
              </RowBox>
              <RowBox>
                <Status statusId={11}></Status>
                <HintHeader>{t('chargingSessionHintGreenHeader')}</HintHeader>
              </RowBox>
              <RowBox>
                <p>{t('chargingSessionHintGreenText')}</p>
              </RowBox>
              <RowBox>
                <Status statusId={2}></Status>
                <HintHeader>{t('chargingSessionHintRedHeader')}</HintHeader>
              </RowBox>
              <LayoutRow>
                <p>{t('chargingSessionHintRedText')}</p>
              </LayoutRow>
              {redReasons.map((translation, i) => {
                return (
                  <LayoutRow>
                    <ReasonText>{translation}</ReasonText>
                  </LayoutRow>
                );
              })}
            </DialogWrapper>
          )}
          <HeadlineBox>
            <RowBox bold center width={7}>
              {t('status')}
              <HintWrapper
                onClick={() => {
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'popup_interaction',
                      popup: {
                        name: `popup_charging_status`,
                        interaction_type: 'open',
                      },
                    },
                  });
                  setHintOpen(true);
                }}
              >
                <HelpIcon />
              </HintWrapper>
            </RowBox>
            <RowBox bold center width={18}>
              {t('cardNumber')}
            </RowBox>
            <RowBox bold center width={20}>
              {t('evse')}
            </RowBox>
            <RowBox bold center width={15}>
              {t('startTime')}
            </RowBox>
            <RowBox bold center width={15}>
              {t('stopTime')}
            </RowBox>
            <RowBox bold center width={15}>
              {t('totalEnergy')}
            </RowBox>
            <RowBox bold center width={10}>
              {t('totalCost')}
            </RowBox>
          </HeadlineBox>
        </TitleRow>
      );
    }
    return (
      <TitleRow>
        <HeadlineBox>
          <RowBox bold center width={7}>
            {t('status')}
            <HintWrapper
              onClick={() => {
                TagManager.dataLayer({
                  dataLayer: {
                    event: 'popup_interaction',
                    popup: {
                      name: `popup_charging_status`,
                      interaction_type: 'open',
                    },
                  },
                });
                setHintOpen(true);
              }}
            >
              <HelpIcon />
            </HintWrapper>
          </RowBox>
          <RowBox bold center width={18}>
            {t('cardNumber')}
          </RowBox>
          <RowBox bold center width={19}>
            {t('evse')}
          </RowBox>
          <RowBox bold center width={19}>
            {t('startTime')}
          </RowBox>
          <RowBox bold center width={19}>
            {t('stopTime')}
          </RowBox>
          <RowBox bold center width={19}>
            {t('totalEnergy')}
          </RowBox>
        </HeadlineBox>
      </TitleRow>
    );
  };

  const DataRow = ({ rowData, index, reimbMode, homeChargingMode, short }) => {
    if (homeChargingMode) {
      return (
        <Row>
          <RowContainer>
            <RowBox center width={16.6}>
              {rowData.company}
            </RowBox>
            <RowBox center width={16.6}>
              {rowData.email}
            </RowBox>
            <RowBox center width={16.6}>
              {rowData.customer_no}
            </RowBox>
            <RowBox center width={16.6}>
              {formatDateTime(rowData.submission_date)}
            </RowBox>
            <RowBox center width={16.6}>
              {rowData.lead_source}
            </RowBox>
            <RowBox center width={16.6}>
              {rowData.wallbox_type}
            </RowBox>
          </RowContainer>
        </Row>
      );
    }
    if (reimbMode) {
      if (short) {
        return (
          <Row>
            <RowContainer>
              <RowBox center width={25}>
                {formatDateTime(rowData.date_paid)}
              </RowBox>
              <RowBox center width={25}>
                {rowData.paid_amount}
              </RowBox>
              <RowBox center width={25}>
                {rowData.applied_tariff}
              </RowBox>
              <RowBox center width={25}>
                {rowData.consumption}
              </RowBox>
            </RowContainer>
          </Row>
        );
      }
      return (
        <Row>
          <RowContainer>
            <RowBox center width={20}>
              {formatDateTime(rowData.date_paid)}
            </RowBox>
            <RowBox center width={20}>
              {rowData.paid_amount}
            </RowBox>
            <RowBox center width={20}>
              {rowData.applied_tariff}
            </RowBox>
            <RowBox center width={20}>
              {rowData.consumption}
            </RowBox>
            <RowBox center width={19}>
              <Button
                index={index}
                onClick={() => {
                  TagManager.dataLayer({
                    dataLayer: {
                      event: 'popup_interaction',
                      popup: {
                        name: `popup_reimbursementChargingSessions`,
                        interaction_type: 'open',
                      },
                    },
                  });
                  setDialogData(data[index]);
                  setIsDialogOpen(true);
                }}
                variant="primary"
              >
                {t('viewReimbursedChargingSessions')}
              </Button>
            </RowBox>
            <RowBox center width={1}></RowBox>
          </RowContainer>
        </Row>
      );
    }
    if (short) {
      return (
        <Row>
          <RowContainer>
            <RowBox center width={5}>
              <Status statusId={rowData.status_id} />
            </RowBox>
            <RowBox center width={20}>
              {rowData.card_number}
            </RowBox>
            <RowBox center width={20}>
              {rowData.evse_id}
            </RowBox>
            <RowBox center width={15}>
              {formatDateTime(rowData.cdr_session_start)}
            </RowBox>
            <RowBox center width={15}>
              {formatDateTime(rowData.cdr_session_end)}
            </RowBox>
            <RowBox center width={15}>
              {rowData.cdr_total_energy}
            </RowBox>
            <RowBox center width={10}>
              {rowData.cdr_total_cost}
            </RowBox>
          </RowContainer>
        </Row>
      );
    }
    return (
      <Row>
        <RowContainer>
          <RowBox center width={5.2}>
            <Status statusId={rowData.status_id} />
          </RowBox>
          <RowBox center width={19}>
            {rowData.card_number}
          </RowBox>
          <RowBox center width={19}>
            {rowData.evse_id}
          </RowBox>
          <RowBox center width={19}>
            {formatDateTime(rowData.session_start)}
          </RowBox>
          <RowBox center width={19}>
            {formatDateTime(rowData.session_end)}
          </RowBox>
          <RowBox center width={19}>
            {rowData.cdr_total_energy}
          </RowBox>
        </RowContainer>
      </Row>
    );
  };

  const HeadLine = ({ reimbMode, homeChargingMode }) => {
    let headline = '';
    if (homeChargingMode) {
      headline = 'homeChargingHeadline';
    } else if (reimbMode) {
      if (superUser) {
        headline = 'reimbursementsForUser';
      } else {
        headline = 'myReimbursements';
      }
    } else if (superUser) {
      headline = 'chargingSessionsForUser';
    } else {
      headline = 'myChargingSessions';
    }

    return `${t(headline)}  ${email || ''}`;
  };

  const onDownloadClick = async () => {
    if (data.length === 0) {
      return;
    }
    const rspData = await getData(
      0,
      recordCount,
      homeChargingMode ? customerNo : id,
    );
    if (rspData) {
      const rows = JSON.parse(JSON.stringify(rspData.sales_leads)).map(
        (row) => {
          return {
            [t('company')]: row.company,
            [t('email')]: row.email,
            [t('customerNo')]: row.customer_no + '',
            [t('date')]: formatDateTime(row.submission_date),
            [t('source')]: row.lead_source,
            [t('wallboxType')]: row.wallbox_type,
          };
        },
      );
      downloadExcel(`home_charging.xlsx`, rows);
    }
  };

  const onReimbursementExcelDownloadClick = async (data) => {
    const rows = JSON.parse(JSON.stringify(data)).map((row) => {
      const transformedRow = {
        [t('datePaid')]: formatDateTime(row.date_paid),
        [t('paidAmmount')]: row.paid_amount,
        [t('appliedTariff')]: row.applied_tariff,
        [t('consumption')]: row.consumption,
      };

      return transformedRow;
    });
    downloadExcel(`reimbursements.xlsx`, rows);
  };

  const getReimbursementDownloadData = async () => {
    const rspData = await getData(
      0,
      recordCount,
      homeChargingMode ? customerNo : id,
      true,
    );
    return rspData?.reimbursments;
  };

  const Table = ({ tableData, reimbMode, homeChargingMode, short }) => {
    return (
      <Wrapper>
        <TableContainer>
          <TitleRowWrapper
            short={short}
            homeChargingMode={homeChargingMode}
            reimbMode={reimbMode}
          />
          {(isLoading && (
            <LoadingCircleContainer>
              <CircularProgress />
            </LoadingCircleContainer>
          )) || (
            <div>
              {tableData &&
                tableData.map((row, i) => (
                  <DataRow
                    short={short}
                    reimbMode={reimbMode}
                    homeChargingMode={homeChargingMode}
                    rowData={row}
                    key={row.IdpId}
                    index={i}
                  />
                ))}
            </div>
          )}
          {!isLoading && (!tableData || recordCount === 0) && (
            <Center>
              <h2>{tDirectCustomer('noDataAvailable')}</h2>
            </Center>
          )}
        </TableContainer>
      </Wrapper>
    );
  };

  const ChargingSessionsDialog = () => {
    return (
      <DialogWrapper
        open={isDialogOpen}
        onClose={onClose}
        width="1300px"
        headline={t('ChargingSessionsForReimbusementHeadline')}
      >
        <Table reimbMode short tableData={[dialogData]} />
        <Table
          reimbMode={false}
          short
          tableData={dialogData.charging_session}
        />
      </DialogWrapper>
    );
  };

  return (
    <PageWrapper
      backButton
      minWidth="1230px"
      title={
        <HeadLine
          reimbMode={reimbursementMode}
          homeChargingMode={homeChargingMode}
        />
      }
    >
      <ChargingSessionsDialog />
      <ButtonWrapper>
        {reimbursementMode ? (
          superUser ? (
            <DownloadMenu
              onExcelDownloadClick={onReimbursementExcelDownloadClick}
              getDownloadData={getReimbursementDownloadData}
              disabled={data?.length === 0 || !data}
              pdfFileName="charging_sessions.pdf"
              PdfTable={ReimbursementTable}
            />
          ) : (
            <a
              href={
                superUser ? `/payouts/${superUser}?mail=${email}` : '/payouts'
              }
            >
              <Button>{tDirectCustomer('payoutDownload')}</Button>
            </a>
          )
        ) : (
          <Button
            disabled={data?.length === 0 || !data}
            onClick={onDownloadClick}
            variant="primary"
          >
            {tDirectCustomer('downloadData')}
          </Button>
        )}
      </ButtonWrapper>
      <Table
        reimbMode={reimbursementMode}
        homeChargingMode={homeChargingMode}
        tableData={data}
      />
      {!isLoading && data && recordCount !== 0 && (
        <Center>
          <Pagination
            onChange={(pos) => setCurrentPage(pos)}
            position={currentPage}
            count={recordCount}
            pageSize={homeChargingMode ? pageSizeHomeCharging : pageSize}
          />
        </Center>
      )}
    </PageWrapper>
  );
};

export default Overviews;
