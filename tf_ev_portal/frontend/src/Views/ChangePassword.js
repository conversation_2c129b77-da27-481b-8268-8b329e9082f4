import { useState, useContext, useEffect } from 'react';
import { IconButton } from '@material-ui/core';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation, Trans } from 'react-i18next';
import qs from 'querystring';
import JwtDecode from 'jwt-decode';
import { TextField, Button, Checkbox } from '../Components';
import StaticDialog from '../Components/changePassword/StaticDialog';
import requester, { idpRequest } from '../utils/requester';
import { userContext } from '../ContextProvider';
import logger from '../utils/logger';
import { getLoginRequestBody } from './Login';
import SuccessDialog from '../Components/SuccessDialog';
import { getBranding } from '../utils/helper';
import PageWrapper from '../Components/PageWrapper';
import {
  PasswordCriteria,
  TextFieldWrapper,
} from '../Components/profile/ChangePasswordDialog';
import { <PERSON><PERSON>Wrapper, <PERSON><PERSON><PERSON>, <PERSON> } from '../Components/evDriver/PageTemplate';
import { Visibility, VisibilityOff } from '@material-ui/icons';
import styled from 'styled-components';
import { isMobile } from 'react-device-detect';

const TermsRow = styled.div`
  text-align: left;
  display: flex;
  align-items: center;
  p {
    padding-left: 0.5rem;
  }
  a {
    color: var(--trafineo-rot-100);
    font-weight: bold;
  }
`;
const TermsContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
`;

const ChangePassword = () => {
  const { user, logOut, setLang } = useContext(userContext);

  useEffect(() => {
    if (user) {
      logOut();
    }
  }, [user, logOut]);

  const { t } = useTranslation('managePassword');
  const tProfile = useTranslation('profile').t;
  const tActions = useTranslation('actions').t;
  const navigate = useNavigate();
  const location = useLocation();
  const [flowType, setFlowType] = useState(0);
  const [key, setKey] = useState(0);
  const [role, setRole] = useState('');
  const [mail, setMail] = useState('');
  const [isTokenExpired, setIsTokenExpired] = useState(false);

  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);

  const checkIfTokenIsExpired = (keyToCheck) => {
    const isExpired = Number(keyToCheck.exp) * 1000 < Date.parse(new Date());
    setIsTokenExpired(isExpired);
    return isExpired;
  };

  if (!parsedQueryString?.key) {
    navigate('/');
  }
  useEffect(() => {
    try {
      const decodedKey = JwtDecode(parsedQueryString.key);
      setKey(decodedKey);
      checkIfTokenIsExpired(decodedKey);
      const { role: roler, locale, email, eml } = decodedKey;
      if (locale) {
        setLang(locale);
      }
      setRole(roler);
      setMail(email || eml);
      if (decodedKey.typ === 'reset-credentials') {
        setFlowType(2);
      } else if (decodedKey.typ === 'execute-actions-invite') {
        setFlowType(1);
      } else {
        navigate('/');
      }
    } catch (err) {
      logger(true).error(`JWT Token is not valid`);
      navigate('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigate, parsedQueryString.key]);

  // checkboxes for Terms and Private Policy
  const [policy, setPolicy] = useState(false);
  const [terms, setTerms] = useState(false);

  const [newPassword, setNewPassword] = useState('');
  const [newPasswordConfirm, setNewPasswordConfirm] = useState('');
  const [newPwVisible, setNewPwVisible] = useState(false);
  const [newPwRepeatVisible, setNewPwRepeatVisible] = useState(false);

  // visibility of dialogs
  const [isTermsDialogVisible, setIsTermsDialogVisible] = useState(false);
  const [isPolicyDialogVisible, setIsPolicyDialogVisible] = useState(false);
  const [hasFailedSetPassword, setHasFailedSetPassword] = useState(false);
  const [errorMessage, setErrorMessage] = useState(
    tActions('generalRequestError'),
  );
  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);
  const [isInfoDialogVisible, setIsInfoDialogVisible] = useState(false);

  const resetFlags = () => {
    setHasFailedSetPassword(false);
  };

  const validateAllCritiriaAreMet = () => {
    if (checkIfTokenIsExpired(key)) {
      return false;
    }
    // check for min 8 chars 1 digit 1 special charakter
    if (!newPassword.match(/^(?=.*\d)(?=.*[@$!%*?&])(?=.*[a-zA-Z]).{8,}$/gm)) {
      return false;
    }
    // check if new pw was entered the same twice
    if (newPassword !== newPasswordConfirm) {
      return false;
    }
    if (flowType === 1 && !policy) {
      return false;
    }
    if (role === 'FleetManager_direct' && !terms) {
      return false;
    }
    return true;
  };

  const onPwReset = async () => {
    const { typ } = key;
    // reset old flags if existing
    resetFlags();
    if (!validateAllCritiriaAreMet()) {
      setNewPassword('');
      setNewPasswordConfirm('');
      return;
    }

    const saveAcceptenceToBackend = async (email, password) => {
      const decodedKey = JwtDecode(parsedQueryString.key);
      try {
        const { data: access_token } = await idpRequest().post(
          process.env.REACT_APP_KEYCLOAK_TOKEN_URI,
          qs.stringify(getLoginRequestBody(email || decodedKey.eml, password)),
          {
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );
        window.localStorage.setItem('user-token', JSON.stringify(access_token));
        window.localStorage.setItem('user-is-logged-in', JSON.stringify(true));
        await requester().post(`/Policies/Post_PoliciesAccepted`, {});
        window.location.href = '/';
      } catch (e) {
        logger().error(e.message);
      }
    };

    const req = () =>
      idpRequest(true).get(
        `/login-actions/action-token?key=${parsedQueryString.key}`,
        {
          headers: {
            PASSWORD: encodeURIComponent(newPassword),
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      );
    try {
      if (typ === 'execute-actions-invite') {
        // dont make idp request if token has wrong type
        const {
          data: { email, password },
        } = await req(key);
        if (email === 'verifed' || password === 'set') {
          await saveAcceptenceToBackend(key.email, newPassword);
        } else {
          throw new Error('Wrong response from IDP.');
        }
      } else if (typ === 'reset-credentials') {
        try {
          await req(key);
          if (true) {
            // TODO: check if response is correct, currently because of workaround not possible
            setIsStatusDialogVisible(true);
          } else {
            throw new Error('Wrong response from IDP.');
          }
        } catch (err) {
          // WORKAROUND: IDP Requests returns 400, but is 200 in reallty.
          navigate('/login');
        }
      }
    } catch (err) {
      setErrorMessage(
        tActions(
          err.response && err.response.status === 400
            ? 'invitationCancelledError'
            : 'generalRequestError',
        ),
      );
      setHasFailedSetPassword(true);
      setNewPassword('');
      setNewPasswordConfirm('');
      setTerms(false);
      setPolicy(false);
      logger().error(`Could not set password\n${err.message}`);
    }
  };

  const loginOnEnter = (e) => {
    // login on Enter press
    if (e.keyCode === 13) {
      onPwReset();
    }
  };
  return (
    <PageWrapper
      isMobile={isMobile}
      card
      title={
        isTokenExpired
          ? t('errorHeadline')
          : (flowType === 1 && t('changeFirstTimePassword')) ||
            (flowType === 2 && t('changePassword'))
      }
    >
      <SuccessDialog
        isVisible={isStatusDialogVisible}
        onClose={() => navigate('/login')}
        text={t('passwordChangeSuccess')}
      />
      {role !== '' && (
        <>
          <StaticDialog
            type={getBranding() === 'trafineo' ? 'DPS_Indirect' : 'DPS'}
            open={isPolicyDialogVisible}
            role={role}
            onClose={() => {
              setIsPolicyDialogVisible(false);
            }}
          />
          <StaticDialog
            open={isInfoDialogVisible}
            type="DPH"
            role={role}
            onClose={() => setIsInfoDialogVisible(false)}
          />
          {role === 'FleetManager_direct' && (
            <StaticDialog
              type="TC"
              langSelect={getBranding() === 'bp'}
              role={role}
              open={isTermsDialogVisible}
              onClose={() => setIsTermsDialogVisible(false)}
            />
          )}
        </>
      )}
      {isTokenExpired ? (
        <>{t('tokenExpiredErrorText')}</>
      ) : (
        <form autoComplete="on">
          <input
            type="hidden"
            autoComplete="username"
            autocomplete="username"
            value={mail}
          />
          <Row>
            <TextFieldWrapper>
              <TextField
                newDriver
                data-cy="newPw"
                autoFocus
                type={newPwVisible ? 'text' : 'password'}
                value={newPassword}
                autoComplete="new-password"
                placeholder={tProfile('newPwPlaceholder')}
                label={tProfile('newPw')}
                name="newPassword"
                onKeyDown={loginOnEnter}
                onChange={(e) => {
                  setNewPassword(e.target.value);
                }}
              />
              <IconButton
                aria-label="toggle password visibility"
                onClick={() => setNewPwVisible(!newPwVisible)}
              >
                {newPwVisible ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </TextFieldWrapper>
          </Row>
          <Row>
            <TextFieldWrapper>
              <TextField
                newDriver
                data-cy="newPwRepeat"
                type={newPwRepeatVisible ? 'text' : 'password'}
                value={newPasswordConfirm}
                autoComplete="new-password"
                placeholder={tProfile('newPwRepeatPlaceholder')}
                label={tProfile('newPwRepeat')}
                onKeyDown={loginOnEnter}
                name="newPasswordRepeat"
                onChange={(e) => {
                  setNewPasswordConfirm(e.target.value);
                }}
              />
              <IconButton
                aria-label="toggle password visibility"
                onClick={() => setNewPwRepeatVisible(!newPwRepeatVisible)}
              >
                {newPwRepeatVisible ? <VisibilityOff /> : <Visibility />}
              </IconButton>
            </TextFieldWrapper>
          </Row>
          <PasswordCriteria
            valid={newPassword.match(/[a-zA-Z]/gm) ? true : false}
          >
            {tProfile('pwCriterieaOneLetter')}
          </PasswordCriteria>
          <PasswordCriteria valid={newPassword.match(/[0-9]/gm) ? true : false}>
            {tProfile('pwCriterieaOneNumber')}
          </PasswordCriteria>
          <PasswordCriteria
            valid={newPassword.match(/[@$!%*?&]/gm) ? true : false}
          >
            {tProfile('pwCriterieaSpecialCharakter')}
          </PasswordCriteria>
          <PasswordCriteria valid={newPassword.length >= 8}>
            {tProfile('pwCriteriea8Charakters')}
          </PasswordCriteria>
          <PasswordCriteria
            valid={
              newPassword.length >= 8 && newPassword === newPasswordConfirm
            }
          >
            {tProfile('pwCriterieaPasswordsMatch')}
          </PasswordCriteria>
          {flowType === 1 && (
            <Row>
              <TermsContainer>
                {role === 'FleetManager_direct' && (
                  <TermsRow>
                    <Checkbox
                      checked={terms}
                      onChange={() => setTerms(!terms)}
                    />
                    <p>
                      <Trans t={t} i18nKey="termsAndConditions">
                        I hereby confirm the
                        <a
                          name="terms"
                          onClick={() => setIsTermsDialogVisible(true)}
                          href="#terms"
                        >
                          Terms Conditions
                        </a>
                      </Trans>
                    </p>
                  </TermsRow>
                )}

                <TermsRow>
                  <Checkbox
                    checked={policy}
                    onChange={() => setPolicy(!policy)}
                  />
                  <p>
                    <Trans t={t} i18nKey="privacyPolicy">
                      I hereby confirm the acceptance of the
                      <a
                        name="policy"
                        onClick={() => setIsPolicyDialogVisible(true)}
                        href="#policy"
                      >
                        Private Policy
                      </a>
                      <a
                        name="info"
                        onClick={() => setIsInfoDialogVisible(true)}
                        href="#info"
                      >
                        Information
                      </a>
                      in accordance with GDPR
                    </Trans>
                  </p>
                </TermsRow>
              </TermsContainer>
            </Row>
          )}
          {hasFailedSetPassword && (
            <Row>
              <Error>{errorMessage}</Error>
            </Row>
          )}
          <ButtonWrapper>
            <Button
              disabled={
                !(
                  newPassword.match(
                    /^(?=.*\d)(?=.*[@$!%*?&])(?=.*[a-zA-Z]).{8,}$/gm,
                  ) && newPassword === newPasswordConfirm
                ) ||
                (flowType === 1 && !policy) ||
                (flowType === 1 && role === 'FleetManager_direct' && !terms)
              }
              onClick={onPwReset}
              variant="primary"
            >
              {t('enter')}
            </Button>
          </ButtonWrapper>
        </form>
      )}
    </PageWrapper>
  );
};

export default ChangePassword;
