import styled from 'styled-components';

import Radio from '@material-ui/core/Radio';
import FormControl from '@material-ui/core/FormControl';

export const Background = styled.div`
  background: white;
  min-height: 550px;
  margin-bottom: 2.5rem;
`;

export const Blank = styled.div`
  height: 10px;
`;
export const Bmargin = styled.b`
  margin-left: 0.25rem;
`;

export const Wrapper = styled.div`
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
  box-sizing: border-box;
  font-family: var(--font-family);
`;

export const Page = styled.div`
  display: flex;
  flex-direction: column;
`;

export const Headline = styled.div`
  font-size: 36px;
  text-align: left;
  margin: 1.5rem 0px;
  font-weight: 500;
`;

export const Row = styled.div`
  display: flex;
  width: 100%;
`;
export const Col = styled.div`
  width: 50%;
  padding-right: 1rem;
  box-sizing: border-box;
  &:last-child {
    padding-right: 0rem;
    padding-left: 1rem;
  }
  margin-bottom: 1rem;
`;

export const ColSingle = styled.div`
  width: 50%;
  padding-right: 1rem;
  box-sizing: border-box;
  margin-bottom: 1rem;
`;

export const ColCheckbox = styled.div`
  width: 40px;
  margin: 1rem 0;
  justify-content: flex-start;

  align-items: flex-start;
  display: flex;
`;

export const ColCheckBoxText = styled.span`
  display: inline !important;
  width: calc(100% - 40px);
  margin: 1rem 0;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  font-size: 16px;
  text-align: left;
  color: #333;
`;

export const ColFull = styled.div`
  width: 100%;
  margin-bottom: 1.5rem;
`;

export const ColRadio = styled.div`
  width: 100%;
  margin: 1rem 0;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

export const Label = styled.div`
  font-weight: bold;
  font-size: 16px;
  text-align: left;
  ${(props) => props.error === true && 'color: var(--error-color)'};
`;
export const ButtonContainer = styled.div`
  margin: 2rem 0 4rem 0;
  display: flex;
  width: 100%;
  justify-content: center;
  flex-direction: column;
  align-items: center;
`;

export const IconWrapper = styled.div`
  margin-right: 1rem;
`;

export const Hint = styled.div`
  font-size: 16px;
  text-align: left;
  color: #333;
  margin: 1.5rem 0 1rem 0;
  line-height: 20px;
  font-weight: 300;
  white-space: pre-line;
`;
export const Status = styled.div`
  font-size: 16px;
  text-align: center;
  color: #333;
  margin: 1.5rem 0 1rem 0;
  font-weight: 300;
  line-height: 20px;
`;

export const StatusError = styled.div`
  font-size: 16px;
  text-align: center;
  color: var(--error-color);
  margin: 1.5rem 0 1rem 0;
  font-weight: 300;
`;
export const FieldsError = styled.div`
  font-size: 16px;
  text-align: center;
  width: 100%;
  color: var(--error-color);
  margin: 1.5rem 0 1rem 0;
  font-weight: 300;
`;
export const StatusErrorField = styled.div`
  font-size: 16px;
  color: var(--error-color);
  margin: 1.5rem 0 1rem 0;
  font-weight: 300;
  text-align: left;
`;

export const ImgDesctiptionCol = styled.div`
  padding: 2rem;
  font-size: 16px;
  line-height: 20px;
  text-align: left;
  white-space: pre-line;
`;
export const ImgCol = styled.div``;
export const Img = styled.img`
  height: 300px;
`;
export const WallboxSelectionContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  margin-top: 1rem;
`;

export const Icon = styled.img`
  display: flex;
  height: 35px;
  margin-right: 0.5rem;
`;

export const ShowMore = styled.div`
  text-decoration: underline;
  cursor: pointer;
  margin: 1rem 0 2rem 0;
`;

export const Specs = styled.div`
  padding: 0 1rem 1rem 1rem;
  text-align: left;
  margin-bottom: 1rem;
  white-space: pre-line;
  font-size: 16px;
  line-height: 22px;
`;

export const WallboxTypeSelectionOption = styled.button`
  display: flex;
  outline: none;
  border: 1px solid gray;
  padding: 1rem;
  margin-right: 1rem;
  border-radius: 10px;
  margin-bottom: 0.5rem;
  border: 1px solid #ebebeb;
  border-color: ${(props) =>
    props.active === true ? 'var(--trafineo-rot-100) !important' : '#ebebeb'};

  opacity: ${(props) => (props.active === true ? 1 : 0.7)};
  background: white;
  cursor: pointer;
  font-weight: 400;
  font-size: 14px;
  align-items: center;
  :hover {
    border-color: grey;
  }
  :focus-visible {
    border-color: grey;
  }
  :focus-visible {
    border-color: grey;
    outline: none;
  }
`;

export const Textarea = styled.textarea`
  width: 100%;
  border: 1px solid #ebebeb;
  opacity: 0.7;
  outline: none;
  padding: 0.5rem;
  font-size: 14px;
  box-shadow: none;
  box-sizing: border-box;
  margin-top: 12px;
  font-family: var(--font-family);
  line-height: 22px;
  border-radius: 0;
  background-color: #f7f7f7;
  &:focus {
    border: solid 1px var(--default-text);
  }
  &:hover {
    border: solid 1px var(--default-text);
  }
`;

export const StyledRadio = styled(Radio)`
  color: black !important;
  font-family: var(--font-family);
  &.Mui-checked {
    color: black;
    &:hover {
      background-color: transparent;
    }
  }
  &:hover {
    background-color: transparent;
  }
`;

export const StyledFormControl = styled(FormControl)`
  .MuiTypography-body1 {
    font-family: var(--font-family);
  }
`;
export const StyledRadioError = styled(Radio)`
  color: var(--error-color) !important;
  font-family: var(--font-family);
  &.Mui-checked {
    color: black;
    &:hover {
      background-color: transparent;
    }
  }
  &:hover {
    background-color: transparent;
  }
`;
