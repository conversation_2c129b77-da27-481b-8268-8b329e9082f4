import { useEffect, useState } from 'react';
import { getBranding } from '../../utils/helper';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import StaticDialog from '../../Components/changePassword/StaticDialog';
import { Button } from '../../Components';
import Footer from '../../Components/Footer';
import Form from './Form';
import {
  Background,
  Blank,
  Wrapper,
  Page,
  Headline,
  ButtonContainer,
  Hint,
} from './Styles';

const HomeChargingAustria = () => {
  const navigate = useNavigate();
  const { t } = useTranslation('homeCharging');

  useEffect(() => {
    if (getBranding() === 'aral') {
      navigate('/home-charging-austria-error', { replace: true });
    }
  }, [navigate]);

  const [page, setPage] = useState(1);
  const [isPolicyDialogVisible, setIsPolicyDialogVisible] = useState(false);

  return (
    <>
      <StaticDialog
        type={getBranding() === 'trafineo' ? 'DPS_Indirect' : 'DPS'}
        open={isPolicyDialogVisible}
        role="Driver"
        onClose={() => setIsPolicyDialogVisible(false)}
      />
      <Blank />
      <Background>
        <Wrapper>
          {(page === 1 && (
            <Page>
              <Headline>{t('homeCharging')}</Headline>
              <Hint>{t('homeChargingHint')}</Hint>
              <ButtonContainer>
                <Button style={{ width: '300px' }} onClick={() => setPage(2)}>
                  {t('continue')}
                </Button>
              </ButtonContainer>
            </Page>
          )) || (
            <Form
              isPolicyDialogVisible={isPolicyDialogVisible}
              setIsPolicyDialogVisible={setIsPolicyDialogVisible}
            />
          )}
        </Wrapper>
      </Background>
      <Footer isHomeChargingAustria />
    </>
  );
};

export default HomeChargingAustria;
