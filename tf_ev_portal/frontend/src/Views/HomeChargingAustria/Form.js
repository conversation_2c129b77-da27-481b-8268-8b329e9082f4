import WallboxImg from '../../static/img/wallboxImg.png';
import statusIcon from '../../static/img/icons/Icon_Done.svg';
import BuildIcon from '@material-ui/icons/Build';
import CircularProgress from '../../Components/CircularProgress';
import { TextField, Button, Checkbox } from '../../Components';
import { useTranslation, Trans } from 'react-i18next';
import { useState } from 'react';
import RadioGroup from '@material-ui/core/RadioGroup';
import FormControlLabel from '@material-ui/core/FormControlLabel';
import requester from '../../utils/requester';
import {
  Row,
  ColRadio,
  Hint,
  Col,
  ColCheckbox,
  ColCheckBoxText,
  ColFull,
  ColSingle,
  Label,
  StyledFormControl,
  StyledRadioError,
  StatusError,
  StyledRadio,
  Headline,
  Img,
  ImgCol,
  ImgDesctiptionCol,
  WallboxSelectionContainer,
  Icon,
  WallboxTypeSelectionOption,
  Bmargin,
  ShowMore,
  FieldsError,
  ButtonContainer,
  Specs,
  Page,
  StatusErrorField,
  IconWrapper,
  Textarea,
  Status,
} from './Styles';
import TagManager from 'react-gtm-module';

const Form = ({ isPolicyDialogVisible, setIsPolicyDialogVisible }) => {
  const [specsOpen, setSpecsOpen] = useState(false);
  const [typeOfInstallation, setTypeOfInstallation] = useState({
    value: '',
    error: false,
  });
  const [ownerConsent, setOwnerConsent] = useState({
    value: '',
    error: false,
  });
  const [street, setStreet] = useState({
    value: '',
    error: false,
  });
  const [houseNo, setHouseNo] = useState({
    value: '',
    error: false,
  });
  const [postalCode, setPostalCode] = useState({
    value: '',
    error: false,
  });
  const [city, setCity] = useState({
    value: '',
    error: false,
  });
  const [company, setCompany] = useState({ value: '', error: false });
  const [title, setTitle] = useState({ value: '', error: false });
  const [firstName, setFirstName] = useState({ value: '', error: false });

  const [lastName, setLastName] = useState({ value: '', error: false });
  const [email, setEmail] = useState({ value: '', error: false });
  const [phone, setPhone] = useState({ value: '', error: false });
  const [customerNoChoice, setCustomerNoChoice] = useState({
    value: '',
    error: false,
  });
  const [isLoading, setIsLoading] = useState(false);
  const [consetShare, setConsetShare] = useState({
    value: false,
    error: false,
  });
  const [dps, setDps] = useState({ value: false, error: false });
  const [confirm, setConfirm] = useState({ value: false, error: false });

  const [customerNo, setCustomerNo] = useState({ value: '', error: false });

  const [country, setCountry] = useState({
    value: '',
    error: false,
  });
  const [company2, setCompany2] = useState({ value: '', error: false });
  const [firstName2, setFirstName2] = useState({ value: '', error: false });
  const [lastName2, setLastName2] = useState({ value: '', error: false });
  const [street2, setStreet2] = useState({
    value: '',
    error: false,
  });
  const [houseNo2, setHouseNo2] = useState({
    value: '',
    error: false,
  });
  const [postalCode2, setPostalCode2] = useState({
    value: '',
    error: false,
  });
  const [city2, setCity2] = useState({
    value: '',
    error: false,
  });
  const [country2, setCountry2] = useState({
    value: '',
    error: false,
  });
  const [billingAdressChoice, setBillingAdressChoice] = useState({
    value: 'Ja',
    error: false,
  });
  const [installationChoice, setInstallationChoice] = useState({
    value: 'Nein',
    error: false,
  });
  const [comment, setComment] = useState({
    value: '',
    error: false,
  });
  const [wallboxType, setWallboxType] = useState({
    value1: null,
    value2: null,
    error: false,
  });
  const [status, setStatus] = useState(0);
  const billingAdressFields = [
    { state: firstName2, setState: setFirstName2 },
    { state: lastName2, setState: setLastName2 },
    { state: street2, setState: setStreet2 },
    { state: houseNo2, setState: setHouseNo2 },
    { state: city2, setState: setCity2 },
    { state: postalCode2, setState: setPostalCode2 },
    { state: country2, setState: setCountry2 },
  ];
  const mandatoryFields = [
    { state: company, setState: setCompany },
    { state: title, setState: setTitle },
    { state: firstName, setState: setFirstName },
    { state: lastName, setState: setLastName },
    { state: email, setState: setEmail },
    { state: phone, setState: setPhone },
    { state: customerNoChoice, setState: setCustomerNoChoice },
    { state: typeOfInstallation, setState: setTypeOfInstallation },
    { state: ownerConsent, setState: setOwnerConsent },
    { state: street, setState: setStreet },
    { state: houseNo, setState: setHouseNo },
    { state: city, setState: setCity },
    { state: postalCode, setState: setPostalCode },
    { state: country, setState: setCountry },
  ];
  const showFieldError = () => {
    let valid = true;
    let fieldsToCheck = [dps, confirm, consetShare, wallboxType];
    fieldsToCheck = fieldsToCheck.concat(mandatoryFields);
    if (billingAdressChoice.value === 'Nein') {
      fieldsToCheck = fieldsToCheck.concat(billingAdressFields);
    }
    if (customerNoChoice.value === 'CustomerNo') {
      fieldsToCheck.push(customerNo);
    }
    fieldsToCheck.forEach((e) => {
      if (e.state?.error || e.error) {
        valid = false;
      }
    });
    return !valid;
  };

  const onSubmit = async () => {
    const req = {
      company: company.value,
      title: title.value,
      first_name: firstName.value,
      last_name: lastName.value,
      phone: phone.value,
      email: email.value,
      installation_wished: installationChoice.value,
      installation_address: {
        install_type: typeOfInstallation.value,
        owner_consent: ownerConsent.value,
        street: street.value,
        house_no: houseNo.value,
        postal_code: postalCode.value,
        city: city.value,
        country: country.value,
      },
      consent_to_share: consetShare.value,
      lead_source: 'reimbursement portal',
      wallbox_type: `${wallboxType.value1 || ''}${
        wallboxType.value1 && wallboxType.value2 ? ' & ' : ''
      }${wallboxType.value2 || ''}`,
      comment: comment.value,
    };
    let valid = true;
    let fieldsToCheck = [];
    fieldsToCheck = fieldsToCheck.concat(mandatoryFields);
    if (billingAdressChoice.value === 'Nein') {
      fieldsToCheck = fieldsToCheck.concat(billingAdressFields);
      req['different_billing_address'] = {
        company: company2.value,
        first_name: firstName2.value,
        last_name: lastName2.value,
        street: street2.value,
        house_no: houseNo2.value,
        postal_code: postalCode2.value,
        city: city2.value,
        country: country2.value,
      };
    }
    if (customerNoChoice.value === 'CustomerNo') {
      if (customerNo.value.length !== 18 || customerNo.value < 0) {
        setCustomerNo({
          ...customerNo,
          error: true,
        });
        valid = false;
      }
      req['customer_no'] = customerNo.value;
    }
    fieldsToCheck.forEach((e) => {
      if (e.state.value.trim().length === 0) {
        e.setState({ ...e.state, error: true });
        valid = false;
      } else {
        e.setState({ ...e.state, error: false });
      }
    });

    if (!dps.value) {
      setDps({
        ...dps,
        error: true,
      });
    }
    if (!confirm.value) {
      setConfirm({
        ...confirm,
        error: true,
      });
    }
    if (!consetShare.value) {
      setConsetShare({
        ...consetShare,
        error: true,
      });
    }
    if (!wallboxType.value1 && !wallboxType.value2) {
      setWallboxType({
        ...wallboxType,
        error: true,
      });
      valid = false;
    } else {
      setWallboxType({
        ...wallboxType,
        error: false,
      });
    }
    if (!/^\S+@\S+\.\S+$/.test(email.value)) {
      setEmail({
        ...email,
        error: true,
      });
      valid = false;
    }
    if (valid && dps.value && consetShare.value && confirm.value) {
      try {
        setStatus(0);
        setIsLoading(true);
        await requester()({
          method: 'put',
          url: '/sales/leads',
          data: req,
        });
        setStatus(1);
        setIsLoading(false);
      } catch (error) {
        setStatus(2);
        setIsLoading(false);
      }
    }
  };

  const { t } = useTranslation('homeCharging');
  return (
    <Page>
      <Headline>{t('contactInfo')}</Headline>
      <Row>
        <Hint>{t('requiredHint')}</Hint>
      </Row>
      <Row>
        <ColFull>
          <TextField
            autoFocus
            newDriver
            error={company.error}
            onFocus={() => setCompany({ ...company, error: false })}
            placeholder={`${t('company')} *`}
            value={company.value}
            onChange={(e) => setCompany({ ...company, value: e.target.value })}
          />
        </ColFull>
      </Row>
      <Row>
        <ColRadio>
          <Label>{t('title')} *:</Label>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={title.value}
              onChange={(e) => {
                setTitle({
                  ...title,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={title.error ? <StyledRadioError /> : <StyledRadio />}
                value="Frau"
                label={t('frau')}
              />
              <FormControlLabel
                control={title.error ? <StyledRadioError /> : <StyledRadio />}
                value="Herr"
                label={t('herr')}
              />
              <FormControlLabel
                control={title.error ? <StyledRadioError /> : <StyledRadio />}
                value="Divers"
                label={t('divers')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      <Row>
        <Col>
          <TextField
            newDriver
            error={firstName.error}
            onFocus={() => setFirstName({ ...firstName, error: false })}
            placeholder={`${t('firstName')} *`}
            value={firstName.value}
            onChange={(e) =>
              setFirstName({ ...firstName, value: e.target.value })
            }
          />
        </Col>
        <Col>
          <TextField
            newDriver
            error={lastName.error}
            onFocus={() => setLastName({ ...lastName, error: false })}
            placeholder={`${t('lastName')} *`}
            value={lastName.value}
            onChange={(e) =>
              setLastName({ ...lastName, value: e.target.value })
            }
          />
        </Col>
      </Row>
      <Row>
        <Col>
          <TextField
            newDriver
            onBlur={() => {
              if (!/^\S+@\S+\.\S+$/.test(email.value)) {
                setEmail({
                  ...email,
                  error: true,
                });
              }
            }}
            error={email.error}
            onFocus={() => setEmail({ ...email, error: false })}
            placeholder={`${t('email')} *`}
            value={email.value}
            onChange={(e) => setEmail({ ...email, value: e.target.value })}
          />
        </Col>
        <Col>
          <TextField
            newDriver
            error={phone.error}
            onFocus={() => setPhone({ ...phone, error: false })}
            placeholder={`${t('phone')} *`}
            value={phone.value}
            onChange={(e) => setPhone({ ...phone, value: e.target.value })}
          />
        </Col>
      </Row>
      <Row>
        <ColRadio>
          <Label>{t('customerNoChoice')} *:</Label>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={customerNoChoice.value}
              onChange={(e) => {
                setCustomerNoChoice({
                  ...customerNoChoice,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={
                  customerNoChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="CustomerNo"
                label={t('customerNo')}
              />
              <FormControlLabel
                control={
                  customerNoChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="false"
                label={t('noCard')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      {customerNoChoice.value === 'CustomerNo' && (
        <Row>
          <ColSingle>
            <Label>{t('customerNo')} *:</Label>
            {(customerNo.error && (
              <StatusErrorField>{t('customerNoHint')}</StatusErrorField>
            )) || <Hint>{t('customerNoHint')}</Hint>}
            <TextField
              onBlur={() => {
                if (customerNo.value.length !== 18 || customerNo.value < 0) {
                  setCustomerNo({
                    ...customerNo,
                    error: true,
                  });
                }
              }}
              newDriver
              type="number"
              error={customerNo.error}
              onFocus={() => setCustomerNo({ ...customerNo, error: false })}
              placeholder={`${t('customerNoPlaceholder')} *`}
              value={customerNo.value}
              onChange={(e) =>
                setCustomerNo({ ...customerNo, value: e.target.value })
              }
            />
          </ColSingle>
        </Row>
      )}
      {customerNoChoice.value === 'false' && <Hint>{t('noFCHint')}</Hint>}
      <Headline>{t('installationAddress')}</Headline>
      <Row>
        <ColRadio>
          <Label>{t('typeOfInstallation')} *:</Label>
          <Hint>{t('typeOfInstallationHint')}</Hint>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={typeOfInstallation.value}
              onChange={(e) => {
                setTypeOfInstallation({
                  ...typeOfInstallation,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={
                  typeOfInstallation.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Wandmontage"
                label={t('typeOfInstallationOption1')}
              />
              <FormControlLabel
                control={
                  typeOfInstallation.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Montage auf Standfuß"
                label={t('typeOfInstallationOption2')}
              />
              <FormControlLabel
                control={
                  typeOfInstallation.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Nicht sicher"
                label={t('typeOfInstallationOption3')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      <Row>
        <ColRadio>
          <Label>{t('ownerConsent')} *:</Label>
          <Hint>{t('ownerConsentHint')}</Hint>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={ownerConsent.value}
              onChange={(e) => {
                setOwnerConsent({
                  ...ownerConsent,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={
                  ownerConsent.error ? <StyledRadioError /> : <StyledRadio />
                }
                value="Ja, liegt vor"
                label={t('ownerConsentChoice1')}
              />
              <FormControlLabel
                control={
                  ownerConsent.error ? <StyledRadioError /> : <StyledRadio />
                }
                value="Nein, liegt (noch) nicht vor"
                label={t('ownerConsentChoice2')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      <Row>
        <Col>
          <TextField
            newDriver
            error={street.error}
            onFocus={() => setStreet({ ...street, error: false })}
            placeholder={`${t('street')} *`}
            value={street.value}
            onChange={(e) => setStreet({ ...street, value: e.target.value })}
          />
        </Col>
        <Col>
          <TextField
            newDriver
            error={houseNo.error}
            onFocus={() => setHouseNo({ ...houseNo, error: false })}
            placeholder={`${t('houseNo')} *`}
            value={houseNo.value}
            onChange={(e) => setHouseNo({ ...houseNo, value: e.target.value })}
          />
        </Col>
      </Row>
      <Row>
        <Col>
          <TextField
            newDriver
            error={city.error}
            onFocus={() => setCity({ ...city, error: false })}
            placeholder={`${t('city')} *`}
            value={city.value}
            onChange={(e) => setCity({ ...city, value: e.target.value })}
          />
        </Col>
        <Col>
          <TextField
            newDriver
            error={postalCode.error}
            onFocus={() => setPostalCode({ ...postalCode, error: false })}
            placeholder={`${t('postalCode')} *`}
            value={postalCode.value}
            onChange={(e) =>
              setPostalCode({ ...postalCode, value: e.target.value })
            }
          />
        </Col>
      </Row>
      <Row>
        <ColSingle>
          <TextField
            newDriver
            error={country.error}
            onFocus={() => setCountry({ ...country, error: false })}
            placeholder={`${t('country')} *`}
            value={country.value}
            onChange={(e) => setCountry({ ...country, value: e.target.value })}
          />
        </ColSingle>
      </Row>
      <Headline>{t('selectWallbox')}</Headline>
      <Row>
        <ImgCol>
          <Img src={WallboxImg} alt="Eve-Single-Pro-line"></Img>
        </ImgCol>
        <ImgDesctiptionCol>{t('wallboxGeneralDesciption')}</ImgDesctiptionCol>
      </Row>
      <Row>
        <Label error={wallboxType.error}>{t('wallboxType')} *:</Label>
      </Row>
      <Row>
        <WallboxSelectionContainer>
          <WallboxTypeSelectionOption
            active={wallboxType.value1 === 'Alfen Eve Single Pro 11kW - MID'}
            onClick={(event) => {
              setWallboxType({
                ...wallboxType,
                value1: wallboxType.value1
                  ? null
                  : 'Alfen Eve Single Pro 11kW - MID',
                error: wallboxType.value1 ? wallboxType.error : false,
              });
            }}
          >
            <Icon src={WallboxImg} /> {t('wallboxMid')}
            <Bmargin> - {t('wallboxMidPrice')}</Bmargin>
          </WallboxTypeSelectionOption>
          <WallboxTypeSelectionOption
            active={
              wallboxType.value2 === 'Alfen Eve Single Pro 11kW - Eichrecht'
            }
            onClick={(event) => {
              setWallboxType({
                ...wallboxType,
                value2: wallboxType.value2
                  ? null
                  : 'Alfen Eve Single Pro 11kW - Eichrecht',
                error: wallboxType.value2 ? wallboxType.error : false,
              });
            }}
          >
            <Icon
              onClick={(event) => {
                setWallboxType({
                  ...wallboxType,
                  value2: wallboxType.value2 ? null : event.target.value,
                });
              }}
              src={WallboxImg}
            />{' '}
            {t('wallboxEichrecht')}{' '}
            <Bmargin> - {t('wallboxEichrechtPrice')}</Bmargin>
          </WallboxTypeSelectionOption>
        </WallboxSelectionContainer>
      </Row>
      <Row>
        <Hint>{t('wallboxTypeHint')}</Hint>
      </Row>
      <Row>
        <ShowMore
          onClick={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'generic_event',
                generic_event: {
                  name: 'home_charging_austria_show_specs',
                  category: 'home_charging_austria',
                  action: 'show_specs',
                  label: 'showSpecs',
                },
              },
            });
            setSpecsOpen(!specsOpen);
          }}
        >
          {t('showSpecs')}
        </ShowMore>
      </Row>
      {specsOpen && (
        <Specs>
          <b> {`${t('wallboxMid')} & ${t('wallboxEichrecht')}`}</b>
          <br />
          <br />
          <b>{t('specsHeadline')}</b>
          <br />
          <br />
          {t('specs')}
        </Specs>
      )}
      <Headline>{t('Installation')}</Headline>
      <Row>
        <ColRadio>
          <Label>{t('installationChoiceLabel')} *:</Label>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={installationChoice.value}
              onChange={(e) => {
                setInstallationChoice({
                  ...installationChoice,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={
                  installationChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Ja"
                label={t('yes')}
              />
              <FormControlLabel
                control={
                  installationChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Nein"
                label={t('no')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      {installationChoice.value === 'Ja' && (
        <>
          <Row>
            <Label>{t('additionalCost')} *:</Label>
          </Row>
          <Row>
            <WallboxSelectionContainer>
              <WallboxTypeSelectionOption active>
                <IconWrapper>
                  <BuildIcon />
                </IconWrapper>
                {t('installationCost')}
                <Bmargin> - {t('installationPrice')}</Bmargin>
              </WallboxTypeSelectionOption>
            </WallboxSelectionContainer>
          </Row>
          <Row>
            <Hint>{t('additionalCostHint')}</Hint>
          </Row>
        </>
      )}
      <Headline>{t('billingAdress')}</Headline>
      <Row>
        <ColRadio>
          <Label>{t('billingAdressLabel')} *:</Label>
          <StyledFormControl component="fieldset">
            <RadioGroup
              row
              value={billingAdressChoice.value}
              onChange={(e) => {
                setBillingAdressChoice({
                  ...billingAdressChoice,
                  value: e.target.value,
                  error: false,
                });
              }}
            >
              <FormControlLabel
                control={
                  billingAdressChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Ja"
                label={t('yes')}
              />
              <FormControlLabel
                control={
                  billingAdressChoice.error ? (
                    <StyledRadioError />
                  ) : (
                    <StyledRadio />
                  )
                }
                value="Nein"
                label={t('no')}
              />
            </RadioGroup>
          </StyledFormControl>
        </ColRadio>
      </Row>
      {billingAdressChoice.value === 'Nein' && (
        <>
          <Row>
            <Col>
              <TextField
                autoFocus
                newDriver
                error={company2.error}
                onFocus={() => setCompany2({ ...company2, error: false })}
                placeholder={`${t('company')} ${t('optional')}`}
                value={company2.value}
                onChange={(e) =>
                  setCompany2({ ...company2, value: e.target.value })
                }
              />
            </Col>
            <Col></Col>
          </Row>
          <Row>
            <Col>
              <TextField
                newDriver
                error={firstName2.error}
                onFocus={() => setFirstName2({ ...firstName2, error: false })}
                placeholder={`${t('firstName')} *`}
                value={firstName2.value}
                onChange={(e) =>
                  setFirstName2({
                    ...firstName2,
                    value: e.target.value,
                  })
                }
              />
            </Col>
            <Col>
              <TextField
                newDriver
                error={lastName2.error}
                onFocus={() => setLastName2({ ...lastName2, error: false })}
                placeholder={`${t('lastName')} *`}
                value={lastName2.value}
                onChange={(e) =>
                  setLastName2({ ...lastName2, value: e.target.value })
                }
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <TextField
                newDriver
                error={street2.error}
                onFocus={() => setStreet2({ ...street2, error: false })}
                placeholder={`${t('street')} *`}
                value={street2.value}
                onChange={(e) =>
                  setStreet2({ ...street2, value: e.target.value })
                }
              />
            </Col>
            <Col>
              <TextField
                newDriver
                error={houseNo2.error}
                onFocus={() => setHouseNo2({ ...houseNo2, error: false })}
                placeholder={`${t('houseNo')} *`}
                value={houseNo2.value}
                onChange={(e) =>
                  setHouseNo2({ ...houseNo2, value: e.target.value })
                }
              />
            </Col>
          </Row>
          <Row>
            <Col>
              <TextField
                newDriver
                error={city2.error}
                onFocus={() => setCity2({ ...city2, error: false })}
                placeholder={`${t('city')} *`}
                value={city2.value}
                onChange={(e) => setCity2({ ...city2, value: e.target.value })}
              />
            </Col>
            <Col>
              <TextField
                newDriver
                error={postalCode2.error}
                onFocus={() => setPostalCode2({ ...postalCode2, error: false })}
                placeholder={`${t('postalCode')} *`}
                value={postalCode2.value}
                onChange={(e) =>
                  setPostalCode2({
                    ...postalCode2,
                    value: e.target.value,
                  })
                }
              />
            </Col>
          </Row>
          <Row>
            <ColSingle>
              <TextField
                newDriver
                error={country2.error}
                onFocus={() => setCountry2({ ...country2, error: false })}
                placeholder={`${t('country')} *`}
                value={country2.value}
                onChange={(e) =>
                  setCountry2({ ...country2, value: e.target.value })
                }
              />
            </ColSingle>
          </Row>
        </>
      )}
      <Row>
        <ColFull>
          <Textarea
            rows={8}
            maxLength={10000}
            newDriver
            placeholder={t('comment')}
            value={comment.value}
            onChange={(e) => setComment({ ...comment, value: e.target.value })}
          />
        </ColFull>
      </Row>
      <Row>
        <Hint>
          <Trans t={t} i18nKey="dataTrustHint">
            I hereby confirm the acceptance of the
            <a
              name="policy"
              onClick={() => setIsPolicyDialogVisible(true)}
              href="#policy"
            >
              Private Policy
            </a>
            in accordance with GDPR
          </Trans>
        </Hint>
      </Row>
      <Row>
        <ColCheckbox>
          <Checkbox
            error={consetShare.error}
            checked={consetShare.value}
            onChange={() => {
              setConsetShare({
                ...consetShare,
                value: !consetShare.value,
                error: false,
              });
            }}
          />
        </ColCheckbox>
        <ColCheckBoxText>
          <Trans t={t} i18nKey="consentShare">
            I hereby confirm the acceptance of the
            <a href="mailto:<EMAIL>">Private Policy</a>
            in accordance with GDPR
          </Trans>
        </ColCheckBoxText>
      </Row>
      <Row>
        <ColCheckbox>
          <Checkbox
            error={dps.error}
            checked={dps.value}
            onChange={() => {
              setDps({
                ...dps,
                value: !dps.value,
                error: false,
              });
            }}
          />
        </ColCheckbox>
        <ColCheckBoxText>
          <Trans t={t} i18nKey="dps">
            I hereby confirm the acceptance of the
            <a
              name="policy"
              onClick={() => setIsPolicyDialogVisible(true)}
              href="#policy"
            >
              Private Policy
            </a>
            in accordance with GDPR
          </Trans>
        </ColCheckBoxText>
      </Row>
      <Row>
        <ColCheckbox>
          <Checkbox
            error={confirm.error}
            checked={confirm.value}
            onChange={() => {
              setConfirm({
                ...confirm,
                value: !confirm.value,
                error: false,
              });
            }}
          />
        </ColCheckbox>
        <ColCheckBoxText>{t('confirmDialogText')}</ColCheckBoxText>
      </Row>
      {showFieldError() && (
        <Row>
          <FieldsError>{t('fillMandatoryFields')}</FieldsError>
        </Row>
      )}
      <Row>
        <ButtonContainer>
          {status === 1 && (
            <div>
              <img src={statusIcon} alt="success" />
              {installationChoice.value === 'Ja' && (
                <Status>{t('success')}</Status>
              )}
              {installationChoice.value !== 'Ja' && (
                <Status>{t('successNoInstallation')}</Status>
              )}
            </div>
          )}
          {status === 2 && <StatusError>{t('fail')}</StatusError>}
          {(isLoading && <CircularProgress />) ||
            (status !== 1 && (
              <Button style={{ width: '300px' }} onClick={onSubmit}>
                {t('submit')}
              </Button>
            ))}
        </ButtonContainer>
      </Row>
    </Page>
  );
};

export default Form;
