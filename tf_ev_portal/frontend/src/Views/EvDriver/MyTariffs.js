import { useContext, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { evDriverContext, reminderContext } from '../../ContextProvider';

import qs from 'querystring';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import { ButtonWrapper } from '../../Components/evDriver/PageTemplate';
import { Button } from '../../Components';
import Reminder from '../../Components/evDriver/Reminder';
import requester from '../../utils/requester';
import { Error, Row } from '../../Components/evDriver/PageTemplate';
import Tariff from '../../Components/Tariff';
import { useLiveQuery } from 'dexie-react-hooks';
import { db } from '../../db';
import EditWrapper from '../../Components/EditWrapper';
import { Box } from '@mui/material';
import Hint from '../../Components/Hint';
import { isMobile } from 'react-device-detect';
const TariffList = styled.div`
  margin: 2rem 0;
  display: flex;
  flex-direction: column;
`;

const Spacer = styled.div`
  height: 40px;
`;

const MyTariffs = () => {
  const { t } = useTranslation('evDriver');
  const navigate = useNavigate();
  const { evDriverData } = useContext(evDriverContext);
  const { edit } = useParams();
  const isReimbursementCancelled = evDriverData.reimbursementStatus === 3;
  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const skipTypeSelection = parsedQueryString.skipType;

  const tActions = useTranslation('actions').t;

  const pendingTariff = evDriverData.pending_tariff_completed
    ? evDriverData.pending_tariff
    : undefined;
  const pendingTariffId = pendingTariff?.functional_tariff_id;

  const tariffsToDisplay = () => {
    if (pendingTariff && !pendingTariffId) {
      return [pendingTariff].concat(evDriverData.tariffs);
    }
    return evDriverData.tariffs;
  };
  const { reminder, setReminder } = useContext(reminderContext);
  const [reminderInput, setReminderInput] = useState({
    active: reminder ? true : false,
    interval: reminder || 12,
  });
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const files = useLiveQuery(() => db.files.toArray()) || [];
  const tariffModified =
    evDriverData?.approvalStatus.indexOf('tariff_modified') !== -1;

  const changeReminder = async (remindeValue) => {
    try {
      setIsErrorVisible(false);
      if (remindeValue) {
        await requester().put(
          `/reminder`,
          {
            reminder_type: 'electricity_contract_update_reminder',
            reminder_interval: remindeValue,
          },
          {
            headers: {
              'Content-Type': 'application/json',
            },
          },
        );
      } else {
        await requester().delete(
          `/reminder/electricity_contract_update_reminder`,
        );
      }
      const { data: remiderRsp } = await requester().get(`/reminder`);
      const contractReminder = remiderRsp.reminders.filter(
        (e) => (e.reminder_type = 'electricity_contract_update_reminder'),
      )[0];
      setReminder(contractReminder ? contractReminder.interval : undefined);
    } catch (err) {
      setReminderInput({
        active: reminder ? true : false,
        interval: reminder || 12,
      });
      setIsErrorVisible(true);
    }
  };
  const onReminderChange = (data) => {
    setReminderInput(data);
    if (
      reminderInput.active !== data.active ||
      reminderInput.interval !== data.interval
    ) {
      changeReminder(data.active ? data.interval : undefined);
    }
  };

  return (
    <div>
      {!pendingTariff && !edit && (
        <>
          <Row>
            <Reminder
              hint={t('reminderHint')}
              value={reminderInput}
              onChange={onReminderChange}
            />
          </Row>
          {isErrorVisible && (
            <Row style={{ padding: '0.5rem' }}>
              <Error>{tActions('generalRequestError')}</Error>
            </Row>
          )}
        </>
      )}

      <TariffList>
        {tariffsToDisplay()
          .sort(function (a, b) {
            return (
              new Date(b.tariff_elements[0].restrictions[0].start_date) -
              new Date(a.tariff_elements[0].restrictions[0].start_date)
            );
          })
          .map((tariff, i) => {
            const currentTariffId = tariff.functional_tariff_id;
            const isTariffModified = pendingTariffId === currentTariffId;
            return (
              <Tariff
                key={i}
                index={i}
                tariffModified={isTariffModified}
                files={isTariffModified ? files : undefined}
                editDisabled={!edit || (edit && isTariffModified)}
                tariff={isTariffModified ? pendingTariff : tariff}
                skipTypeSelection={skipTypeSelection}
              />
            );
          })}
      </TariffList>
      {evDriverData?.principle === 'foureye' &&
        (tariffModified || pendingTariff) && (
          <Box mb="2rem">
            <Hint>{tariffModified ? t('inApproval') : t('needsApproval')}</Hint>
          </Box>
        )}

      {pendingTariff ? (
        <EditWrapper
          type="tariff"
          needsApproval
          beforeSafe={() => true}
          data={{
            tariff: pendingTariff,
            files: files,
          }}
        ></EditWrapper>
      ) : (
        <ButtonWrapper isMobile={isMobile}>
          <Button
            disabled={isReimbursementCancelled || tariffModified}
            onClick={() => navigate('/changeTariff')}
            variant="primary"
          >
            {t('modifyOrAddTariff')}
          </Button>
        </ButtonWrapper>
      )}
      <Spacer />
    </div>
  );
};

export default MyTariffs;
