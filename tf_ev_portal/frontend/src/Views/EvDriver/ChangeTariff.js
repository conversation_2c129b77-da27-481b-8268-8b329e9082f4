import PageWrapper from '../../Components/PageWrapper';
import { useContext } from 'react';
import { db } from '../../db';

import {
  Row,
  TariffTypeSelectionContainer,
  TariffTypeSelectionOption,
} from '../../Components/evDriver/PageTemplate';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { evDriverContext } from '../../ContextProvider';
import OfflineBoltIcon from '@material-ui/icons/OfflineBolt';
import EuroIcon from '@material-ui/icons/Euro';
import EditIcon from '@material-ui/icons/Edit';
import Green_Energy from '../../static/img/icons/Green_energy.svg';
import { isMobile } from 'react-device-detect';
import styled from 'styled-components';

const StyledIcon = styled.img`
  height: 24px;
  width: 24px;
  margin-right: 0.5rem;
`;

const ChangeTariff = () => {
  const { t } = useTranslation('evDriver');
  const navigate = useNavigate();
  const { evDriverDispatch } = useContext(evDriverContext);

  const revert = () => {
    evDriverDispatch({
      type: 'revert',
      value: {
        tariffType: null,
      },
    });
    db.delete();
  };
  const TariffTypeSelection = () => {
    return (
      <TariffTypeSelectionContainer>
        <TariffTypeSelectionOption
          onClick={() => {
            revert();
            navigate('/tariffselection');
          }}
        >
          <OfflineBoltIcon style={{ marginRight: '0.5rem' }} />
          {t(`optionNewSupplier`)}
        </TariffTypeSelectionOption>
        <TariffTypeSelectionOption
          onClick={() => {
            revert();
            navigate('/tariffselection');
          }}
        >
          <EuroIcon style={{ marginRight: '0.5rem' }} />
          {t(`optionNewWorkprice`)}
        </TariffTypeSelectionOption>
        <TariffTypeSelectionOption
          onClick={() => {
            revert();
            navigate('/myinformation/mytariffs/edit');
          }}
        >
          <EditIcon style={{ marginRight: '0.5rem' }} />
          {t(`optionWorngTariff`)}
        </TariffTypeSelectionOption>
        <TariffTypeSelectionOption
          onClick={() => {
            revert();
            navigate('/myinformation/mytariffs/edit?skipType=true');
          }}
        >
          <StyledIcon src={Green_Energy} alt="SustainabilityIcon" />
          {t(`optionEditSustainability`)}
        </TariffTypeSelectionOption>
      </TariffTypeSelectionContainer>
    );
  };
  return (
    <PageWrapper
      backButton
      isMobile={isMobile}
      title={t('modifyOrAddTariff')}
      description={t('changeTariffDescription')}
    >
      <Row>
        <TariffTypeSelection />
      </Row>
    </PageWrapper>
  );
};

export default ChangeTariff;
