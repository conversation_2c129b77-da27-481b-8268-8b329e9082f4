import { useContext } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import PageWrapper from '../../Components/PageWrapper';
import { Box } from '@mui/system';
import { Tab, Tabs } from '@mui/material';
import Activities from '../../Components/Activities';
import { evDriverContext } from '../../ContextProvider';
import styled from 'styled-components';
import Cards from '../../Components/userDetails/Cards';
import MyTariffs from './MyTariffs';
import Vehicle from './Vehicle';
import Welcome from './Welcome';
import { isMobile } from 'react-device-detect';
import PersonalInfo from '../../Components/MyInformation/PersonalInfo';
import CircleIcon from '@mui/icons-material/Circle';
import ErrorIcon from '@mui/icons-material/Error';

const TabWrapper = styled.div`
  margin-top: ${isMobile ? '0.5rem' : '1.5rem'};
  margin-left: ${isMobile ? '0' : '0.5rem'};
  max-width: ${(props) => (isMobile ? '100%' : props.maxWidth)};
`;

const Legend = styled.div`
  width: 100%;
`;

const LegendInfo = styled.p`
  display: inline-block;
  padding-right: 20px;
`;

const StyledTab = styled(Tab)`
  text-transform: none !important;
  font-size: 14px !important;
  padding: 0 1rem !important;
  &.Mui-selected {
    color: black !important;
    font-weight: bold;
  }
`;

const StyledTabs = styled(Tabs)`
  & .MuiTabs-scrollButtons.Mui-disabled {
    opacity: 0.2 !important;
  }
`;

const StyledCircleIcon = styled(CircleIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: var(--trafineo-rot-100) !important;
`;

const StyledUpdateIcon = styled(ErrorIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: #ff7e00 !important;
`;

const LegendUpdateIcon = styled(StyledUpdateIcon)`
  margin-bottom: -3.5px;
`;

const LegendCircleIcon = styled(StyledCircleIcon)`
  margin-bottom: -3.5px;
`;

function a11yProps(index) {
  return {
    id: `info-tab-${index}`,
    'aria-controls': `info-tabpanel-${index}`,
  };
}
const MyInformation = () => {
  const { t } = useTranslation('evDriver');
  const { tab } = useParams();
  const { evDriverData } = useContext(evDriverContext);
  const navigate = useNavigate();

  const informationTabs = {
    personalinformation: (
      <TabWrapper maxWidth="700px">
        <PersonalInfo evDriverData={evDriverData} />
      </TabWrapper>
    ),
    mycards: (
      <TabWrapper maxWidth="660px">
        <Cards userMode tokens={evDriverData.cards || []} />
      </TabWrapper>
    ),
    myvehicle: (
      <TabWrapper maxWidth="330px">
        <Vehicle />
      </TabWrapper>
    ),
    mywallbox: (
      <TabWrapper maxWidth="660px">
        <Welcome
          onSave={(data) => {
            alert(JSON.stringify(data));
          }}
        />
      </TabWrapper>
    ),
    mytariffs: (
      <TabWrapper maxWidth="660px">
        <MyTariffs />
      </TabWrapper>
    ),
    myactivity: (
      <TabWrapper>
        <Activities />
      </TabWrapper>
    ),
  };

  const missingName = !evDriverData.firstname || !evDriverData.lastname;
  const missingVehicleOrLicencePlate =
    !evDriverData.vehicle.id || !evDriverData.vehicle.licencePlate;

  const wallboxUpdateRequested = evDriverData.approvalStatus.includes(
    'wallbox_update_requested',
  );
  const bankingUpdateRequested = evDriverData.approvalStatus.includes(
    'banking_update_requested',
  );
  const tariffUpdateRequested = evDriverData.approvalStatus.includes(
    'tariff_update_requested',
  );

  const missingWallboxAddress =
    !evDriverData.wallbox ||
    !evDriverData.wallbox.address ||
    !evDriverData.wallbox.address.street ||
    !evDriverData.wallbox.address.number ||
    !evDriverData.wallbox.address.city ||
    !evDriverData.wallbox.address.postcode ||
    !evDriverData.wallbox.address.country;

  const missingEvseId = !evDriverData.wallbox.evseId;

  return (
    <PageWrapper
      backButton
      isMobile={isMobile}
      backFuction={() => navigate('/')}
      title={t('myInformation')}
    >
      <Legend>
        {(missingVehicleOrLicencePlate ||
          missingName ||
          missingWallboxAddress ||
          missingEvseId) && (
          <LegendInfo>
            <LegendCircleIcon />
            {t('generalInformationMissing')}
          </LegendInfo>
        )}
        {(bankingUpdateRequested ||
          wallboxUpdateRequested ||
          tariffUpdateRequested) && (
          <LegendInfo>
            <LegendUpdateIcon />
            {t('changesRequested')}
          </LegendInfo>
        )}
      </Legend>
      <Box>
        <Box mb="1rem" sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <StyledTabs
            variant="scrollable"
            allowScrollButtonsMobile
            scrollButtons="auto"
            value={tab}
            onChange={(event, newValue) =>
              navigate('/myinformation/' + newValue)
            }
            aria-label="informationTabs"
          >
            {Object.entries(informationTabs).map((arr, i) => {
              const e = arr[0];
              return (
                <StyledTab
                  key={e}
                  disableRipple
                  value={e}
                  label={t(e)}
                  {...a11yProps(i)}
                  icon={
                    bankingUpdateRequested && i === 0 ? (
                      <StyledUpdateIcon />
                    ) : missingName && !bankingUpdateRequested && i === 0 ? (
                      <StyledCircleIcon />
                    ) : missingVehicleOrLicencePlate && i === 2 ? (
                      <StyledCircleIcon />
                    ) : wallboxUpdateRequested && i === 3 ? (
                      <StyledUpdateIcon />
                    ) : (missingEvseId || missingWallboxAddress) &&
                      !wallboxUpdateRequested &&
                      i === 3 ? (
                      <StyledCircleIcon />
                    ) : tariffUpdateRequested && i === 4 ? (
                      <StyledUpdateIcon />
                    ) : null
                  }
                  iconPosition="end"
                />
              );
            })}
          </StyledTabs>
        </Box>
        <TabWrapper>{informationTabs[tab]}</TabWrapper>
      </Box>
    </PageWrapper>
  );
};

export default MyInformation;
