import { useEffect, useContext } from 'react';
import { Box } from '@material-ui/core';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { evDriverContext } from '../../ContextProvider';
import Content from '../../Components/helper/Content';
import statusIcon from '../../static/img/icons/Icon_Done.svg';
import styled from 'styled-components';

const SuccessText = styled.h2`
  white-space: pre-line;
  text-align: center;
`;

const Wrapper = styled(Box)`
  width: 100%;
  height: calc(100vh - 175px);
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
`;

const Waiting = () => {
  const { evDriverData } = useContext(evDriverContext);
  const navigate = useNavigate();
  const { t } = useTranslation('evDriver');
  const twoEyePrinciple = evDriverData?.principle === 'twoeye';

  useEffect(() => {
    if (!evDriverData) {
      navigate('/');
    }
  }, [evDriverData, navigate]);

  return (
    <Content>
      <Wrapper data-cy="waitingPage">
        <img src={statusIcon} alt="success" />
        <SuccessText>{`${
          twoEyePrinciple ? t('submitPending') : t('waitingForFleetManagerText')
        } `}</SuccessText>
      </Wrapper>
    </Content>
  );
};

export default Waiting;
