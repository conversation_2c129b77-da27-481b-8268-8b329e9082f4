import { useContext, useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import qs from 'querystring';
import PropTypes from 'prop-types';
import DateField from '../../Components/evDriver/DateField';
import iconTariff from '../../static/img/icon-clock.svg';
import iconTwoTariff from '../../static/img/icon-twotariff.svg';
import TariffField from '../../Components/evDriver/TariffField';
import UploadField from '../../Components/UploadField';
import infoIcon from '../../static/img/icons/Info_Icon_White.svg';
import { ReactComponent as DocumentIcon } from '../../static/img/iconEletricityContract.svg';
import { evDriverContext, reminderContext } from '../../ContextProvider';
import PageTemplate, {
  Row,
  InputWrapper,
  InformationIcon,
  TooltipContainer,
  Description,
  Error,
  RowUpload,
  TooltipTextWrapper,
  TariffTypeSelectionContainer,
  TariffTypeSelectionOption,
  TariffIcon,
  InformationContainer,
  InformationText,
  Hint,
} from '../../Components/evDriver/PageTemplate';
import { QuestionMark } from '../../Components';
import { isMobile } from 'react-device-detect';
import { db } from '../../db';
import { useLiveQuery } from 'dexie-react-hooks';
import Reminder from '../../Components/evDriver/Reminder';
import MultiFileDisplay from '../../Components/MultiFileDisplay';
import styled from 'styled-components';
import { MenuItem } from '@material-ui/core';
import Select from '../../Components/Select';
import CurrencyField from '../../Components/evDriver/CurrencyField';

/**
 * isViewInitial === true: The ev driver enters the work price, selects the date and pdf file to upload of the current
 * electricity tariff. He checks the responsibility section checkbox and by confirming he moves on to the bank data view.
 * isViewInitial === false: The ev driver enters the work price, selects the date and pdf file to upload of the additional
 * electricity tariff. He checks the responsibility section checkbox and by confirming goes back to the modify view.
 */

const maxPrice = 1.5;
const minPrice = 0.01;

const maxCo2 = 1000.0;
const minCo2 = 0.01;

const UploadInfoRow = styled.div`
  width: 100%;
  display: flex;
  justify-content: flex-start;
  min-height: 50px;
`;

const InfoWrapper = styled.div`
  width: ${(props) => props.width};
`;

const UploadInfoText = styled.p`
  padding: 0;
  margin: 0;
  width: 100%;
  line-height: 20px;
  padding-left: 5px;
  text-align: left;
`;

const NumberText = styled.p`
  padding: 0;
  margin: 0;
  line-height: 20px;
  color: white;
`;

const UploadInfoCircle = styled.div`
  height: 20px;
  width: 20px;
  background-color: var(--trafineo-rot-100);
  border-radius: 10px;
  text-align: center;
  vertical-align: middle;
`;

const StyledDocumentIcon = styled(DocumentIcon)`
  fill: var(--trafineo-rot-100) !important;
  width: 80% !important;
  height: 80% !important;
`;

const TextArea = styled.textarea`
  font-family: var(--font-family);
  font-size: 14px;
  line-height: 20px;
  padding: 0.5rem;
`;

const CommentRow = styled(Row)`
  margin-top: -35px;
`;

const Counter = styled.p`
  font-style: italic;
  text-align: initial;
  color: ${(props) => (props.full ? 'var(--error-color)' : 'default')};
`;

const Placeholder = styled.div`
  color: grey;
`;

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const ElectricityTariff = ({ isViewInitial }) => {
  const { t } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;
  const navigate = useNavigate();
  const location = useLocation();
  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);
  const { reminder, setReminder } = useContext(reminderContext);
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { id } = parsedQueryString;

  const tariffSustainabilityTypes = [true, false, 'null'];

  const tariff = evDriverData?.pending_tariff
    ? evDriverData.pending_tariff
    : id
    ? evDriverData?.tariffs[id]
    : isViewInitial
    ? evDriverData?.tariffs?.[0]
    : null;

  const [date, setDate] = useState(
    tariff ? tariff.tariff_elements[0].restrictions[0].start_date : null,
  );
  const [price, setPrice] = useState(
    tariff ? tariff.tariff_elements[0].price_components[0].price : null,
  );

  const [co2, setCo2] = useState(
    tariff
      ? tariff.energy_mix
        ? tariff.energy_mix.environ_impact
          ? tariff.energy_mix.environ_impact.find(
              (e) => e.category === 'CARBON_DIOXIDE',
            ).amount
          : null
        : null
      : null,
  );

  const [comment, setComment] = useState(
    isViewInitial ? (tariff && tariff[0] ? tariff[0].comment : '') : '',
  );
  const [greenTariff, setGreenTariff] = useState({
    value: !tariff
      ? ''
      : tariff?.energy_mix === null
      ? ''
      : tariff?.energy_mix
      ? tariff.energy_mix?.is_green_energy
        ? true
        : tariff.energy_mix?.environ_impact
        ? false
        : 'null'
      : 'null',
    error: false,
  });
  const [emissionValid, setEmissionValid] = useState(true);
  const [priceValid, setPriceValid] = useState(true);
  const [dateValid, setDateValid] = useState(true);
  const [offPeakPriceValid, setOffPeakPriceValid] = useState(3);
  const [offPeakTimespanValid, setOffPeakTimespanValid] = useState(true);
  const [offPeakPrice, setOffPeakPrice] = useState(
    tariff && tariff.tariff_elements[1]
      ? tariff.tariff_elements[1].price_components[0].price
      : null,
  );
  const [reminderInput, setReminderInput] = useState({
    active: reminder ? true : false,
    interval: reminder || 12,
  });

  const [offPeakIsHigherAccepted, setOffPeakIsHigherAccepted] = useState(false);
  const [clickedSelection, setClickedSelection] = useState(false);
  useEffect(() => {
    if (isViewInitial && tariff) {
      setDate(tariff.tariff_elements[0].restrictions[0].start_date);
      setPrice(tariff.tariff_elements[0].price_components[0].price);
      setComment(tariff.tariff_elements[0].comment);
    }
  }, [isViewInitial, tariff]);

  const stringToDate = (string) => {
    const split = string.split(':');
    const hours = new Date();
    hours.setHours(split[0]);
    hours.setMinutes(split[1]);
    return hours;
  };

  const [offPeakFrom, setOffPeakFrom] = useState(
    tariff &&
      tariff.tariff_elements[1] &&
      tariff.tariff_elements[1].restrictions[0].start_time
      ? stringToDate(tariff.tariff_elements[1].restrictions[0].start_time)
      : new Date(1579474800000),
  );
  const [offPeakTo, setOffPeakTo] = useState(
    tariff &&
      tariff.tariff_elements[1] &&
      tariff.tariff_elements[1].restrictions[0].end_time
      ? stringToDate(tariff.tariff_elements[1].restrictions[0].end_time)
      : new Date(1579474800000),
  );
  const [sameAsWeekdays, setSameAsWeekdays] = useState(
    !(tariff && tariff.tariff_elements[2]),
  );
  const isAtDriver = evDriverData?.country?.new === 'at';

  const setTariffType = (type) => {
    evDriverDispatch({
      type: 'tariffType',
      value: { tariffType: type },
    });
  };

  const skipType = parsedQueryString.skipType;

  const [viewMode, setViewMode] = useState(
    skipType !== 'false'
      ? tariff?.tariff_elements.length === 1
        ? 'ONETARIFF'
        : tariff?.tariff_elements.length > 1
        ? 'TWOTARIFF'
        : 'SELECTION'
      : 'SELECTION',
  );

  const dbFileData = useLiveQuery(() => db.files.toArray()) || [];

  const isFileUploaded = dbFileData.length > 0;

  const Tooltip = () => {
    const { t } = useTranslation('evDriver');

    return (
      <TooltipContainer>
        <InformationIcon src={infoIcon} alt="info" />
        <div>{t(`electricityTariffTooltip${isAtDriver ? 'At' : ''}`)}</div>
      </TooltipContainer>
    );
  };

  const DateTooltip = () => {
    const { t } = useTranslation('evDriver');

    return (
      <TooltipContainer>
        <InformationIcon src={infoIcon} alt="info" />
        <div>{t('validityDateTooltip')}</div>
      </TooltipContainer>
    );
  };

  const UploadTooltip = () => {
    const { t } = useTranslation('evDriver');

    return (
      <TooltipContainer>
        <InformationIcon src={infoIcon} alt="info" />
        <div>{t('uploadTooltip')}</div>
      </TooltipContainer>
    );
  };

  const StartDateTooltip = ({ isGreenEnergy = false }) => {
    const { t } = useTranslation('evDriver');

    return (
      <TooltipContainer>
        <InformationIcon src={infoIcon} alt="info" />
        <div>
          {t(isGreenEnergy ? 'green_energy_hint' : 'tariffValidFromHint')}
        </div>
      </TooltipContainer>
    );
  };

  useEffect(() => {
    // incoming values should be valid
    if (!evDriverData && process.env.REACT_APP_USE_MOCK !== 'true') {
      navigate('/');
    }
  }, [evDriverData, navigate]);
  useEffect(() => {
    if (location.pathname === '/tariffselection' && skipType !== 'true') {
      setViewMode('SELECTION');
    }
    if (location.pathname === '/electricityTariff') {
      if (evDriverData?.tariffType) {
        setViewMode(evDriverData?.tariffType);
      }
    }
  }, [location, evDriverData, skipType, tariff]);

  const isReadyForNextSite = () => {
    const isPriceValid = (priceInput) =>
      priceInput >= minPrice && priceInput <= maxPrice;

    const isEmissionValid = (co2) => {
      let value =
        (!co2 && greenTariff.value === 'null') ||
        (greenTariff.value
          ? co2 && co2 === '0'
          : co2 && co2 !== '0' && co2 >= minCo2 && co2 <= maxCo2);
      if (value || value === 0) {
        return true;
      } else {
        return false;
      }
    };

    const isStartDateValid = () => {
      const startDate = date.toISOString?.().replace(/(.*)T.*/g, '$1') || date;
      const tariffId = tariff
        ? tariff.tariff_elements[0]?.functional_tariff_id
        : null;

      if (!evDriverData?.tariffs) {
        return true;
      }

      const filteredTariffs = evDriverData?.tariffs?.filter(
        (e) =>
          e.tariff_elements[0].restrictions[0].start_date === startDate &&
          e.tariff_elements[0].functional_tariff_id !== tariffId,
      );
      return filteredTariffs.length === 0;
    };

    const isOffPeakTimespanValid = () => {
      const diffInHrs = Math.abs(offPeakFrom - offPeakTo) / 36e5;
      const isValid = diffInHrs >= 1 && diffInHrs <= 23;
      return isValid;
    };
    const isOffpeakPriceValid = () => {
      if (offPeakPrice < minPrice || offPeakPrice > maxPrice) {
        return 2;
      } else if (Number(offPeakPrice) > price) {
        return offPeakIsHigherAccepted ? 3 : 4;
      } else {
        return 3;
      }
    };

    // outgoing values should be valid
    setDateValid(isStartDateValid());
    setPriceValid(isPriceValid(price));
    setEmissionValid(isEmissionValid(co2));
    if (viewMode === 'TWOTARIFF') {
      setOffPeakTimespanValid(isOffPeakTimespanValid());
      setOffPeakPriceValid(isOffpeakPriceValid());
      if (
        isPriceValid(price) &&
        isEmissionValid(co2) &&
        isOffPeakTimespanValid() &&
        isOffpeakPriceValid() === 3 &&
        isStartDateValid() &&
        isFileUploaded
      ) {
        saveTariff(true);
        navigate(
          isViewInitial
            ? '/bankData'
            : skipType
            ? '/myinformation/mytariffs?skip=true'
            : '/myinformation/mytariffs',
        );
      } else {
        window.scrollTo({ top: 0, behavior: 'smooth' });
      }
    } else {
      if (
        isPriceValid(price) &&
        isStartDateValid() &&
        isEmissionValid(co2) &&
        isFileUploaded
      ) {
        saveTariff(true);
        navigate(
          isViewInitial
            ? '/bankData'
            : skipType
            ? '/myinformation/mytariffs?skip=true'
            : '/myinformation/mytariffs',
        );
      }
    }
  };

  useEffect(() => {
    // prevent hook from resetting co2 value if user has not yet clicked the Select component
    if (!clickedSelection) {
      return;
    }
    if (greenTariff.value === true) {
      setCo2(0);
    }
    if (greenTariff.value === 'null' || greenTariff.value === false) {
      setCo2(null);
    }
  }, [greenTariff.value, clickedSelection]);

  const convertTime = (time) => {
    try {
      if (viewMode === 'TWOTARIFF') {
        return `${time.getHours() < 10 ? '0' : ''}${time.getHours()}:${
          time.getMinutes() < 10 ? '0' : ''
        }${time.getMinutes()}`;
      }
    } catch (e) {
      return null;
    }
    return null;
  };

  const saveTariff = (tariffCompleted) => {
    const formattedStartDate = date
      ? typeof date === 'string'
        ? date
        : new Date(date.getTime() - date.getTimezoneOffset() * 60000)
            .toISOString()
            .split('T')[0]
      : null;

    const action = {
      type: 'pending_tariff',
      value:
        greenTariff.value === 'null'
          ? {
              pending_tariff_completed: tariffCompleted,
              pending_tariff: {
                currency: 'EUR',
                comment: comment,
                functional_tariff_id: tariff
                  ? tariff.functional_tariff_id
                  : null,
                start_date: formattedStartDate,
                tariff_elements: [
                  {
                    price_components: [
                      {
                        tariff_type: 'ENERGY',
                        tariff_sub_type: evDriverData?.tariffType,
                        price,
                        step_size: 1,
                      },
                    ],
                    restrictions: [
                      {
                        start_time: convertTime(offPeakTo),
                        end_time: convertTime(offPeakFrom),
                        day_of_week:
                          sameAsWeekdays ||
                          evDriverData?.tariffType === 'ONETARIFF'
                            ? [
                                'MONDAY',
                                'TUESDAY',
                                'WEDNESDAY',
                                'THURSDAY',
                                'FRIDAY',
                                'SATURDAY',
                                'SUNDAY',
                              ]
                            : [
                                'MONDAY',
                                'TUESDAY',
                                'WEDNESDAY',
                                'THURSDAY',
                                'FRIDAY',
                              ],
                        start_date: formattedStartDate,
                      },
                    ],
                  },
                ],
              },
            }
          : {
              pending_tariff_completed: tariffCompleted,
              pending_tariff: {
                currency: 'EUR',
                comment: comment,
                functional_tariff_id: tariff
                  ? tariff.functional_tariff_id
                  : null,
                energy_mix: {
                  is_green_energy: greenTariff.value,
                  environ_impact: [{ category: 'CARBON_DIOXIDE', amount: co2 }],
                },
                start_date: formattedStartDate,
                tariff_elements: [
                  {
                    price_components: [
                      {
                        tariff_type: 'ENERGY',
                        tariff_sub_type: evDriverData?.tariffType,
                        price,
                        step_size: 1,
                      },
                    ],
                    restrictions: [
                      {
                        start_time: convertTime(offPeakTo),
                        end_time: convertTime(offPeakFrom),
                        day_of_week:
                          sameAsWeekdays ||
                          evDriverData?.tariffType === 'ONETARIFF'
                            ? [
                                'MONDAY',
                                'TUESDAY',
                                'WEDNESDAY',
                                'THURSDAY',
                                'FRIDAY',
                                'SATURDAY',
                                'SUNDAY',
                              ]
                            : [
                                'MONDAY',
                                'TUESDAY',
                                'WEDNESDAY',
                                'THURSDAY',
                                'FRIDAY',
                              ],
                        start_date: formattedStartDate,
                      },
                    ],
                  },
                ],
              },
            },
    };
    if (evDriverData?.tariffType === 'TWOTARIFF' || viewMode === 'TWOTARIFF') {
      action.value.pending_tariff.tariff_elements.push({
        comment: comment,
        functional_tariff_id: tariff
          ? tariff.tariff_elements[0].functional_tariff_id
          : null,
        price_components: [
          {
            tariff_type: 'ENERGY',
            tariff_sub_type: evDriverData?.tariffType,
            price: offPeakPrice,
            step_size: 1,
          },
        ],
        restrictions: [
          {
            start_time: convertTime(offPeakFrom),
            end_time: convertTime(offPeakTo),
            day_of_week: sameAsWeekdays
              ? [
                  'MONDAY',
                  'TUESDAY',
                  'WEDNESDAY',
                  'THURSDAY',
                  'FRIDAY',
                  'SATURDAY',
                  'SUNDAY',
                ]
              : ['MONDAY', 'TUESDAY', 'WEDNESDAY', 'THURSDAY', 'FRIDAY'],
            start_date: formattedStartDate,
          },
        ],
      });
      if (!sameAsWeekdays) {
        action.value.pending_tariff.tariff_elements.push({
          comment: comment,
          functional_tariff_id: tariff
            ? tariff.tariff_elements[0].functional_tariff_id
            : null,
          price_components: [
            {
              tariff_type: 'ENERGY',
              tariff_sub_type: 'ONETARIFF',
              price: offPeakPrice,
              step_size: 1,
            },
          ],
          restrictions: [
            {
              start_time: null,
              end_time: null,
              day_of_week: ['SATURDAY', 'SUNDAY'],
              start_date: formattedStartDate,
            },
          ],
        });
      }
    }
    evDriverDispatch(action);
    setReminder(reminderInput.active ? reminderInput.interval : undefined);
  };

  const convertNumber = (e, callback, isCO2) => {
    let input = e.target.value;

    input = isCO2
      ? input.replace?.(/0*(\d+\.?\d{0,2}).*/g, '$1')
      : input.replace?.(/0*(\d+\.?\d{0,6}).*/g, '$1');
    callback(input);
  };

  const onUpload = async (tariffFile) => {
    const reader = new FileReader();
    reader.onload = async () => {
      const { result } = reader;
      const { name, size } = tariffFile;
      try {
        // Add the new friend!
        await db.files.put({
          name,
          data: result,
          blobData: tariffFile,
          size,
        });
      } catch (error) {
        console.error(error);
      }
    };
    reader.readAsDataURL(tariffFile);
  };

  const onOffPeakFromChange = (e) => {
    setOffPeakFrom(e);
  };
  const onOffPeakToChange = (e) => {
    setOffPeakTo(e);
  };

  const TariffTypeSelection = () => {
    return (
      <TariffTypeSelectionContainer>
        <TariffTypeSelectionOption
          autoFocus
          active={
            evDriverData?.tariffType
              ? evDriverData?.tariffType === 'ONETARIFF'
              : tariff && tariff.tariff_elements.length === 1
          }
          onClick={(event) => {
            setTariffType(event.target.value);
          }}
          value="ONETARIFF"
        >
          <TariffIcon src={iconTariff} />
          {t(`tariffTypeSelectionOneTariff${isAtDriver ? 'At' : ''}`)}
        </TariffTypeSelectionOption>

        <TariffTypeSelectionOption
          active={
            evDriverData?.tariffType
              ? evDriverData?.tariffType === 'TWOTARIFF'
              : tariff && tariff.tariff_elements.length > 1
          }
          onClick={(event) => {
            setTariffType(event.target.value);
          }}
          value="TWOTARIFF"
        >
          <TariffIcon src={iconTwoTariff} />
          {t(`tariffTypeSelectionTwoTariff${isAtDriver ? 'At' : ''}`)}
        </TariffTypeSelectionOption>
      </TariffTypeSelectionContainer>
    );
  };

  if (viewMode === 'SELECTION') {
    return (
      <PageTemplate
        headline={t('electricityTariffHeadline')}
        description={t('tariffTypeSelectionHeadline')}
        currentPage={2}
        cardNumber={evDriverData?.cardNumber}
        next={
          evDriverData?.tariffType ||
          (tariff &&
            tariff.tariff_elements[0].price_components[0].tariff_sub_type)
        }
        onBoardingMode={isViewInitial}
        onNext={() => {
          if (
            !evDriverData?.tariffType &&
            tariff &&
            tariff.tariff_elements[0].price_components[0].tariff_sub_type
          ) {
            setTariffType(
              tariff.tariff_elements[0].price_components[0].tariff_sub_type,
            );
          }
          navigate('/electricityTariff' + (id ? `?id=${id}` : ''));
        }}
        onPrevious={() =>
          isViewInitial ? navigate('/') : window.history.back()
        }
      >
        <Row>
          <TariffTypeSelection />
        </Row>
      </PageTemplate>
    );
  }

  return (
    <PageTemplate
      headline={
        isViewInitial
          ? t('headlineElectricityTariffInput')
          : t('headlineAdditionalElectricityTariffInput')
      }
      description={t('tariffDescription')}
      tooltip={{
        content: <Tooltip />,
      }}
      currentPage={3}
      cardNumber={evDriverData?.cardNumber}
      next={
        (evDriverData?.tariffType === 'TWOTARIFF'
          ? price && offPeakPrice
          : price) &&
        date &&
        isFileUploaded &&
        greenTariff.value !== ''
      }
      onBoardingMode={isViewInitial}
      onNext={isReadyForNextSite}
      onPrevious={() =>
        isViewInitial
          ? navigate('/tariffselection' + (id ? `?id=${id}` : ''))
          : window.history.back()
      }
    >
      <TariffField
        offPeakIsHigherAccepted={offPeakIsHigherAccepted}
        setOffPeakIsHigherAccepted={setOffPeakIsHigherAccepted}
        autoFocus
        viewMode={viewMode}
        priceValue={price}
        onPriceChange={(e) => {
          convertNumber(e, setPrice);
        }}
        offPeakPriceValue={offPeakPrice}
        onOffPeakPriceChange={(e) => {
          convertNumber(e, setOffPeakPrice);
        }}
        offPeakFrom={offPeakFrom}
        onOffPeakFromChange={onOffPeakFromChange}
        sameAsWeekdays={sameAsWeekdays}
        setSameAsWeekdays={setSameAsWeekdays}
        offPeakTo={offPeakTo}
        onOffPeakToChange={onOffPeakToChange}
        priceLabel={t('labelPriceInput')}
        priceValid={priceValid}
        offPeakPriceValid={offPeakPriceValid}
        offPeakTimespanValid={offPeakTimespanValid}
        onFocusPrice={() => setPriceValid(true)}
        onFocusOffPeakPrice={() => setOffPeakPriceValid(3)}
        onFocusOffPeakTimespanValid={() => setOffPeakTimespanValid(true)}
        dateTooltip={{
          content: <DateTooltip />,
        }}
      />
      <Row>
        <InputWrapper isMobile={isMobile}>
          <Description error={!dateValid}>
            {t('tariffValidFrom')}
            <TooltipTextWrapper>
              <QuestionMark
                tooltip={{
                  content: <StartDateTooltip isGreenEnergy={false} />,
                  type: isViewInitial
                    ? 'initial_start_date_electricity_tariff'
                    : 'start_date_electricity_tariff',
                }}
                small
              >
                ?
              </QuestionMark>
            </TooltipTextWrapper>
          </Description>

          <DateField
            error={!dateValid}
            value={date}
            onChange={(e) => setDate(e)}
          />
          {dateValid ? <></> : <Error>{t('tariffErrorSameStartDate')}</Error>}
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper isMobile={isMobile}>
          <Description error={greenTariff.error}>
            {t('greenTariffHeadline')}
            <TooltipTextWrapper>
              <QuestionMark
                tooltip={{
                  content: <StartDateTooltip isGreenEnergy={true} />,
                  type: isViewInitial
                    ? 'initial_green_energy__electricity_tariff'
                    : 'green_energy__electricity_tariff',
                  maxWidth: '400px',
                }}
                small
              >
                ?
              </QuestionMark>
            </TooltipTextWrapper>
          </Description>
          <Select
            big
            error={greenTariff.error}
            value={greenTariff.value}
            onFocus={() => setGreenTariff({ ...greenTariff, error: false })}
            onChange={(e) => {
              setGreenTariff({ ...greenTariff, value: e.target.value });
            }}
            displayEmpty
            renderValue={
              greenTariff.value !== ''
                ? undefined
                : () => <Placeholder>{t('greenTariffPlaceholder')}</Placeholder>
            }
          >
            {tariffSustainabilityTypes.map((entry) => (
              <StyledMenuItem
                key={entry}
                value={entry}
                data-cy={`${entry}Select`}
                onClick={() => setClickedSelection(true)}
              >
                {t(`${entry}GreenTariff`)}
              </StyledMenuItem>
            ))}
          </Select>
          <InformationContainer>
            <InformationText>{t('greenTariffInfoBox')}</InformationText>
          </InformationContainer>
        </InputWrapper>
      </Row>
      {greenTariff.value !== '' && greenTariff.value !== 'null' && (
        <Row>
          <InputWrapper>
            <Description error={!emissionValid}>
              {t('emissionRateDescription')}
            </Description>
            <CurrencyField
              isCO2
              autoFocus
              disabled={greenTariff.value}
              value={co2}
              onChange={(e) => convertNumber(e, setCo2, true)}
              error={!emissionValid}
              onFocus={() => setEmissionValid(true)}
            />
            {(!emissionValid && (
              <Error data-cy="emissionValidationError">
                {t('emissionValidationError')}
              </Error>
            )) || <Hint>{t('emissionRateHelpText')}</Hint>}
          </InputWrapper>
        </Row>
      )}
      <Row>
        <Description bold>{t('electricityContractHintHeadline')}</Description>
      </Row>
      <Row>
        <InfoWrapper width="30%">
          <StyledDocumentIcon />
        </InfoWrapper>
        <InfoWrapper width="70%">
          <UploadInfoRow>
            <UploadInfoCircle>
              <NumberText>1</NumberText>
            </UploadInfoCircle>
            <UploadInfoText>{t('electricityContractHint1')}</UploadInfoText>
          </UploadInfoRow>
          <UploadInfoRow>
            <UploadInfoCircle>
              <NumberText>2</NumberText>
            </UploadInfoCircle>
            <UploadInfoText>{t('electricityContractHint2')}</UploadInfoText>
          </UploadInfoRow>
          <UploadInfoRow>
            <UploadInfoCircle>
              <NumberText>3</NumberText>
            </UploadInfoCircle>
            <UploadInfoText>{t('electricityContractHint3')}</UploadInfoText>
          </UploadInfoRow>
          <UploadInfoRow>
            <UploadInfoCircle>
              <NumberText>4</NumberText>
            </UploadInfoCircle>
            <UploadInfoText>{t('electricityContractHint4')}</UploadInfoText>
          </UploadInfoRow>
        </InfoWrapper>
      </Row>

      <RowUpload>
        <Description>
          {tActions('uploadElectricityTariff')}
          {isAtDriver && (
            <TooltipTextWrapper>
              <QuestionMark
                tooltip={{
                  content: <UploadTooltip />,
                }}
                small
              >
                ?
              </QuestionMark>
            </TooltipTextWrapper>
          )}
        </Description>
        <UploadField
          multiFile
          fileErrorMessage={tActions('errorSizeMultiFile', { fileSize: 9 })}
          fileNames={(() => {
            const fileObj = {};
            dbFileData.forEach((e) => {
              fileObj[e.name] = true;
            });
            return fileObj;
          })()}
          maxFileSize={(() => {
            const maxSize = 9437184;
            let size = 0;
            dbFileData.forEach((e) => {
              size = size + e.size;
            });
            return maxSize - size;
          })()}
          fileTypes={{
            'application/pdf': true,
            'image/png': true,
            'image/jpeg': true,
            'image/heic': true,
            'image/heif': true,
          }}
          onUpload={onUpload}
        />
        <Description style={{ marginTop: '0.5rem' }}>
          {t('supportedFileTypesTarif')}
        </Description>
        <MultiFileDisplay
          files={dbFileData}
          onFileDelete={async (fileName) => {
            try {
              await db.files.where({ name: fileName }).delete();
            } catch (e) {
              console.error(e);
            }
          }}
        />
      </RowUpload>
      <CommentRow>
        <InputWrapper isMobile>
          <Description>{t('Comments')}</Description>
          <TextArea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            maxLength={255}
            placeholder={t('commentPlaceholder')}
            rows={5}
          ></TextArea>
          <Counter full={comment?.length === 255}>
            {255 - comment?.length || 255}/255
          </Counter>
        </InputWrapper>
      </CommentRow>
      {isViewInitial && (
        <Row>
          <Reminder
            hint={t('reminderHintInitial')}
            value={reminderInput}
            onChange={setReminderInput}
          />
        </Row>
      )}
    </PageTemplate>
  );
};

ElectricityTariff.defaultProps = {
  isViewInitial: false,
};

ElectricityTariff.propTypes = {
  isViewInitial: PropTypes.bool,
};

export default ElectricityTariff;
