import { useState, useContext } from 'react';
import { Grid, Box } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import qs from 'querystring';
import BankIcon from '../../static/img/icon-bank.svg';
import LocationIcon from '../../static/img/icon-location.svg';
import TariffIcon from '../../static/img/icon-tariff.svg';
import PageTemplate, {
  InformationContainer,
  InformationIcon,
  InformationText,
} from '../../Components/evDriver/PageTemplate';
import { Checkbox, Error, LegacyTariffs } from '../../Components';

import Languages from '../../Components/LanguageSelection';

import {
  evDriverContext,
  reminderContext,
  userContext,
} from '../../ContextProvider';
import yellowInfoIcon from '../../static/img/icons/Icon_Info_Yellow.svg';
import CountryFlag from '../../Components/evDriver/CountryFlag';
import requester from '../../utils/requester';
import { isDateValid } from '../../utils/helper';
import { db } from '../../db';
import { useLiveQuery } from 'dexie-react-hooks';
import DriveEtaIcon from '@material-ui/icons/DriveEta';
import FlagIcon from '@material-ui/icons/Flag';
import DialogWrapper from '../../Components/DialogWrapper';
import { Md5 } from 'ts-md5';
import jwtDecode from 'jwt-decode';
import TagManager from 'react-gtm-module';

const useStyles = makeStyles({
  container: {
    width: '100%',
    maxWidth: 812,
  },
  main: {
    maxWidth: 600,
    margin: '0 auto',
  },
  viewHeader: {
    textAlign: 'left',
  },
  errorText: {
    color: 'var(--trafineo-rot-100)',
    paddingLeft: '1rem',
  },
  row: {
    display: 'flex',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bold: {
    fontWeight: 'bold',
  },
  button: {
    width: '220px',
  },
  inputWrapper: {
    padding: 4,
  },
  grid: {
    boxSizing: 'border-box',
    position: 'relative',
  },
  filledBox: {
    display: 'flex',
    flexDirection: 'column',
    border: 'solid 1px var(--trafineo-grau-70)',
    backgroundColor: 'var(--trafineo-grau-20)',
  },
  filledBoxNew: {
    display: 'flex',
    flexDirection: 'column',
  },
  filledBoxRow: {
    textAlign: 'left',
    justifyContent: 'space-between',
    display: 'flex',
    alignItems: 'center',
    padding: '1rem',
  },
  filledBoxRowOverview: {
    textAlign: 'left',
    display: 'flex',
    alignItems: 'center',
    padding: '1rem',
    '& p': {
      marginLeft: '1rem',
    },
  },
  filledBoxRowOverviewNew: {
    textAlign: 'left',
    display: 'flex',
    alignItems: 'center',
    padding: '1rem 0',
    '& p': {
      marginLeft: '0.5rem',
      fontSize: 14,
      lineHeight: '21px',
      fontWeight: 400,
    },
  },
  borderBox: {
    padding: '1rem',
    position: 'relative',
  },
  borderBoxRow: {
    display: 'flex',
    padding: '0.5rem',
    textAlign: 'left',
    '& p': {
      fontSize: '14px',
      lineHeight: '21px',
      fontWeight: 400,
      margin: 0,
    },
  },
  modified: {
    border: 'solid 2px var(--trafineo-rot-100)',
  },
  buttonContainer: {
    padding: '1rem 0.5rem',
    marginTop: '1.5rem',
  },
  imgContainer: {
    paddingRight: '0.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  fullWidth: {
    width: '100%',
    margin: 0,
  },
  margin: { margin: '0 1rem', minWidth: '74px' },
  progressWrapper: {
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
  },
  tooltipWrapper: {
    marginTop: '-3px',
  },
  icon: {
    position: 'absolute',
    left: 0,
    width: '15px',
    top: '26px',
  },
  iconCar: {
    position: 'absolute',
    left: 0,
    width: '15px',
    top: '22px',
  },
  iconTariff: {
    position: 'absolute',
    left: 0,
    width: '15px',
    top: '28px',
  },
  iconLoc: {
    position: 'absolute',
    left: 0,
    width: '20px',
    top: '22px',
  },
});

const OnboardingSummary = () => {
  const classes = useStyles();
  const { t } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;
  const tHomeCharging = useTranslation('homeCharging').t;
  const navigate = useNavigate();
  const location = useLocation();
  const { evDriverData } = useContext(evDriverContext);
  const { user } = useContext(userContext);

  let email = '';
  try {
    email = jwtDecode(user.access_token).preferred_username;
  } catch (e) {
    email = '';
  }
  const { reminder } = useContext(reminderContext);

  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { driverMail = '' } = parsedQueryString;

  const countryCode = evDriverData?.wallbox?.country;
  const evseId = evDriverData?.wallbox?.evseId;
  const firstname = evDriverData?.firstname;
  const lastname = evDriverData?.lastname;
  const address = evDriverData?.wallbox?.address;
  const vehicle = evDriverData?.vehicle;
  const licencePlate = evDriverData?.licencePlate;
  const iban = evDriverData?.bankData?.iban?.new;
  const dateOfEntry = evDriverData?.bankData?.validFrom?.new;
  const pendingTariff = {
    ...evDriverData?.pending_tariff,
    functional_tariff_id:
      evDriverData?.pending_tariff.tariff_elements[0].functional_tariff_id,
  };
  const twoEyePrinciple = evDriverData?.principle === 'twoeye';

  // visibility of dialog
  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);
  const [confirmData, setConfirmData] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const tOnboarding = useTranslation('onboarding').t;
  const [reminderError, setReminderError] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [chosenLanguage, setChosenLanguage] = useState(
    evDriverData?.language?.new,
  );
  const files = useLiveQuery(() => db.files.toArray()) || [];

  const showError = (message) => {
    setIsErrorVisible(true);
    setErrorMessage(message);
  };

  const routeUser = () => {
    window.location.reload();
  };

  const handleSuccess = () => {
    setIsErrorVisible(false);
    setIsStatusDialogVisible(true);
  };

  const sendData = async () => {
    const processHash = Md5.hashStr(new Date().getTime() + email);
    let requestCount = 0;
    setIsLoading(true);
    const finalize = async () => {
      if (pendingTariff.tariff_elements[0].functional_tariff_id) {
        delete pendingTariff.tariff_elements[0].functional_tariff_id;
      }

      const data = {
        portal_process_id: processHash,
        firstname: firstname,
        lastname: lastname,
        wallbox: {
          location_of_wallbox: countryCode?.toUpperCase(),
          evse_id: evseId,
          wallbox_owner: true,
          address: address
            ? {
                street: address.street,
                number: address.houseNo,
                additional_information: address.additionalInformation,
                postcode: address.postalCode,
                city: address.city,
              }
            : null,
        },
        vehicle_id: vehicle.id,
        language: chosenLanguage,
        licence_plate: licencePlate,
        correctness_confirmation: true,
        tariff: pendingTariff,
        bank_data_updated: true,
      };
      TagManager.dataLayer({
        dataLayer: {
          event: 'tariff_submit',
          tariff_submit: {
            number_of_tariffs: data.tariff.tariff_elements.length,
            phase: 'Onboarding',
          },
        },
      });
      try {
        await requester()({
          method: 'post',
          url: '/Ev_Driver_Data/Post_Ev_Driver_Data',
          data,
        });

        if (reminder) {
          try {
            setReminderError(false);
            await requester()({
              method: 'put',
              url: '/reminder',
              data: {
                reminder_type: 'electricity_contract_update_reminder',
                reminder_interval: reminder,
              },
            });
          } catch (error) {
            console.error(error);
            setReminderError(true);
          }
        }
        setIsLoading(false);
        handleSuccess();
      } catch (error) {
        showError(tActions('generalRequestError'));
        setIsLoading(false);
      }
    };
    const uploadFile = async (file) => {
      try {
        var bodyFormData = new FormData();
        bodyFormData.append('file', file.blobData);
        bodyFormData.append('upload_type_id', 2);
        bodyFormData.append('portal_process_id', processHash);
        bodyFormData.append('filename', encodeURIComponent(file.name));
        await requester()({
          method: 'post',
          url: '/FileUpload',
          data: bodyFormData,
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        requestCount = requestCount + 1;
        if (requestCount === files.length) {
          finalize();
        } else {
          uploadFile(files[requestCount]);
        }
      } catch (err) {
        showError(
          tActions(
            err.response && err.response.status === 406
              ? 'infectedFileError'
              : 'generalRequestError',
          ),
        );
        setIsLoading(false);
      }
    };
    uploadFile(files[0]);
  };

  const bankData = (
    <Box className={`${classes.borderBox}`}>
      <img src={BankIcon} alt="Bankdata" className={classes.icon} />

      <Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="iban">
            <b>
              {t('iban')}
              {': '}
            </b>
            {iban &&
              `**** **** **** **** **${iban?.toString().slice(0, 2)} ${iban
                ?.toString()
                .slice(2)}`}
          </p>
        </Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="bankDate">
            <b>{t('dateOfEntry')} </b>
            {isDateValid(new Date(dateOfEntry))
              ? new Intl.DateTimeFormat('de-DE', {
                  year: 'numeric',
                  month: '2-digit',
                  day: '2-digit',
                }).format(new Date(dateOfEntry))
              : '-'}
          </p>
        </Box>
      </Box>
    </Box>
  );

  const VehicleInfo = (
    <Box className={`${classes.borderBox}`}>
      <DriveEtaIcon className={classes.iconCar} />
      <Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="licensePlate">
            <b>
              {t('licencePlate')}
              {': '}
            </b>
            {licencePlate}
          </p>
        </Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="vehicle">
            <b>
              {t('vehicle')}
              {': '}
            </b>
            {vehicle.id === 1
              ? t('otherVehicle')
              : `${vehicle.Vehicle_Make} ${vehicle.Vehicle_Model} ${vehicle.Vehicle_Model_Version}`}
          </p>
        </Box>
      </Box>
    </Box>
  );

  const LanguageSelect = (
    <Box className={`${classes.borderBox}`}>
      <FlagIcon className={classes.iconCar} />
      <Box>
        <Box className={classes.borderBoxRow}>
          <p data-cy="bankDate">
            <b>
              {tOnboarding('language')}
              {': '}
            </b>
          </p>
        </Box>
        <Box className={classes.borderBoxRow}>
          <Languages
            language={chosenLanguage}
            changeLanguage={setChosenLanguage}
          />
        </Box>
      </Box>
    </Box>
  );
  return (
    <PageTemplate
      headline={<span>{t('summaryHeadline')} </span>}
      currentPage={6}
      cardNumber={evDriverData?.cardNumber}
      next={confirmData && chosenLanguage}
      onBoardingMode
      onNext={sendData}
      nextText={twoEyePrinciple ? t('submit') : t('sendForApproval')}
      isLoading={isLoading}
      onPrevious={() => navigate('/vehicle')}
    >
      <Box className={classes.container}>
        <DialogWrapper
          showSuccess
          open={isStatusDialogVisible}
          onClose={routeUser}
          successMessage={
            <div>
              <div>
                {twoEyePrinciple
                  ? t('submitSuccess')
                  : `${t('successTextApproval')} ${driverMail}`}
              </div>
              <div>
                {reminderError
                  ? twoEyePrinciple
                    ? t('reminderError2eye')
                    : t('reminderError')
                  : ''}
              </div>
            </div>
          }
        />
        <Grid className={classes.main} container>
          <Grid item className={classes.grid} xs={12}>
            <Box className={`${classes.borderBox}`}>
              <img
                src={LocationIcon}
                alt="Wallbox"
                className={classes.iconLoc}
              />
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {t('wallboxLoc')}
                    {': '}
                  </b>
                  <span data-cy="country">{countryCode?.toUpperCase?.()}</span>
                  <CountryFlag small country={countryCode} />
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {t('wallboxId')}
                    {': '}
                  </b>
                  <span data-cy="evseId">{evseId}</span>
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {t('firstname')}
                    {': '}
                  </b>
                  <span data-cy="firstname">{firstname}</span>
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {t('lastname')}
                    {': '}
                  </b>
                  <span data-cy="lastname">{lastname}</span>
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {tHomeCharging('street')}
                    {': '}
                  </b>
                  <span data-cy="street">{address?.street}</span>
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {tHomeCharging('houseNo')}
                    {': '}
                  </b>
                  <span data-cy="houseNo">{address?.houseNo}</span>
                </p>
              </Box>
              {address?.additionalInformation && (
                <Box className={classes.borderBoxRow}>
                  <p>
                    <b>
                      {tHomeCharging('addressExtension')}
                      {': '}
                    </b>
                    <span data-cy="addressExtension">
                      {address?.additionalInformation}
                    </span>
                  </p>
                </Box>
              )}
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {tHomeCharging('city')}
                    {': '}
                  </b>
                  <span data-cy="city">{address?.city}</span>
                </p>
              </Box>
              <Box className={classes.borderBoxRow}>
                <p>
                  <b>
                    {tHomeCharging('postalCode')}
                    {': '}
                  </b>
                  <span data-cy="postalCode">{address?.postalCode}</span>
                </p>
              </Box>
            </Box>
          </Grid>
          <Grid item className={classes.grid} xs={12}>
            <img src={TariffIcon} alt="Tariff" className={classes.iconTariff} />
            <LegacyTariffs
              comment={
                pendingTariff.comment ||
                pendingTariff.tariff_elements[0].comment
              }
              pendingTariff={pendingTariff}
              files={files}
              reminder={reminder ? reminder : 0}
              hideIcon
              newTheme
            />
          </Grid>
          <Grid item className={classes.grid} xs={12}>
            {bankData}
          </Grid>
          <Grid item className={classes.grid} xs={12}>
            {VehicleInfo}
          </Grid>
          <Grid item className={classes.grid} xs={12}>
            {LanguageSelect}
          </Grid>
          <Grid item className={classes.grid} xs={12}>
            <Box className={classes.filledBoxNew}>
              <Box className={classes.filledBoxRowOverviewNew}>
                <Checkbox
                  autofocus
                  id="confirmData"
                  checked={confirmData}
                  onChange={() => {
                    setConfirmData(!confirmData);
                  }}
                />
                <p>{t('confirmDataText')}</p>
              </Box>
            </Box>
          </Grid>
          <InformationContainer>
            <InformationIcon>
              <img src={yellowInfoIcon} alt="success" />
            </InformationIcon>
            <InformationText>{t('wallboxOnlineInfo')}</InformationText>
          </InformationContainer>
          <Grid item className={classes.grid} xs={12}>
            <Error visible={isErrorVisible} text={errorMessage} />
          </Grid>
        </Grid>
      </Box>
    </PageTemplate>
  );
};

export default OnboardingSummary;
