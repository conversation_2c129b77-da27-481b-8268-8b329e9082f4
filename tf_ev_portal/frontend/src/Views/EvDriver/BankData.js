import { useContext, useEffect, useState } from 'react';
import styled from 'styled-components';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import qs from 'querystring';
import PropTypes from 'prop-types';
import Center from '../../Components/helper/Center';
import requester from '../../utils/requester';
import Error from '../../Components/Error';
import CircularProgress from '../../Components/CircularProgress';
import { evDriverContext, userContext } from '../../ContextProvider';
import { isDateValid } from '../../utils/helper';
import PageTemplate, { Row } from '../../Components/evDriver/PageTemplate';
import { isMobile } from 'react-device-detect';
import EditWrapper from '../../Components/EditWrapper';
import { EditButton } from '../../Components/ActionButtons';
import SuccessDialog from '../../Components/SuccessDialog';
/**
 * By hitting the enter/replace a redirect url tto an external site is requested and the ev driver is redirected to that url for entering his bank data.
 * After that he is redirected back to bank data view and the last 4 ibans he just enterned are requested. That and the actual date
 * form the ev driver bank data.
 * isViewInitial === true: If the ev driver already has entered his bank data he also can hit confirm to keep that data.
 * He then is forwarded to the summary view.
 * isViewInitial === false: The ev driver then is returned to the modify view.
 */

const TextContainer = styled.div`
  text-align: left;
  color: var(--default-text);
  font-size: 16px;
  line-height: 24px;
  font-weight: 400;
`;

const LoadingWrapper = styled.div`
  width: ${isMobile ? '100vw' : '625px'};
  height: 308px;
  display: flex;
  justify-content: center;
  align-items: center;
`;

const BorderContainer = styled.div`
  box-sizing: border-box;
  margin: 0 auto;
  display: flex;
  padding: 0.5rem 1rem;
  align-items: center;
  margin-bottom: 3rem;
  border: solid 1px var(--trafineo-grau-70);
`;

const BorderContainerRow = styled.p`
  font-size: 14px;
  margin: 0.75rem 0px;
  width: 100%;
  display: flex;
`;
const BorderContainerTitle = styled.div`
  font-size: 14px;
  margin: 0.75rem 0px;
  width: 100%;
  margin: 0 auto;
  width: 100%;
  align-items: center;
  font-weight: bold;
  display: flex;
`;

const ContentWrapper = styled.div`
  display: block;
  text-align: left;
`;

const CircularProgressWrapper = styled.div`
  margin: 0 auto;
  text-align: center;
`;

const TextContainerWrapper = styled.div`
  display: flex;
  justify-content: center;
  width: 100%;
  margin-bottom: 3rem;
`;

const Bold = styled.span`
  font-weight: bold;
  margin-right: 0.25rem;
`;

const BankData = ({ isViewInitial }) => {
  const { t } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;
  const navigate = useNavigate();
  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const { redirect } = qs.parse(rawQueryString);
  const { lang } = useContext(userContext);

  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [isDialogVisible, setIsDialogVisible] = useState(false);
  const [error, setError] = useState(undefined);

  useEffect(() => {
    // incoming values should be valid
    if (!evDriverData) {
      navigate('/');
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [evDriverData]);

  useEffect(() => {
    // incoming values should be valid
    if (redirect) {
      (async () => {
        let error = false;
        try {
          setIsErrorVisible(false);
          setIsLoading(true);
          const rsp = await requester()({
            method: 'put',
            url: '/Rapyd_User_Management/User_Token',
          });
          const { last_four_iban_digits } = rsp.data;
          const date = new Date();
          const validFrom = new Date(
            date.getTime() - date.getTimezoneOffset() * 60000,
          )
            .toISOString()
            .split('T')[0];
          setIsLoading(false);
          evDriverDispatch({
            type: 'bankData',
            value: {
              iban: last_four_iban_digits,
              validFrom,
            },
          });
        } catch (err) {
          error = true;
          setIsLoading(false);
          setIsErrorVisible(true);
          console.error(err);
        }
        if (!isViewInitial && !error) {
          handleSave();
        }
      })();
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [redirect]);

  const redirectToRapyd = async () => {
    try {
      setIsLoading(true);
      const rsp = await requester()({
        method: 'post',
        url: '/Rapyd_User_Management/Rapyd_User',
        data: {
          country: isViewInitial
            ? evDriverData?.wallbox?.country
            : evDriverData?.wallbox?.locationOfWallbox?.toUpperCase() ||
              evDriverData?.wallbox?.country,
          language: lang,
        },
      });
      const { redirect_url } = rsp.data;
      window.location.href = redirect_url;
    } catch (err) {
      setIsLoading(false);
      setIsErrorVisible(true);
      console.error(err);
    }
  };

  const goToNextSite = () => {
    navigate(isViewInitial ? '/vehicle' : '/editData');
  };

  const isBankDataValid = () => {
    return (
      evDriverData?.bankData?.iban?.new &&
      evDriverData?.bankData?.validFrom?.new
    );
  };

  const handleSave = async () => {
    setIsLoading(true);
    setError(undefined);
    try {
      await requester()({
        method: 'patch',
        url: '/Ev_Driver_Data/Patch_Ev_Driver_Data',
        data: {
          bank_data_updated: true,
        },
      });
      setIsDialogVisible(true);
    } catch (e) {
      console.error(e);
      setError(t('generalRequestError'));
    }
    setIsLoading(false);
  };

  const Content = (
    <div>
      <Row>
        {(isLoading && (
          <LoadingWrapper>
            <CircularProgressWrapper>
              <CircularProgress />
            </CircularProgressWrapper>
          </LoadingWrapper>
        )) || (
          <ContentWrapper>
            <TextContainerWrapper>
              <TextContainer>
                {!isViewInitial || isBankDataValid()
                  ? !isViewInitial
                    ? t('bankDataText')
                    : t('bankDataTextInitialReplace')
                  : t('bankDataTextInitial')}
              </TextContainer>
            </TextContainerWrapper>
            {(!isViewInitial || isBankDataValid()) && (
              <BorderContainer>
                <div>
                  <BorderContainerTitle>
                    {t('bankData')} <EditButton onClick={redirectToRapyd} />
                  </BorderContainerTitle>
                  <BorderContainerRow>
                    <Bold>{t('dateOfEntry')}</Bold>{' '}
                    {isDateValid(
                      new Date(evDriverData?.bankData?.validFrom?.new),
                    )
                      ? new Intl.DateTimeFormat('de-DE', {
                          year: 'numeric',
                          month: '2-digit',
                          day: '2-digit',
                        }).format(
                          new Date(evDriverData?.bankData?.validFrom?.new),
                        )
                      : '-'}
                  </BorderContainerRow>
                  <BorderContainerRow>
                    <Bold>{t('iban')}</Bold>
                    {`**** **** **** **** **${evDriverData?.bankData?.iban?.new}`}
                  </BorderContainerRow>
                </div>
              </BorderContainer>
            )}
            <Center>
              <Error
                data-cy="bankDataProcessError"
                visible={isErrorVisible}
                text={t('bankDataProcessError')}
              />
            </Center>
          </ContentWrapper>
        )}
      </Row>
    </div>
  );
  return (
    <PageTemplate
      headline={t('bankDataHeadline')}
      description={''}
      currentPage={4}
      hideButtons={!isViewInitial}
      cardNumber={evDriverData?.cardNumber}
      next={true}
      nextText={isBankDataValid() ? null : tActions('enterBankingInformation')}
      onBoardingMode={isViewInitial}
      onNext={() => (isBankDataValid() ? goToNextSite() : redirectToRapyd())}
      onPrevious={() =>
        isViewInitial ? navigate('/electricityTariff') : navigate('/editData')
      }
    >
      {isDialogVisible && (
        <SuccessDialog
          isVisible
          onClose={() =>
            isViewInitial
              ? setIsDialogVisible(false)
              : redirect
              ? window.location.replace(redirect)
              : window.location.reload()
          }
          text={t('submitSuccess')}
        />
      )}
      <div>{error && <Error>{error}</Error>}</div>
      {Content}
      {!isViewInitial && (
        <EditWrapper
          redirect="/myinformation/personalinformation"
          noButton={true}
          disabled={
            !redirect ||
            !evDriverData?.bankData?.iban?.new ||
            isErrorVisible ||
            isLoading
          }
          beforeSafe={isBankDataValid}
          data={{
            bank_data_updated: true,
          }}
        />
      )}
    </PageTemplate>
  );
};

BankData.defaultProps = {
  isViewInitial: false,
};

BankData.propTypes = {
  isViewInitial: PropTypes.bool,
};

export default BankData;
