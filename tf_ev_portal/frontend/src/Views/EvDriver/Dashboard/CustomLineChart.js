import { useTranslation } from 'react-i18next';

import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON>A<PERSON><PERSON>,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  AreaChart,
  Area,
} from 'recharts';

import styled from 'styled-components';
import { getBranding } from '../../../utils/helper';
import { useWindowHeight, useWindowWidth } from '@react-hook/window-size';
import { useEffect, useRef, useState } from 'react';

const YearText = styled.p`
  font-size: 14px;
  color: #b5b5b5;
  margin: 0px;
  margin-top: 2px;
`;

const EnergyText = styled.p`
  font-weight: 600;
  font-size: 20px;
  margin: 0px;
  margin-top: 5px;
  margin-bottom: 5px;
`;

const MonthText = styled.p`
  font-size: 14px;
  color: #b5b5b5;
  margin: 0px;
`;

const TooltipWrapper = styled.div`
  background-color: #fff;
  padding-right: 5px;
  height: 70px;
  border-radius: 5px;
  padding: 5px;
  box-shadow: 4px 4px 7px 4px rgba(0, 0, 0, 0.3);
  border: none !important;
  outline: none !important;
`;

const CustomLineChart = ({ data, isMobile }) => {
  const { t } = useTranslation('overview');

  const windowWidth = useWindowWidth();
  const windowHeight = useWindowHeight();
  const elementRef = useRef(null);
  const [width, setWidth] = useState(0);

  useEffect(() => {
    const handleResize = () => {
      setWidth(elementRef.current.current.offsetWidth);
    };
    handleResize();
    window.addEventListener('resize', handleResize);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, [windowWidth, windowHeight]);

  const CustomTooltip = ({ active, payload }) => {
    if (active && payload && payload.length) {
      return (
        <TooltipWrapper>
          <YearText>{payload[0].payload.timestamp.split('-')[0]}</YearText>
          <EnergyText>
            {(
              Math.round((payload[0].payload.value + Number.EPSILON) * 100) /
              100
            ).toFixed(2) || 0}
            {' kWh'}
          </EnergyText>
          <MonthText>{t(payload[0].payload.month)}</MonthText>
        </TooltipWrapper>
      );
    }
    return null;
  };

  const formatter = (value) => `${value.toFixed(0)} kWh`;
  const getColor = () => {
    if (getBranding() === 'aral') {
      return '#0064cc';
    } else if (getBranding() === 'bp') {
      return '#9acc00';
    } else {
      return '#c60018';
    }
  };

  return (
    <ResponsiveContainer ref={elementRef}>
      <AreaChart
        width={700}
        height={'100%'}
        data={data}
        margin={{
          top: 5,
          right: 10,
          left: -15,
          bottom: 5,
        }}
      >
        <CartesianGrid vertical={false} strokeDasharray={'0 0'} />
        <XAxis
          dataKey="name"
          interval={isMobile || width > 430 ? 'preserveEnd' : 1}
        />
        <YAxis tickFormatter={formatter} width={90} />

        <Tooltip
          wrapperStyle={{ outline: 'none' }}
          cursor={{ stroke: getColor(), strokeDasharray: 5 }}
          content={CustomTooltip}
        />

        <Area
          dataKey="value"
          type="monotone"
          stroke={
            getBranding() === 'aral'
              ? '#0064cc'
              : getBranding() === 'bp'
              ? '#9acc00'
              : '#c60018'
          }
          strokeWidth={2.5}
          fillOpacity={1}
          fill={'rgb(235, 235, 235, 0.5)'}
          dot={false}
        />
      </AreaChart>
    </ResponsiveContainer>
  );
};

export default CustomLineChart;
