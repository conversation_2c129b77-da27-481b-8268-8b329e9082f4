import { useEffect, useState, useContext } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import requester from '../../../utils/requester';
import PageWrapper from '../../../Components/PageWrapper';
import EvStationIcon from '@mui/icons-material/EvStation';
import EuroIcon from '@mui/icons-material/Euro';
import styled from 'styled-components';
import logger from '../../../utils/logger';
import { secondsToHms } from '../../../utils/helper';
import { evDriverContext } from '../../../ContextProvider';
import TagManager from 'react-gtm-module';

import { useWindowWidth } from '@react-hook/window-size';
import { isMobile } from 'react-device-detect';

import axios from 'axios';
import DriverDashboardSummary from '../../../Components/DriverDashboard/DriverDashboardSummary';
import DialogWrapper from '../../../Components/DialogWrapper';
import { Button } from '../../../Components';
import NextPayment from '../../../Components/DriverDashboard/NextPayment';
import Tooltip from '@mui/material/Tooltip';
import IconButton from '@mui/material/IconButton';
import HelpIcon from '@mui/icons-material/Help';
import ClickAwayListener from '@material-ui/core/ClickAwayListener';

import CurrentTariff from './CurrentTariff';
import PaidOut from './PaidOut';
import EnergyConsumption from './EnergyConsumption';
import Trainings from './Trainings';
import CardDisplay from '../../../Components/DriverDashboard/CardDisplay';

const Grid = styled.div`
  display: grid;
  grid-template-areas:
    'account account account account account account account expectedReimburstement expectedReimburstement expectedReimburstement  expectedReimburstement expectedReimburstement summary summary summary summary summary summary summary tariff tariff tariff tariff tariff tariff tariff'
    'paidOut paidOut paidOut paidOut paidOut paidOut paidOut paidOut paidOut  chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions chargingsessions trainings trainings trainings trainings trainings trainings trainings';
  gap: 1.5rem;
  grid-template-columns: repeat(24, 1fr);
  @media (max-width: 1365px) {
    grid-template-areas:
      'account account expectedReimburstement summary summary summary '
      'paidOut paidOut paidOut paidOut tariff tariff'
      'chargingsessions chargingsessions chargingsessions chargingsessions trainings trainings';
    gap: 1.5rem;
    grid-template-columns: repeat(6, 1fr);
  }
  @media (max-width: 995px) {
    grid-template-areas:
      'account account account expectedReimburstement'
      'paidOut paidOut paidOut paidOut'
      'chargingsessions chargingsessions chargingsessions chargingsessions'
      'summary summary summary summary'
      'tariff tariff trainings trainings';
    gap: 1.5rem;
    grid-template-columns: repeat(4, 1fr);
  }
  ${isMobile
    ? `@media (max-width: 768px) {
    grid-template-areas:
      'account'
      'trainings'
      'expectedReimburstement'
      'summary'
      'chargingsessions'
      'paidOut'
      'tariff';
    gap: 1rem;
    grid-template-columns: repeat(1, 1fr);
  }`
    : 'min-width:768px'}
`;

const GridItem = styled.div`
  grid-area: ${(props) => props.name};
  border-radius: 20.5px;
  box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.25);
  background-color: ${(props) => props.bg || '#fff'};
  ${(props) =>
    props.minHeight && !isMobile ? `min-height: ${props.minHeight}` : ''};
  /* height: auto; */
  padding: 1.5rem;
  width: ${(props) =>
    isMobile && props.windowWidth <= 768 ? 'calc(100vw - 2rem)' : '100%'};
  box-sizing: border-box;
  min-width: 250px;
`;

const ItemHeadline = styled.div`
  text-align: left;
  font-weight: bold;
  color: black;
  font-size: 22px;
  display: flex;
  align-items: center;
  position: relative;
  padding-right: 10px;
  svg {
    margin: 0 0 0 0.5rem;
    height: 20px;
  }
`;

const ButtonLink = styled(Link)`
  width: 100%;
  display: flex;
`;

const StyledTooltip = styled(Tooltip)`
  text-align: center !important;
  width: 25px;
  padding: 0 !important;
`;

const StyledIconButton = styled(IconButton)`
  position: absolute !important;
  right: 0px;
  top: 0px;
  background-color: transparent !important;
`;

const Spacer = styled.div`
  height: 15px;
  width: 100%;
`;

const DriverDashboard = () => {
  const { t } = useTranslation('evDriver');
  const { t: headerbar } = useTranslation('headerbar');
  const { t: directCustomer } = useTranslation('directCustomer');
  const [summary, setSummary] = useState({
    totalTransactions: 0,
    totalEnergy: 0,
    totalTime: '0s',
  });
  const [isLoading, setIsLoading] = useState(true);
  const [reminderOpen, setReminderOpen] = useState(
    sessionStorage.getItem('showReminder') !== null &&
      sessionStorage.getItem('showReminder') !== 'false',
  );
  const [tooltipOpen, setOpen] = useState({
    payment: false,
    info: false,
    tariff: false,
  });
  const [expectedReimbursement, setExpectedReimbursement] = useState();
  const [summaryError, setSummaryError] = useState(false);
  const [expectedReimbursementError, setExpectedReimbursementError] =
    useState(false);

  const handleTooltipClose = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: `tooltip_interaction`,
        tooltip: {
          name: `tooltip_driver_dashboard_${
            tooltipOpen.payment
              ? 'payment'
              : tooltipOpen.info
              ? 'info'
              : 'tariff'
          }`,
          interaction_type: 'close',
        },
      },
    });
    setOpen({ payment: false, info: false });
  };

  const handleTooltipOpen = (type) => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'tooltip_interaction',
        tooltip: {
          name: `tooltip_driver_dashboard_${type}`,
          interaction_type: 'open',
        },
      },
    });
    if (type === 'info') {
      setOpen({ ...tooltipOpen, info: true });
    } else if (type === 'payment') {
      setOpen({ ...tooltipOpen, payment: true });
    } else {
      setOpen({ ...tooltipOpen, tariff: true });
    }
  };
  const windowWidth = useWindowWidth();
  const navigate = useNavigate();

  const { evDriverData } = useContext(evDriverContext);

  const closeReminder = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'popup_interaction',
        popup: {
          name: `popup_profile_reminder`,
          interaction_type: 'close',
        },
      },
    });
    sessionStorage.setItem('showReminder', false);
    setReminderOpen(false);
  };

  const editPersonalData = () => {
    sessionStorage.setItem('showReminder', false);
    navigate('/MyInformation/personalinformation');
  };

  const wallboxUpdateRequested = evDriverData.approvalStatus.includes(
    'wallbox_update_requested',
  );
  const bankingUpdateRequested = evDriverData.approvalStatus.includes(
    'banking_update_requested',
  );
  const tariffUpdateRequested = evDriverData.approvalStatus.includes(
    'tariff_update_requested',
  );

  useEffect(() => {
    const alreadyShown = sessionStorage.getItem('showReminder');
    if (alreadyShown !== 'false') {
      if (
        evDriverData &&
        (!evDriverData.firstname ||
          !evDriverData.lastname ||
          !evDriverData.vehicle.Vehicle_Model ||
          !evDriverData.vehicle.licencePlate ||
          !evDriverData.wallbox.evseId ||
          !evDriverData.wallbox.address ||
          !evDriverData.wallbox.address.street ||
          !evDriverData.wallbox.address.number ||
          !evDriverData.wallbox.address.city ||
          !evDriverData.wallbox.address.postcode ||
          !evDriverData.wallbox.address.country ||
          wallboxUpdateRequested ||
          bankingUpdateRequested ||
          tariffUpdateRequested)
      ) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'popup_interaction',
            popup: {
              name: `popup_profile_reminder`,
              interaction_type: 'open',
            },
          },
        });
        setReminderOpen(true);
        sessionStorage.setItem('showReminder', true);
      }
    }
  }, [
    setReminderOpen,
    evDriverData,
    bankingUpdateRequested,
    tariffUpdateRequested,
    wallboxUpdateRequested,
  ]);

  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const today = new Date();
      try {
        const endpoints = [
          `/chargingsessions/?order_by=cdr_session_start:desc&date_from=${
            new Date(
              today.getTime() - ********** - today.getTimezoneOffset() * 60000,
            )
              .toISOString()
              .split('T')[0]
          }T00:00:00&date_to=${
            new Date(today.getTime() - today.getTimezoneOffset() * 60000)
              .toISOString()
              .split('T')[0]
          }T23:59:59&cdr_type=home`,
          `/chargingsessions/?offset=0&limit=5&cdr_type=home`,
        ];
        axios
          .all(endpoints.map((endpoint) => requester().get(endpoint)))
          .then(
            axios.spread(
              (
                { data: summaryData, headers: summaryHeaders },
                { data: chargingSessionRsp },
              ) => {
                let totalTime = 0;
                summaryData.data?.forEach((e) => {
                  totalTime = totalTime + e.total_time;
                });
                setSummary({
                  totalTransactions: summaryHeaders
                    ? summaryHeaders['x-total-count']
                    : 0,
                  totalTime: secondsToHms(totalTime).substring(0, 7),
                  totalEnergy: summaryData.meta.total_energy,
                });
                setExpectedReimbursement(
                  chargingSessionRsp.meta.total_expected_reimbursement_cost,
                );
                setIsLoading(false);
              },
            ),
          )
          .catch((error) => {
            console.error(error);
            if (error.response.status === 504) {
              logger().error(`Timeout of request for chargingsessions.`);
              setSummaryError(true);
              setExpectedReimbursementError(true);
              setIsLoading(false);
              return 'error';
            } else {
              logger().error(
                `Couldn't get overview data from api.\n${error.message}`,
              );
              setIsLoading(false);
              return {};
            }
          });
      } catch (err) {
        if (err.response.status === 504) {
          logger().error(`Timeout of request for chargingsessions.`);
          setSummaryError(true);
          setExpectedReimbursementError(true);
          setIsLoading(false);
          return 'error';
        } else {
          logger().error(
            `Couldn't get overview data from api.\n${err.message}`,
          );
          setIsLoading(false);
          return {};
        }
      }
    };
    fetchData();
  }, []);

  return (
    <div
      style={{
        backgroundColor: '#f7f6fb',
        minHeight: 'var(--app-height-driver)',
      }}
    >
      <ClickAwayListener onClickAway={handleTooltipClose}>
        <PageWrapper
          dashboard
          title={t('driverDashboard')}
          isMobile={isMobile && windowWidth <= 768}
          hasFooter={!isMobile}
        >
          <DialogWrapper
            open={reminderOpen}
            onClose={closeReminder}
            headline={t('reminderHeadline')}
          >
            <p>{t('reminderDescription')}</p>
            <br />
            <ButtonLink to="/myInformation/personalinformation">
              <Button variant="primary" onClick={editPersonalData}>
                {t('reminderButton')}
              </Button>
            </ButtonLink>
          </DialogWrapper>
          <Grid>
            <GridItem
              windowWidth={windowWidth}
              isMobile={isMobile}
              name="account"
            >
              <ItemHeadline>{headerbar('myProfile')}</ItemHeadline>
              <CardDisplay />
            </GridItem>
            <GridItem
              windowWidth={windowWidth}
              isMobile={isMobile}
              name="expectedReimburstement"
            >
              <ItemHeadline>
                {t('nextPayment')}
                <StyledTooltip
                  PopperProps={{
                    disablePortal: true,
                  }}
                  onClose={handleTooltipClose}
                  open={tooltipOpen.payment}
                  title={t('nextPaymentHint')}
                >
                  <StyledIconButton
                    onClick={(e) => handleTooltipOpen('payment')}
                  >
                    <HelpIcon />
                  </StyledIconButton>
                </StyledTooltip>
              </ItemHeadline>
              <NextPayment
                isMobile={isMobile && windowWidth <= 768}
                error={expectedReimbursementError}
                amount={
                  (
                    Math.round((expectedReimbursement + Number.EPSILON) * 100) /
                    100
                  ).toFixed(2) || 0
                }
                isLoading={isLoading}
              />
            </GridItem>
            <GridItem
              windowWidth={windowWidth}
              isMobile={isMobile}
              minHeight="307px"
              name="summary"
            >
              <ItemHeadline>
                {t('dashboardSummaryHeadline')}
                <StyledTooltip
                  PopperProps={{
                    disablePortal: true,
                  }}
                  onClose={handleTooltipClose}
                  open={tooltipOpen.info}
                  title={t('summaryHint')}
                >
                  <StyledIconButton onClick={(e) => handleTooltipOpen('info')}>
                    <HelpIcon />
                  </StyledIconButton>
                </StyledTooltip>
              </ItemHeadline>
              <DriverDashboardSummary
                isMobile={isMobile && windowWidth <= 768}
                data={summary}
                error={summaryError}
                isLoading={isLoading}
              />
            </GridItem>
            <GridItem name="trainings">
              <ItemHeadline>{directCustomer('trainingsHeadline')}</ItemHeadline>
              <Trainings isDriver />
            </GridItem>
            <GridItem name="tariff">
              <ItemHeadline>
                {t('myTariff')}
                <StyledTooltip
                  PopperProps={{
                    disablePortal: true,
                  }}
                  onClose={handleTooltipClose}
                  open={tooltipOpen.tariff}
                  title={t('tariffTooltip')}
                >
                  <StyledIconButton
                    onClick={(e) => handleTooltipOpen('tariff')}
                  >
                    <HelpIcon />
                  </StyledIconButton>
                </StyledTooltip>
              </ItemHeadline>
              <CurrentTariff
                tariff={evDriverData.tariffs.find(
                  // check if tariff currently applies
                  (tariff) =>
                    Date.now() >=
                      new Date(
                        tariff.tariff_elements[0].restrictions[0].start_date,
                      ).setHours(0, 0, 0) &&
                    Date.now() <=
                      new Date(
                        tariff.tariff_elements[0].restrictions[0].end_date,
                      ).setHours(23, 59, 59, 999),
                )}
              />
            </GridItem>
            <GridItem
              windowWidth={windowWidth}
              isMobile={isMobile}
              minHeight="450px"
              name="chargingsessions"
            >
              <ItemHeadline>
                <EvStationIcon />
                {t('energyConsumption')}
              </ItemHeadline>
              <Spacer />
              <EnergyConsumption isMobile={isMobile && windowWidth <= 768} />
            </GridItem>
            <GridItem
              windowWidth={windowWidth}
              isMobile={isMobile}
              minHeight="450px"
              name="paidOut"
            >
              <ItemHeadline>
                <EuroIcon />
                {t('paidOut')}
              </ItemHeadline>
              <Spacer />
              <PaidOut isMobile={isMobile && windowWidth <= 768} />
            </GridItem>
          </Grid>
        </PageWrapper>
      </ClickAwayListener>
    </div>
  );
};

export default DriverDashboard;
