import IconDay from '../../../static/img/icons/Icon-day.svg';
import IconNight from '../../../static/img/icons/Icon-offpeak.svg';

import { Link } from 'react-router-dom';
import { useTranslation } from 'react-i18next';

import styled from 'styled-components';
import { isMobile } from 'react-device-detect';

const StyledLink = styled(Link)`
  position: absolute;
  bottom: 20px;
  left: 0px;
  color: var(--primary);
  text-decoration: underline;
  font-size: 16px;
  line-height: 24px;
  ::after {
    content: ' >';
  }
`;

const Wrapper = styled.div`
  position: relative;
  height: 100%;
`;

const CenterIcon = styled.div`
  margin: auto;
  width: fit-content;
  padding-top: 50px;
`;

const Price = styled.p`
  text-align: center;
  font-size: 22px;
  font-weight: 600;
  margin-bottom: 5px;
`;

const Info = styled.p`
  font-size: 11px;
  margin: 0;
  text-align: center;
`;

const TariffType = styled.p`
  font-size: 15px;
  text-align: center;
  font-weight: 600;
  margin-bottom: ${(props) => (isMobile ? '30px' : '15px')};
`;

const TwoTariffWrapper = styled.div`
  width: 50%;
  display: inline-block;
`;

const TariffIcon = styled.img`
  height: 70px;
`;

const CurrentTariff = ({ tariff }) => {
  const { t } = useTranslation('evDriver');
  const isTwoTariff = tariff?.tariff_elements.length > 1;
  return (
    <Wrapper>
      {isTwoTariff && (
        <>
          <TwoTariffWrapper>
            <CenterIcon>
              <TariffIcon src={IconDay} alt="sun icon" />
            </CenterIcon>

            <Price>
              {(
                Math.round(
                  (tariff?.tariff_elements[0].price_components[0].price +
                    Number.EPSILON) *
                    100,
                ) / 100
              ).toFixed(2) || 0}
            </Price>
            <Info>EUR/kWh</Info>
            <TariffType isMobile={isMobile}>{t('tariffNormal')}</TariffType>
          </TwoTariffWrapper>
          <TwoTariffWrapper>
            <CenterIcon>
              <TariffIcon src={IconNight} alt="sun icon" />
            </CenterIcon>

            <Price>
              {(
                Math.round(
                  (tariff?.tariff_elements[1].price_components[0].price +
                    Number.EPSILON) *
                    100,
                ) / 100
              ).toFixed(2) || 0}
            </Price>
            <Info>EUR/kWh</Info>
            <TariffType isMobile={isMobile}>{t('offpeakTariff')}</TariffType>
          </TwoTariffWrapper>
        </>
      )}
      {!isTwoTariff && (
        <>
          <CenterIcon>
            <TariffIcon src={IconDay} alt="sun icon" />
          </CenterIcon>
          <Price>
            {!tariff && '-'}
            {tariff &&
              (
                Math.round(
                  (tariff?.tariff_elements[0].price_components[0].price ||
                    0 + Number.EPSILON) * 100,
                ) / 100
              ).toFixed(2)}
          </Price>
          <Info>EUR/kWh</Info>
          <TariffType isMobile={isMobile}>{t('tariffNormal')}</TariffType>
        </>
      )}
      <StyledLink to="myinformation/mytariffs">{t('updateTariffs')}</StyledLink>
    </Wrapper>
  );
};

export default CurrentTariff;
