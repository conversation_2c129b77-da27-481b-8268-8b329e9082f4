import { useContext, useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { isMobile } from 'react-device-detect';
import PropTypes from 'prop-types';

// Components
import PageTemplate, {
  InformationContainer,
  InformationText,
  InformationIcon,
  Description,
  StyledMenuItem,
  Placeholder,
} from '../../Components/evDriver/PageTemplate';
import CountryFlag from '../../Components/evDriver/CountryFlag';
import { Button, TextField } from '../../Components';
import EditWrapper from '../../Components/EditWrapper';
import DialogWrapper from '../../Components/DialogWrapper';
import { useCallbackPrompt } from '../../Components/useCallbackPrompt';
import { Grid } from '@mui/material';
import { InputWrapper } from '../../Components/evDriver/PageTemplate';
import Select from '../../Components/Select';

import { evDriverContext } from '../../ContextProvider';
import { countries } from '../../constants/localization';
import operators from '../../constants/operators';
import i18next from '../../i18n';
import styled from 'styled-components';

// Images
import EvseIdTooltip from '../../static/img/evseTootip.png';
import SerivalNumberTooltip from '../../static/img/SerivalNumberTooltip.jpeg';
import SerivalNumberTooltip2 from '../../static/img/SerivalNumberTooltip2.jpeg';
import DeDriverTooltip from '../../static/img/deDriverTooltip.png';
import operatorIdDE from '../../static/img/operatorIdDE.png';
import operatorIdNL from '../../static/img/operatorIdNL.png';
import operatorIdEN from '../../static/img/operatorIdEN.png';

// Icons
import infoIcon from '../../static/img/icons/Info_Icon.svg';
import CircleIcon from '@mui/icons-material/Circle';
import ErrorIcon from '@mui/icons-material/Error';

/**
 * isViewInitial === true: The ev driver selects his country and by confirming moves on to the evse id view.
 * isViewInitial === false: The ev driver modifies his country and by confirming goes back to the modify view.
 */

const StyledUpdateIcon = styled(ErrorIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: #ff7e00 !important;
`;

const StyledCircleIcon = styled(CircleIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: var(--trafineo-rot-100) !important;
`;

const Row = styled.div`
  display: flex;
  width: 100%;
  margin-bottom: 1rem;
  flex-direction: ${isMobile ? 'column' : 'row'};
`;
const Col = styled.div`
  width: ${isMobile ? '100%' : '50%'};
  box-sizing: border-box;
  padding-bottom: ${isMobile ? '1rem' : '0rem'};
  &:last-child {
    padding-right: 0rem;
    padding-left: ${isMobile ? '0rem' : '0.5rem'};
  }
  padding-right: ${isMobile ? '0rem' : '0.5rem'};
`;

const ColSingle = styled.div`
  width: ${isMobile ? '100%' : '50%'};
  box-sizing: border-box;
  padding-right: ${isMobile ? '0rem' : '0.5rem'};
  margin-bottom: 1rem;
`;

const TooltipHeadline = styled.div`
  display: flex;
  white-space: pre-wrap;
  margin-bottom: 1.25rem;
`;
const BaseImage = styled.img`
  max-width: 500px;
`;
const DeDriverImage = styled.img`
  width: ${isMobile ? '100%' : '330px'};
  margin: 0.5rem;
`;
const SerialNumberImage = styled.img`
  width: ${isMobile ? '100%' : '330px'};
  margin: 0.5rem;
`;
const TooltipImage = styled.div`
  position: relative;
`;
const AutocorrectContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: ${isMobile ? '100%' : '625px'};
`;
const CustomError = styled.div`
  white-space: pre-line;
  color: var(--error-color);
  text-align: left;
  font-size: 14px;
  line-height: 21px;
  padding-top: 0.25rem;
  width: ${isMobile ? '100%' : '625px'};
`;

const ButtonContainer = styled.div`
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  margin-top: 1rem;
  button {
    margin-left: 0.5rem;
    width: fit-content;
  }
`;

const CenterGrid = styled(Grid)`
  text-align: center;
`;

const Welcome = ({ isViewInitial, onSave }) => {
  const { t } = useTranslation('evDriver');
  const tLocalization = useTranslation('localization').t;
  const tHomeCharging = useTranslation('homeCharging').t;
  const navigate = useNavigate();

  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);
  const [country, setCountry] = useState({
    value:
      evDriverData?.wallbox?.locationOfWallbox?.toLowerCase() ||
      evDriverData?.wallbox?.country ||
      '',
    error: false,
  });

  const isAtDriver =
    evDriverData?.wallbox?.locationOfWallbox?.toLowerCase() === 'at';
  const isDeDriver =
    !evDriverData?.wallbox?.locationOfWallbox ||
    evDriverData?.wallbox?.locationOfWallbox?.toLowerCase() === 'de';
  const isNlDriver =
    evDriverData?.wallbox?.locationOfWallbox?.toLowerCase() === 'nl';
  const [evseId, setEvseId] = useState(
    evDriverData?.wallbox?.evseId
      ? evDriverData?.wallbox?.evseId
      : isAtDriver
      ? 'AT*BPE*E'
      : isNlDriver
      ? 'NL*NUO*E'
      : '',
  );
  const [operator, setOperator] = useState({
    value:
      evDriverData?.wallbox?.evseId?.indexOf('DE*BPE*E') === 0
        ? 'BPE (DE*BPE*E)'
        : evDriverData?.wallbox?.evseId?.indexOf('DE*ARP*E') === 0
        ? 'ARP (DE*ARP*E)'
        : evDriverData?.wallbox?.evseId?.indexOf('DE*VAT*E') === 0
        ? 'VAT (DE*VAT*E)'
        : null,
    error: false,
  });
  const [firstname, setFirstName] = useState({
    value: evDriverData?.firstname || undefined,
    error: false,
  });

  const [lastname, setLastname] = useState({
    value: evDriverData?.lastname || undefined,
    error: false,
  });
  const [error, setError] = useState(false);
  const [errorType, setErrorType] = useState(false);
  const [street, setStreet] = useState({
    value: evDriverData?.wallbox?.address?.street || '',
    error: false,
  });
  const [houseNo, setHouseNo] = useState({
    value:
      evDriverData?.wallbox?.address?.houseNo ||
      evDriverData?.wallbox?.address?.number ||
      '',
    error: false,
  });
  const [additionalInformation, setAdditionalInformation] = useState({
    value:
      evDriverData?.wallbox?.address?.additional_information ||
      evDriverData?.wallbox?.address?.additionalInformation ||
      '',
    error: false,
  });
  const [postalCode, setPostalCode] = useState({
    value:
      evDriverData?.wallbox?.address?.postalCode ||
      evDriverData?.wallbox?.address?.postcode ||
      '',
    error: false,
  });
  const [city, setCity] = useState({
    value: evDriverData?.wallbox?.address?.city || '',
    error: false,
  });

  const [showDialog, setShowDialog] = useState(false);
  const [showPrompt, confirmNavigation, cancelNavigation] =
    useCallbackPrompt(showDialog);

  const wallboxUpdateRequested = evDriverData.approvalStatus.includes(
    'wallbox_update_requested',
  );

  const evseIdUnchanged =
    evseId === '' || evDriverData?.wallbox?.evseId === evseId;

  const wallboxHasNoAddress =
    !evDriverData?.wallbox?.address ||
    Object.keys(evDriverData?.wallbox?.address).length === 0 ||
    (evDriverData?.wallbox?.address?.street === undefined &&
      evDriverData?.wallbox?.address?.number === undefined &&
      evDriverData?.wallbox?.address?.city === undefined &&
      evDriverData?.wallbox?.address?.postcode === undefined &&
      evDriverData?.wallbox?.address?.additional_information === undefined);

  const wallboxUnchanged =
    (evDriverData?.wallbox?.address?.street === street.value &&
      evDriverData?.wallbox?.address?.number === houseNo.value &&
      evDriverData?.wallbox?.address?.city === city.value &&
      evDriverData?.wallbox?.address?.postcode === postalCode.value &&
      evDriverData?.wallbox?.address?.additional_information ===
        additionalInformation.value) ||
    (wallboxHasNoAddress &&
      (street.value === '' || street.value === undefined) &&
      (houseNo.value === '' || houseNo.value === undefined) &&
      (city.value === '' || city.value === undefined) &&
      (postalCode.value === '' || postalCode.value === undefined) &&
      (additionalInformation.value === '' ||
        additionalInformation.value === undefined));

  const missingWallboxAddress =
    !evDriverData.wallbox.address ||
    !evDriverData.wallbox.address.street ||
    !evDriverData.wallbox.address.number ||
    !evDriverData.wallbox.address.city ||
    !evDriverData.wallbox.address.postcode ||
    !evDriverData.wallbox.address.country;

  const missingEvseId = !evDriverData.wallbox.evseId;

  const cancelChanges = () => {
    setEvseId(
      evDriverData?.wallbox?.evseId
        ? evDriverData?.wallbox?.evseId
        : isAtDriver
        ? 'AT*BPE*E'
        : isNlDriver
        ? 'NL*NUO*E'
        : '',
    );
    setOperator({
      value:
        evDriverData?.wallbox?.evseId?.indexOf('DE*BPE*E') === 0
          ? 'BPE (DE*BPE*E)'
          : evDriverData?.wallbox?.evseId?.indexOf('DE*ARP*E') === 0
          ? 'ARP (DE*ARP*E)'
          : evDriverData?.wallbox?.evseId?.indexOf('DE*VAT*E') === 0
          ? 'VAT (DE*VAT*E)'
          : null,
      error: false,
    });
    setFirstName({
      value: evDriverData?.firstname || undefined,
      error: false,
    });

    setLastname({
      value: evDriverData?.lastname || undefined,
      error: false,
    });
    setStreet({
      value: evDriverData?.wallbox?.address?.street || '',
      error: false,
    });
    setHouseNo({
      value:
        evDriverData?.wallbox?.address?.houseNo ||
        evDriverData?.wallbox?.address?.number ||
        '',
      error: false,
    });
    setAdditionalInformation({
      value:
        evDriverData?.wallbox?.address?.additional_information ||
        evDriverData?.wallbox?.address?.additionalInformation ||
        '',
      error: false,
    });
    setPostalCode({
      value:
        evDriverData?.wallbox?.address?.postalCode ||
        evDriverData?.wallbox?.address?.postcode ||
        '',
      error: false,
    });
    setCity({
      value: evDriverData?.wallbox?.address?.city || '',
      error: false,
    });
  };
  useEffect(() => {
    if ((wallboxUnchanged && evseIdUnchanged) || isViewInitial) {
      setShowDialog(false);
    } else {
      setShowDialog(true);
    }
  }, [
    evDriverData,
    evseIdUnchanged,
    wallboxUnchanged,
    wallboxHasNoAddress,
    isViewInitial,
  ]);

  const isReadyForNextSite = () => {
    let error = false;
    if (
      !evseId.match(
        /([A-Za-z]{2}\*?[A-Za-z0-9]{3}\*?E[A-Za-z0-9*]{1,30})|(\+?[0-9]{1,3}\*[0-9]{3,6}\*[0-9*]{1,32})/gm,
      )
    ) {
      setErrorType(1);
      setError(true);
      error = true;
    } else if (
      (evseId.toUpperCase().indexOf('DE*VAT') === 0 ||
        evseId.toUpperCase().indexOf('NL*NUO') === 0) &&
      evseId[8] === '*'
    ) {
      setErrorType(0);
      setError(true);
      error = true;
    } else if (
      (evseId.toUpperCase().indexOf('DE*BPE') === 0 ||
        evseId.toUpperCase().indexOf('DE*ARP') === 0) &&
      evseId.slice(evseId.length - 3) !== '*01' &&
      evseId.slice(evseId.length - 3) !== '*02'
    ) {
      setErrorType(2);
      setError(true);
      error = true;
    } else if (evseId[evseId.length - 1] === '*') {
      setErrorType(3);
      setError(true);
      error = true;
    } else {
      setError(false);
    }
    const fieldsToCheck = [
      {
        value: country,
        setError: setCountry,
      },
      {
        value: street,
        setError: setStreet,
      },
      {
        value: houseNo,
        setError: setHouseNo,
      },
      {
        value: postalCode,
        setError: setPostalCode,
      },
      {
        value: city,
        setError: setCity,
      },
    ];

    if (isViewInitial) {
      fieldsToCheck.push(
        {
          value: firstname,
          setError: setFirstName,
        },
        {
          value: lastname,
          setError: setLastname,
        },
      );
    }

    fieldsToCheck.forEach((e) => {
      if (!e.value.value || e.value.value === '') {
        e.setError({ ...e.value, error: true });
        error = true;
      } else {
        e.setError({ ...e.value, error: false });
      }
    });

    if (country.value === 'de' && !operator.value) {
      setOperator({ ...operator, error: true });
      error = true;
    }

    if (!error) {
      setShowDialog(false);
    }
    return !error;
  };

  const setDefaultValues = () => {
    setError(false);
    // Prevent invalid old inputs from blocking any changes to the EVSE-ID
    if (
      (isNlDriver || country.value === 'nl') &&
      evseId.indexOf('NL*NUO*E') !== 0
    ) {
      setEvseId('NL*NUO*E');
    } else if (
      (isAtDriver || country.value === 'at') &&
      evseId.indexOf('AT*BPE*E') !== 0
    ) {
      setEvseId('AT*BPE*E');
    }
  };

  const OperatorTooltip = () => {
    return (
      <>
        <TooltipImage>
          <BaseImage
            src={
              i18next.language === 'en'
                ? operatorIdEN
                : i18next.language === 'nl'
                ? operatorIdNL
                : operatorIdDE
            }
            alt="Operator-validation-tooltip"
          />
        </TooltipImage>
      </>
    );
  };

  const Tooltip = () => {
    return (
      <>
        <DialogWrapper
          open={showPrompt}
          onClose={cancelNavigation}
          headline={t('warning')}
        >
          <Grid container>
            <Grid item xs={12}>
              <p>{t('leavePageConfirm')}</p>
              <br />
            </Grid>
            <CenterGrid item xs={6}>
              <Button onClick={confirmNavigation}>{t('leavePage')}</Button>
            </CenterGrid>
            <CenterGrid item xs={6}>
              <Button onClick={cancelNavigation}>{t('stayPage')}</Button>
            </CenterGrid>
          </Grid>
        </DialogWrapper>
        {isDeDriver && (
          <>
            <TooltipHeadline data-cy="de-tooltip">
              <InformationIcon src={infoIcon} alt="info" />
              <div>{t('evseIdTooltipDe')}</div>
            </TooltipHeadline>
            <TooltipImage>
              <DeDriverImage
                src={DeDriverTooltip}
                alt="Evse-Id-Tooltip"
              ></DeDriverImage>
            </TooltipImage>
          </>
        )}
        <TooltipHeadline data-cy="default-tooltip">
          <InformationIcon src={infoIcon} alt="info" />
          <div>
            {isAtDriver
              ? t('serialNumberTooltip')
              : isDeDriver
              ? t('evseIdInfo')
              : t('evseIdTooltip')}
          </div>
        </TooltipHeadline>
        <TooltipImage>
          {isAtDriver ? (
            <>
              <SerialNumberImage
                data-cy="at-tooltip"
                src={SerivalNumberTooltip}
                alt="Evse-Id-Tooltip"
              />
              <SerialNumberImage
                src={SerivalNumberTooltip2}
                alt="Evse-Id-Tooltip"
              />
            </>
          ) : (
            <BaseImage src={EvseIdTooltip} alt="Evse-Id-Tooltip" />
          )}
        </TooltipImage>
      </>
    );
  };

  const goToNextSite = () => {
    if (isReadyForNextSite()) {
      if (isViewInitial) {
        evDriverDispatch({
          type: 'wallbox',
          value: {
            country: country.value,
            firstname: firstname.value,
            lastname: lastname.value,
            evseId,
            address: {
              street: street.value,
              houseNo: houseNo.value,
              additionalInformation: additionalInformation.value,
              postalCode: postalCode.value,
              city: city.value,
            },
          },
        });
      }
      navigate(isViewInitial ? '/tariffselection' : '/editData');
    }
  };

  const onEnter = (e) => {
    if (e.keyCode === 13) {
      if (isViewInitial) {
        goToNextSite();
      }
    }
  };

  const Content = (
    <div>
      <DialogWrapper
        open={showPrompt}
        onClose={cancelNavigation}
        headline={t('warning')}
      >
        <Grid container>
          <Grid item xs={12}>
            <p>{t('leavePageConfirm')}</p>
            <br />
          </Grid>
          <CenterGrid item xs={6}>
            <Button onClick={confirmNavigation}>{t('leavePage')}</Button>
          </CenterGrid>
          <CenterGrid item xs={6}>
            <Button onClick={cancelNavigation}>{t('stayPage')}</Button>
          </CenterGrid>
        </Grid>
      </DialogWrapper>
      <Row>
        <ColSingle>
          <Description error={country.error}>
            {t('wallboxDescription')}
          </Description>
          <Select
            error={country.error}
            disabled={!isViewInitial}
            autoFocus
            data-cy="mandatory"
            big
            displayEmpty
            renderValue={
              country.value !== ''
                ? undefined
                : () => <Placeholder>{t('chooseCountry')}</Placeholder>
            }
            value={country.value}
            onChange={(e) => {
              if (isViewInitial) {
                evDriverDispatch({
                  type: 'wallbox',
                  value: {
                    country: e.target.value,
                    evseId: '',
                    ...evDriverData,
                  },
                });
              }
              setCountry({ value: e.target.value, error: false });
              if (e.target.value === 'at') {
                if (evseId.indexOf('AT*BPE*E') !== 0) {
                  setEvseId('AT*BPE*E');
                }
              } else if (e.target.value === 'nl') {
                if (evseId.indexOf('NL*NUO*E') !== 0) {
                  setEvseId('NL*NUO*E');
                }
              } else if (e.target.value === 'de' && operator.value) {
                if (operator.value === 'BPE (DE*BPE*E)') {
                  setEvseId('DE*BPE*E');
                } else if (operator.value === 'ARP (DE*ARP*E)') {
                  setEvseId('DE*ARP*E');
                } else {
                  setEvseId('DE*VAT*E');
                }
              } else if (
                !evseId.match(
                  /([A-Za-z]{2}\*?[A-Za-z0-9]{3}\*?E[A-Za-z0-9]{1,30})|(\+?[0-9]{1,3}\*[0-9]{3,6}\*[0-9]{1,32})/gm,
                ) ||
                evseId.indexOf('AT*BPE*E') === 0 ||
                evseId.indexOf('NL*NUO*E') === 0
              ) {
                setEvseId('');
              }
            }}
          >
            {Object.values(countries).map((entry) => (
              <StyledMenuItem
                key={entry}
                value={entry}
                data-cy={`${entry}Select`}
              >
                <CountryFlag country={entry} />
                {tLocalization(`${entry}CountryName`)}
              </StyledMenuItem>
            ))}
          </Select>
        </ColSingle>
      </Row>
      {country.value === 'de' && (
        <Row>
          <ColSingle>
            <Description error={operator.error}>
              {t('operatorDescription')}
              <TextField
                noDisplay
                label={' '}
                tooltip={{
                  content: <OperatorTooltip />,
                  maxWidth: '700px',
                  placement: 'right-end',
                  type: isViewInitial ? 'initial_evseId' : 'evseId',
                }}
              ></TextField>
            </Description>

            <Select
              error={operator.error}
              autoFocus
              data-cy="mandatory"
              big
              displayEmpty
              renderValue={
                operator.value !== null
                  ? null
                  : () => <Placeholder>{t('chooseOperator')}</Placeholder>
              }
              value={operator.value}
              onChange={(e) => {
                setOperator({ value: e.target.value, error: false });

                if (e.target.value === 'BPE (DE*BPE*E)') {
                  setEvseId('DE*BPE*E');
                } else if (e.target.value === 'ARP (DE*ARP*E)') {
                  setEvseId('DE*ARP*E');
                } else {
                  setEvseId('DE*VAT*E');
                }
              }}
            >
              {Object.values(operators).map((entry) => (
                <StyledMenuItem
                  key={entry}
                  value={entry}
                  data-cy={`${entry}Select`}
                >
                  {entry}
                </StyledMenuItem>
              ))}
            </Select>
          </ColSingle>
        </Row>
      )}
      <Row>
        <ColSingle>
          <TextField
            newDriver
            disabled={country.value === 'de' && !operator.value}
            error={error}
            onFocus={() => setDefaultValues()}
            placeholder={t('evseIdPlaceholer')}
            data-cy="mandatory"
            value={evseId}
            label={t('headlineEvseIdInput')}
            name="evseid"
            tooltip={{
              content: <Tooltip />,
              maxWidth: '700px',
              placement: 'right-end',
              type: isViewInitial ? 'initial_evseId' : 'evseId',
            }}
            icon={!isViewInitial && missingEvseId && !wallboxUpdateRequested}
            updateIcon={!isViewInitial && wallboxUpdateRequested}
            onKeyDown={onEnter}
            onChange={(e) => {
              if (!e.target.value.match(/^[a-zA-Z0-9*]*$/)) {
                return;
              }
              if (
                country.value === 'de' &&
                (e.target.value.indexOf('DE*BPE*E') === 0 ||
                  e.target.value.indexOf('DE*ARP*E') === 0 ||
                  e.target.value.indexOf('DE*VAT*E') === 0)
              ) {
                setEvseId(e.target.value.trim().toUpperCase());
              } else if (
                (isNlDriver || country.value === 'nl') &&
                e.target.value.indexOf('NL*NUO*E') === 0
              ) {
                setEvseId(e.target.value.trim().toUpperCase());
              } else if (
                (isAtDriver || country.value === 'at') &&
                e.target.value.indexOf('AT*BPE*E') === 0
              ) {
                setEvseId(e.target.value.trim().toUpperCase());
              }
            }}
          />
          {error && errorType === 0 && (
            <AutocorrectContainer>
              <CustomError>{t(`evseIdError${errorType}`)}</CustomError>
              <ButtonContainer>
                <Button
                  data-cy="autocorrectCanelButton"
                  variant="smallSec"
                  onClick={() => setError(false)}
                >
                  {t('cancel')}
                </Button>
                <Button
                  variant="small"
                  data-cy="autocorrectButton"
                  onClick={() => {
                    setEvseId(evseId.slice(0, 8) + evseId.slice(9));
                    setError(false);
                  }}
                >
                  {t('autocorrect')}
                </Button>
              </ButtonContainer>
            </AutocorrectContainer>
          )}
          {error && errorType > 0 && (
            <CustomError data-cy={`error-${errorType}`}>
              {(errorType === 3 || errorType === 2) &&
                operator.value === 'BPE (DE*BPE*E)' &&
                t(`evseIdError${errorType}BPE`)}
              {((errorType !== 3 && errorType !== 2) ||
                operator.value !== 'BPE (DE*BPE*E)') &&
                t(`evseIdError${errorType}`)}
            </CustomError>
          )}
        </ColSingle>
      </Row>

      {isViewInitial && (
        <>
          <Row>
            <InputWrapper isMobile={isMobile}>
              <TextField
                data-cy="mandatory"
                placeholder={t('firstnamePlaceholder')}
                newDriver
                error={firstname.error}
                onFocus={() =>
                  setFirstName({
                    ...firstname,
                    error: false,
                  })
                }
                label={t('firstname')}
                value={firstname.value}
                onChange={(e) =>
                  setFirstName({
                    ...firstname,
                    value: e.target.value,
                  })
                }
              />
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper isMobile={isMobile}>
              <TextField
                data-cy="mandatory"
                placeholder={t('lastnamePlaceholder')}
                newDriver
                error={lastname.error}
                onFocus={() =>
                  setLastname({
                    ...lastname,
                    error: false,
                  })
                }
                label={t('lastname')}
                value={lastname.value}
                onChange={(e) =>
                  setLastname({
                    ...lastname,
                    value: e.target.value,
                  })
                }
              />
            </InputWrapper>
          </Row>
        </>
      )}
      <>
        <Description>
          {t('wallboxAddress')}
          {missingWallboxAddress &&
            !wallboxUpdateRequested &&
            !isViewInitial && <StyledCircleIcon />}
          {wallboxUpdateRequested && !isViewInitial && <StyledUpdateIcon />}
        </Description>
        <Row>
          <Col>
            <TextField
              newDriver
              name="street"
              data-cy="mandatory"
              error={street.error}
              onFocus={() => setStreet({ ...street, error: false })}
              placeholder={`${tHomeCharging('street')}`}
              value={street.value}
              onChange={(e) =>
                setStreet({ value: e.target.value, error: false })
              }
              icon={evDriverData.street === ''}
            />
          </Col>
          <Col>
            <TextField
              newDriver
              name="houseNo"
              data-cy="mandatory"
              error={houseNo.error}
              onFocus={() => setHouseNo({ ...houseNo, error: false })}
              placeholder={`${tHomeCharging('houseNo')}`}
              value={houseNo.value}
              onChange={(e) =>
                setHouseNo({ value: e.target.value, error: false })
              }
            />
          </Col>
        </Row>
        <Row>
          <TextField
            newDriver
            fullWidth
            name="addressExtension"
            placeholder={`${tHomeCharging('addressExtension')}`}
            value={additionalInformation.value}
            onChange={(e) =>
              setAdditionalInformation({
                value: e.target.value,
                error: false,
              })
            }
          />
        </Row>
        <Row>
          <Col>
            <TextField
              newDriver
              name="city"
              error={city.error}
              data-cy="mandatory"
              onFocus={() => setCity({ ...city, error: false })}
              placeholder={`${tHomeCharging('city')}`}
              value={city.value}
              onChange={(e) => setCity({ value: e.target.value, error: false })}
            />
          </Col>
          <Col>
            <TextField
              newDriver
              error={postalCode.error}
              name="postalCode"
              data-cy="mandatory"
              onFocus={() => setPostalCode({ ...postalCode, error: false })}
              placeholder={`${tHomeCharging('postalCode')}`}
              value={postalCode.value}
              onChange={(e) =>
                setPostalCode({ value: e.target.value, error: false })
              }
            />
          </Col>
        </Row>
      </>
      {isViewInitial && (
        <Row>
          <InformationContainer>
            <InformationIcon>
              <img src={infoIcon} alt="success" />
            </InformationIcon>
            <InformationText>{t('welcomeInfo')}</InformationText>
          </InformationContainer>
        </Row>
      )}
    </div>
  );

  if (isViewInitial) {
    return (
      <PageTemplate
        headline={isViewInitial ? t('welcomeHeadline') : t('locationHeadline')}
        description={t('welcomeDescription')}
        currentPage={1}
        cardNumber={evDriverData?.cardNumber}
        next={true}
        onBoardingMode={isViewInitial}
        onNext={goToNextSite}
      >
        {Content}
      </PageTemplate>
    );
  }

  return (
    <EditWrapper
      onCancel={
        !isViewInitial && (!evseIdUnchanged || !wallboxUnchanged)
          ? cancelChanges
          : null
      }
      disabled={!isViewInitial && evseIdUnchanged && wallboxUnchanged}
      data={{
        wallbox: {
          location_of_wallbox: country.value.toUpperCase(),
          evse_id: evseId,
          wallbox_owner: true,
          address: {
            country: evDriverData?.wallbox?.locationOfWallbox,
            street: street.value,
            number: houseNo.value,
            additional_information: additionalInformation.value,
            postcode: postalCode.value,
            city: city.value,
          },
        },
      }}
      beforeSafe={isReadyForNextSite}
    >
      {Content}
    </EditWrapper>
  );
};

Welcome.defaultProps = {
  isViewInitial: false,
};

Welcome.propTypes = {
  isViewInitial: PropTypes.bool,
};

export default Welcome;
