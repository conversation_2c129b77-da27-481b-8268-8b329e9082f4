import ClickAwayListener from '@mui/material/ClickAwayListener';
import Grow from '@mui/material/Grow';
import Paper from '@mui/material/Paper';
import Popper from '@mui/material/Popper';
import MenuItem from '@mui/material/MenuItem';
import MenuList from '@mui/material/MenuList';
import PropTypes from 'prop-types';
import Close from '@material-ui/icons/Close';
import { IconButton } from '@material-ui/core';
import { evDriverContext } from '../../ContextProvider';

import PageTemplate, {
  Description,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { useCallback, useContext, useEffect } from 'react';
import { useState } from 'react';
import { useRef } from 'react';
import { Checkbox, TextField } from '../../Components';
import { ListSubheader } from '@mui/material';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import { Box } from '@mui/system';
import styled from 'styled-components';
import EditWrapper from '../../Components/EditWrapper';
import { isMobile } from 'react-device-detect';
import { Grid } from '@mui/material';
import DialogWrapper from '../../Components/DialogWrapper';
import { useCallbackPrompt } from '../../Components/useCallbackPrompt';
import Button from '../../Components/Button';
import CircleIcon from '@mui/icons-material/Circle';

const PopperStaticText = styled.div`
  padding: 0.5rem;
  width: ${isMobile ? '100%' : '320px'};
  font-size: 14px;
  box-sizing: border-box;
  text-align: left;
  line-height: 28px;
`;

const CenterGrid = styled(Grid)`
  text-align: center;
`;

const StyledCircleIcon = styled(CircleIcon)`
  width: 0.7em !important;
  height: 0.7em !important;
  color: var(--trafineo-rot-100) !important;
`;

const Vehicle = ({ isViewInitial }) => {
  const { t } = useTranslation('evDriver');
  const navigate = useNavigate();

  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);

  const validateInputs = () => {
    let error = false;

    if (
      !vehicle.value.licencePlate ||
      !vehicle.value.licencePlate.match(/^[0-9a-zäöüßA-ZÄÖÜß]/)
    ) {
      setVehicle({ ...vehicle, licencePlateError: true });
      error = true;
    }
    if (!vehicle.value.id) {
      setVehicle({ ...vehicle, error: true });
      error = true;
    }
    if (!error) {
      setShowDialog(false);
    }
    return !error;
  };

  const goToNextSite = () => {
    if (validateInputs()) {
      evDriverDispatch({
        type: 'vehicle',
        value: {
          licencePlate: vehicle.value.licencePlate,
          vehicle: vehicle.value,
        },
      });
      navigate('/summary');
    }
  };
  const [vehicleFilter, setVehicleFilter] = useState('');
  const [vehicle, setVehicle] = useState({
    value: evDriverData?.vehicle || undefined,
    error: false,
    licencePlateError: false,
  });
  const [vehicles, setVehicles] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [noVehiclesFound, setNoVehiclesFound] = useState(false);
  const [otherVehicle, setOtherVehicle] = useState(
    evDriverData?.vehicle?.id === 1 || false,
  );
  const [isSearchFocused, setIsSearchFocused] = useState(false);

  //Menu
  const [open, setOpen] = useState(false);
  const anchorRef = useRef(null);
  const [showDialog, setShowDialog] = useState(false);
  const [showPrompt, confirmNavigation, cancelNavigation] =
    useCallbackPrompt(showDialog);

  useEffect(() => {
    if (evDriverData?.vehicle?.licencePlate) {
      setVehicle({ ...vehicle, value: evDriverData?.vehicle });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [evDriverData]);

  useEffect(() => {
    if (
      isViewInitial ||
      (vehicle.value.licencePlate === evDriverData.vehicle.licencePlate &&
        vehicle.value.id === evDriverData.vehicle.id)
    ) {
      setShowDialog(false);
    } else {
      setShowDialog(true);
    }
  }, [vehicle, evDriverData, isViewInitial]);

  const handleToggle = () => {
    setOpen((prevOpen) => !prevOpen);
  };

  const handleClose = () => {
    setVehicleFilter('');
    setVehicles([]);
  };
  const handleSelect = (selectedVehicle) => {
    setVehicle({
      ...vehicle,
      value: { licencePlate: vehicle.value.licencePlate, ...selectedVehicle },
    });
    setVehicleFilter('');
    setVehicles([]);
  };

  function handleListKeyDown(event) {
    if (event.key === 'Tab') {
      event.preventDefault();
      setOpen(false);
    } else if (event.key === 'Escape') {
      setOpen(false);
    }
  }

  const getVehicles = useCallback(async (searchString) => {
    setIsLoading(true);
    try {
      const rsp = await requester().get('/vehicle_list', {
        headers: {
          search: searchString,
        },
      });
      setIsLoading(false);
      return rsp.data;
    } catch (err) {
      setIsLoading(false);
      return null;
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const rspData = await getVehicles(vehicleFilter);
      if (rspData) {
        const sortedResponse = {};
        rspData
          .sort((a, b) => a.Vehicle_Make.localeCompare(b.Vehicle_Make))
          .sort((a, b) => a.Vehicle_Make.localeCompare(b.Vehicle_Model))
          .forEach((e) => {
            if (sortedResponse[e.Vehicle_Make]) {
              sortedResponse[e.Vehicle_Make].push(e);
            } else {
              sortedResponse[e.Vehicle_Make] = [e];
            }
          });
        setNoVehiclesFound(false);
        setVehicles(sortedResponse);
      } else {
        setNoVehiclesFound(true);
      }
    };
    if (vehicleFilter.length > 1) {
      setNoVehiclesFound(false);
      fetchData();
    } else {
      setVehicles([]);
    }
  }, [vehicleFilter, getVehicles]);

  // return focus to the button when we transitioned from !open -> open
  const prevOpen = useRef(open);
  useEffect(() => {
    if (prevOpen.current === true && open === false) {
      anchorRef.current.focus();
    }

    prevOpen.current = open;
  }, [open]);

  const Content = (
    <div>
      <DialogWrapper
        open={showPrompt}
        onClose={cancelNavigation}
        headline={t('warning')}
      >
        <Grid container>
          <Grid item xs={12}>
            <p>{t('leavePageConfirm')}</p>
            <br />
          </Grid>
          <CenterGrid item xs={6}>
            <Button onClick={confirmNavigation}>{t('leavePage')}</Button>
          </CenterGrid>
          <CenterGrid item xs={6}>
            <Button onClick={cancelNavigation}>{t('stayPage')}</Button>
          </CenterGrid>
        </Grid>
      </DialogWrapper>
      <Row>
        <InputWrapper isMobile={isMobile}>
          <TextField
            data-cy="mandatory"
            placeholder={t('licencePlatePlaceholder')}
            newDriver
            error={vehicle.licencePlateError}
            onFocus={() =>
              setVehicle({
                ...vehicle,
                licencePlateError: false,
              })
            }
            label={t('licencePlate')}
            value={vehicle?.value?.licencePlate}
            onChange={(e) =>
              setVehicle({
                ...vehicle,
                value: {
                  ...vehicle.value,
                  licencePlate: e.target.value.toUpperCase(),
                },
              })
            }
            icon={!isViewInitial && !evDriverData?.vehicle?.licencePlate}
          />
        </InputWrapper>
      </Row>

      <Box w="100%">
        {!isViewInitial &&
          vehicle?.value?.id &&
          vehicle?.value?.id !== 1 &&
          !evDriverData?.vehicle?.id && (
            <Description error={vehicle.error} style={{ marginBottom: '0' }}>
              {t('vehicle')} <StyledCircleIcon />
            </Description>
          )}
        {vehicle?.value?.id &&
          vehicle?.value?.id !== 1 &&
          (evDriverData?.vehicle?.id || isViewInitial) && (
            <Description error={vehicle.error} style={{ marginBottom: '0' }}>
              {t('vehicle')}
            </Description>
          )}
        <InputWrapper isMobile={isMobile} style={{ position: 'relative' }}>
          {vehicle?.value?.Battery_Capacity_Full && vehicle?.value?.id !== 1 ? (
            <MenuItem
              data-cy="vehicle"
              disableRipple
              style={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'flex-start',
                padding: '0.5rem',
                background: '#F7F7F7',
                marginBottom: '0.25rem',
                color: '#414042',
                marginTop: '0.5rem',
              }}
              onMouseEnter={(e) => (e.target.style.opacity = 0.8)}
              onMouseLeave={(e) => (e.target.style.opacity = 1)}
            >
              <div style={{ fontSize: '14px' }}>{`${
                vehicle.value.Vehicle_Make
              } ${vehicle.value.Vehicle_Model} ${
                vehicle.value.Vehicle_Model_Version
                  ? vehicle.value.Vehicle_Model_Version
                  : ''
              }`}</div>
              <div style={{ fontSize: '13px' }}>{`${
                vehicle.value.Drivetrain_Type
              } - ${vehicle.value.Battery_Capacity_Full} kWh (${
                vehicle.value.Availability_Date_From?.split('-')[1]
              } - ${
                vehicle.value.Availability_Date_To
                  ? vehicle.value.Availability_Date_To.split('-')[1]
                  : t('now')
              })`}</div>
              <IconButton
                style={{ position: 'absolute', right: '10px', top: '15px' }}
                size="small"
                data-cy="deleteVehicle"
                onClick={() => {
                  setVehicle({
                    ...vehicle,
                    value: {
                      licencePlate: vehicle.value.licencePlate,
                      Vehicle_Make: null,
                      Vehicle_Model: null,
                      Vehicle_Model_Version: null,
                      Battery_Capacity_Full: null,
                      Drivetrain_Type: null,
                      Availability_Date_From: null,
                      Availability_Date_To: null,
                      id: null,
                    },
                  });
                }}
              >
                <Close fontSize="small" />
              </IconButton>
            </MenuItem>
          ) : (
            <>
              <TextField
                data-cy="mandatory"
                placeholder={t('vehiclePlaceholder')}
                newDriver
                label={t('vehicle')}
                onFocus={() => {
                  setIsSearchFocused(true);
                  setVehicle({
                    ...vehicle,
                    error: false,
                  });
                }}
                onBlur={() => {
                  setIsSearchFocused(false);
                }}
                error={vehicle.error}
                disabled={otherVehicle}
                autocomplete="off"
                value={vehicleFilter}
                onChange={(e) => setVehicleFilter(e.target.value)}
                id="vehicleFilter"
                aria-controls={open ? 'composition-menu' : undefined}
                aria-expanded={open ? 'true' : undefined}
                aria-haspopup="true"
                ref={anchorRef}
                onClick={handleToggle}
                icon={!isViewInitial && !evDriverData?.vehicle?.id}
              />
              {isLoading && (
                <div
                  style={{
                    position: 'absolute',
                    right: '10px',
                    top: '20px',
                  }}
                >
                  <CircularProgress small />
                </div>
              )}
              <Popper
                open={isSearchFocused || vehicleFilter.length > 1}
                anchorEl={anchorRef.current}
                role={undefined}
                placement="bottom-start"
                transition
                disablePortal
                style={{ zIndex: 10, width: isMobile ? '100%' : '320px' }}
              >
                {({ TransitionProps, placement }) => (
                  <Grow
                    {...TransitionProps}
                    style={{
                      transformOrigin:
                        placement === 'bottom-start' ? 'top' : 'bottom',
                    }}
                  >
                    <Paper>
                      <ClickAwayListener onClickAway={handleClose}>
                        {vehicleFilter.length < 2 ? (
                          <PopperStaticText>
                            {t('enter2Letters')}
                          </PopperStaticText>
                        ) : noVehiclesFound ? (
                          <PopperStaticText>
                            {t('noVehiclesFound')}
                          </PopperStaticText>
                        ) : (
                          <MenuList
                            autoFocusItem={open}
                            id="composition-menu"
                            style={{
                              maxHeight: '300px',
                              width: isMobile ? '100%' : '320px',
                              overflowY: 'scroll',
                              padding: 0,
                            }}
                            aria-labelledby="vehicleFilter"
                            onKeyDown={handleListKeyDown}
                          >
                            {Object.entries(vehicles).map((e) => {
                              return (
                                <Box>
                                  <ListSubheader
                                    style={{
                                      textAlign: 'left',
                                      padding: '0.5rem',
                                      color: '#414042',
                                      fontSize: '14px',
                                      lineHeight: '32px',
                                      fontWweight: 600,
                                    }}
                                    disabled
                                  >
                                    {e[0]}
                                  </ListSubheader>
                                  {e[1].map((e) => (
                                    <MenuItem
                                      data-cy="listVehicle"
                                      style={{
                                        display: 'flex',
                                        flexDirection: 'column',
                                        alignItems: 'flex-start',
                                        padding: '0.5rem',
                                        background: '#F7F7F7',
                                        marginBottom: '0.25rem',
                                        color: '#414042',
                                      }}
                                      onMouseEnter={(e) =>
                                        (e.target.style.opacity = 0.8)
                                      }
                                      onMouseLeave={(e) =>
                                        (e.target.style.opacity = 1)
                                      }
                                      onClick={() => handleSelect(e)}
                                    >
                                      <div style={{ fontSize: '14px' }}>{`${
                                        e.Vehicle_Make
                                      } ${e.Vehicle_Model} ${
                                        e.Vehicle_Model_Version
                                          ? e.Vehicle_Model_Version
                                          : ''
                                      }`}</div>
                                      <div style={{ fontSize: '13px' }}>{`${
                                        e.Drivetrain_Type
                                      } - ${e.Battery_Capacity_Full} kWh (${
                                        e.Availability_Date_From?.split('-')[1]
                                      } - ${
                                        e.Availability_Date_To
                                          ? e.Availability_Date_To.split('-')[1]
                                          : t('now')
                                      })`}</div>
                                    </MenuItem>
                                  ))}
                                </Box>
                              );
                            })}
                          </MenuList>
                        )}
                      </ClickAwayListener>
                    </Paper>
                  </Grow>
                )}
              </Popper>
            </>
          )}
        </InputWrapper>
      </Box>
      <Row
        style={{
          display: 'flex',
          alignItems: 'center',
          paddingLeft: '0.25rem',
        }}
      >
        <Checkbox
          autofocus
          data-cy="vehicleNotFound"
          boxSize="small"
          id="otherVehicle"
          checked={otherVehicle}
          onChange={() => {
            if (otherVehicle) {
              setVehicle({
                ...vehicle,
                error: false,
                value: {
                  licencePlate: vehicle.value.licencePlate,
                  Vehicle_Make: null,
                  Vehicle_Model: null,
                  Vehicle_Model_Version: null,
                  Battery_Capacity_Full: null,
                  Drivetrain_Type: null,
                  id: null,
                },
              });
            } else {
              setVehicle({
                value: { licencePlate: vehicle.value.licencePlate, id: 1 },
                error: false,
              });
            }

            setOtherVehicle(!otherVehicle);
          }}
        />
        <p style={{ paddingLeft: '0.5rem' }}>{t('otherVehicle')}</p>
      </Row>
    </div>
  );

  if (isViewInitial) {
    return (
      <PageTemplate
        headline={t('vehicleHeadline')}
        description={t('vehicleDescription')}
        currentPage={5}
        cardNumber={evDriverData?.cardNumber}
        next={true}
        nextText={''}
        onBoardingMode
        onNext={goToNextSite}
        onPrevious={() => navigate('/bankData')}
      >
        {Content}
      </PageTemplate>
    );
  }
  return (
    <EditWrapper
      beforeSafe={validateInputs}
      disabled={
        !isViewInitial &&
        vehicle.value.licencePlate === evDriverData.vehicle.licencePlate &&
        vehicle.value.id === evDriverData.vehicle.id
      }
      data={{
        vehicle_id: vehicle.value?.id,
        licence_plate: vehicle.value?.licencePlate,
      }}
    >
      {Content}
    </EditWrapper>
  );
};

export default Vehicle;

Vehicle.defaultProps = {
  isViewInitial: false,
};

Vehicle.propTypes = {
  isViewInitial: PropTypes.bool,
};
