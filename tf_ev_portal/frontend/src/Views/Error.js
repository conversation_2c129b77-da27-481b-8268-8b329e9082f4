import styled from 'styled-components';
import { useTranslation, Trans } from 'react-i18next';
import { Link } from 'react-router-dom';
import ContentWidth from '../Components/helper/ContentWidth';
import Center from '../Components/helper/Center';

const Error = () => {
  const TextBlock = styled.div`
    text-align: left;
    padding: 20px;
  `;
  const Text = styled.p`
    font-size: 16px;
    a {
      color: var(--trafineo-rot-100);
      text-decoration: none;
    }
  `;

  const { t } = useTranslation('footer');

  return (
    <Center>
      <ContentWidth>
        <TextBlock>
          <h1> {t('errorPageTitle')} </h1>{' '}
          <Text>
            <Trans t={t} i18nKey="errorPageContent">
              An unexpected error occurred. Please try again later or contact
              the <Link to="/contact">support</Link>.
            </Trans>
          </Text>
        </TextBlock>
      </ContentWidth>
    </Center>
  );
};
export default Error;
