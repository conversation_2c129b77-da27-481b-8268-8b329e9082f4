import {
  TariffTypeSelectionContainer,
  TariffTypeSelectionOption,
} from '../Components/evDriver/PageTemplate';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import DriveEtaIcon from '@material-ui/icons/DriveEta';
import GroupIcon from '@material-ui/icons/Group';
import { Background, Blank, Page } from './HomeChargingAustria/Styles';
import { Headline, Headline2, Wrapper } from './DriverOnboarding';
import { isMobile } from 'react-device-detect';

const SignUpType = () => {
  const { t } = useTranslation('onboarding');
  const navigate = useNavigate();

  const TariffTypeSelection = () => {
    return (
      <TariffTypeSelectionContainer>
        <TariffTypeSelectionOption
          onClick={() => {
            navigate('/signUp');
          }}
        >
          <DriveEtaIcon style={{ marginRight: '0.5rem' }} />
          {t(`optionDriver`)}
        </TariffTypeSelectionOption>
        <TariffTypeSelectionOption
          onClick={() => {
            navigate('/onboarding');
          }}
        >
          <GroupIcon style={{ marginRight: '0.5rem' }} />
          {t(`optionFleetmanager`)}
        </TariffTypeSelectionOption>
      </TariffTypeSelectionContainer>
    );
  };
  return (
    <>
      <Background>
        <Blank />
        <Wrapper>
          <>
            <Headline isMobile={isMobile}>{t('signUp')}</Headline>
            <Headline2>{t('signupTypeDesc')}</Headline2>
            <Page>
              <TariffTypeSelection />
            </Page>
          </>
        </Wrapper>
      </Background>
    </>
  );
};

export default SignUpType;
