import { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useTranslation, Trans } from 'react-i18next';
import qs from 'querystring';
import PropTypes from 'prop-types';
import JwtDecode from 'jwt-decode';
import { isMobile } from 'react-device-detect';

import PageWrapper from '../Components/PageWrapper';
import { Error, InputWrapper, Row } from '../Components/evDriver/PageTemplate';
import { TextField, Button } from '../Components';
import Center from '../Components/helper/Center';
import Select from '../Components/Select';

import logger from '../utils/logger';
import { idpRequest } from '../utils/requester';
import { isInDemoMode, getBranding } from '../utils/helper';
import Cookies from 'universal-cookie';

const ButtonContainer = styled.div`
  width: 50%;
`;

const Branding = styled.div`
  text-align: center;
  padding: 0.5rem;
`;

const PWForgot = styled(Link)`
  color: var(--trafineo-rot-100);
  margin-top: 1rem;
  font-weight: bold;
  font-size: 12px;
  text-decoration: underline;
`;

const AbsoluteText = styled.div`
  position: absolute;
  background: white;
  padding: 0 1em;
  font-size: 14px;
`;

export function getLoginRequestBody(username, password) {
  const paramsForEnv = {};
  if (process.env.NODE_ENV !== 'production') {
    paramsForEnv.client_id = process.env.REACT_APP_KEYCLOAK_ID;
    paramsForEnv.client_secret = process.env.REACT_APP_KEYCLOAK_SECRET;
  }
  return {
    grant_type: 'password',
    username,
    password,
    ...paramsForEnv,
  };
}

export const refreshToken = (refresh_token) => {
  const paramsForEnv = {};
  if (process.env.NODE_ENV !== 'production') {
    paramsForEnv.client_id = process.env.REACT_APP_KEYCLOAK_ID;
    paramsForEnv.client_secret = process.env.REACT_APP_KEYCLOAK_SECRET;
  }
  return idpRequest().post(
    process.env.REACT_APP_KEYCLOAK_TOKEN_URI,
    qs.stringify({
      grant_type: 'refresh_token',
      refresh_token,
      ...paramsForEnv,
    }),
    {
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    },
  );
};

const Login = ({ isLoggedIn, setIsLoggedIn, setUser }) => {
  const { t } = useTranslation('actions');
  const tOnboarding = useTranslation('onboarding').t;
  const navigate = useNavigate();
  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const { error } = qs.parse(rawQueryString);
  const [userName, setUsername] = useState(
    isInDemoMode() ? 'Fleetmanager_Direct' : '',
  );
  const [password, setPassword] = useState(isInDemoMode() ? ' ' : '');
  const branding = window.localStorage.getItem('branding');

  const [loginError, setLoginError] = useState(0);

  const isCredentialsError = loginError === 401;
  const isGeneralError = error || (!!loginError && loginError !== 401);
  const [isNotInvitedError, setIsNotInvitedError] = useState(false);
  const [wrongPortalError, setWrongPortalError] = useState(false);
  const [redirectUrl, setRedirectUrl] = useState('');

  const { REACT_APP_KEYCLOAK_APP_USER_ROLE } = process.env;

  const cookies = new Cookies();

  useEffect(() => {
    if (isLoggedIn) {
      navigate('/');
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isLoggedIn]);

  const isReadyForLogin = () => {
    // credentials should be valid
    return userName && password;
  };

  const resetErrors = () => {
    setWrongPortalError(false);
    setIsNotInvitedError(false);
    setLoginError(0);
  };

  const loginFunction = async () => {
    if (isReadyForLogin()) {
      const body = getLoginRequestBody(userName, password);
      resetErrors();
      try {
        const { data: access_token } = await idpRequest().post(
          process.env.REACT_APP_KEYCLOAK_TOKEN_URI,
          qs.stringify(body),
          {
            skipAuthRefresh: true,
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );

        const decodedKey = JwtDecode(access_token.access_token);
        const { role, provider } = decodedKey;
        if (role.toLowerCase() === REACT_APP_KEYCLOAK_APP_USER_ROLE) {
          setIsNotInvitedError(true);
        } else if (provider && getBranding() !== provider.toLowerCase()) {
          setWrongPortalError(true);
          const url = [];
          if (provider.toLowerCase() !== 'trafineo') {
            url.push(`${provider.toLowerCase()}-`);
          }
          if (
            window.location.host.indexOf('reimbursement-dev.trafineo.com') !==
            -1
          ) {
            url.push('reimbursement-dev.trafineo.com');
          } else if (
            window.location.host.indexOf('reimbursement-qa.trafineo.com') !== -1
          ) {
            url.push('reimbursement-qa.trafineo.com');
          } else if (
            window.location.host.indexOf(
              'reimbursement-staging.trafineo.com',
            ) !== -1
          ) {
            url.push('reimbursement-staging.trafineo.com');
          } else {
            url.push('reimbursement.trafineo.com');
          }
          setRedirectUrl(`https://${url.join('')}`);
        } else {
          setIsNotInvitedError(false);
          setUser(access_token);
          setIsLoggedIn(true);
        }
      } catch (e) {
        console.error('lgin.js', e);
        setLoginError(e.response?.status);
        logger().error(e.message);
      }
    }
  };

  const loginOnEnter = (e) => {
    // login on Enter press
    if (e.keyCode === 13 && cookies.get('reimbursementCookie') !== undefined) {
      loginFunction();
    }
  };

  return (
    <PageWrapper isMobile={isMobile} card title={t('Login')}>
      <form autoComplete="on">
        {isInDemoMode() ? (
          <>
            <Branding>
              Branding:{'  '}
              <Select
                value={branding}
                onChange={(e) => {
                  window.localStorage.setItem('branding', e.target.value);
                  window.location.reload();
                }}
              >
                <option value="aral">Aral</option>
                <option value="bp">Bp</option>
                <option value="trafineo">Trafineo</option>
              </Select>
            </Branding>
            <Branding>
              User:{'  '}
              <Select
                value={userName}
                onChange={(e) => {
                  setUsername(e.target.value);
                }}
              >
                <option value="Fleetmanager_Direct">Fleetmanager_Direct</option>
                <option value="Fleetmanager_Indirect">
                  Fleetmanager_Indirect
                </option>
                <option value="SalesAdmin">SalesAdmin</option>
                <option value="Support">Support</option>
                <option value="Driver_Welcome_Twotariff">Driver_Welcome</option>
                <option value="Driver_Welcome_At">Driver_Welcome_At</option>
                <option
                  value={
                    branding === 'bp'
                      ? 'Driver_Modify_Bp'
                      : branding === 'aral'
                      ? 'Driver_Modify_Aral'
                      : 'Driver_Modify'
                  }
                >
                  Driver_Modify
                </option>
              </Select>
            </Branding>
          </>
        ) : (
          <>
            <Row>
              <InputWrapper fullWidth>
                <TextField
                  data-cy="usernameInput"
                  autoFocus
                  autoComplete="username"
                  label={t('username')}
                  placeholder={t('userNamePlaceholder')}
                  newDriver
                  error={isCredentialsError}
                  name="username"
                  onChange={(e) => {
                    setUsername(e.target.value);
                  }}
                  onKeyDown={loginOnEnter}
                />
              </InputWrapper>
            </Row>
            <Row style={{ marginBottom: '1rem' }}>
              <InputWrapper fullWidth>
                <TextField
                  data-cy="passwordInput"
                  type="password"
                  autoComplete="current-password"
                  label={t('password')}
                  placeholder={t('password')}
                  name="password"
                  newDriver
                  error={isCredentialsError}
                  onKeyDown={loginOnEnter}
                  onChange={(e) => {
                    setPassword(e.target.value);
                  }}
                />
              </InputWrapper>
            </Row>
            {(isCredentialsError ||
              isGeneralError ||
              isNotInvitedError ||
              wrongPortalError) && (
              <Row style={{ justifyContent: 'center', marginBottom: '1rem' }}>
                {isCredentialsError && (
                  <Error data-cy="credentialsErrorMessage">
                    {t('credentialsIncorrect')}
                  </Error>
                )}
                {isGeneralError && (
                  <Error data-cy="defaultErrorMessage">
                    {t('generalLoginError')}
                  </Error>
                )}
                {isNotInvitedError && (
                  <Error data-cy="isNotInvitedErrorMessage">
                    {t('isNotInvitedError')}
                  </Error>
                )}
                {wrongPortalError && (
                  <p data-cy="wrongPortalErrorMessage">
                    <Trans t={t} i18nKey="wrongPortalError">
                      Click
                      <a href={redirectUrl}>here</a>
                      to be forwarded
                    </Trans>
                  </p>
                )}
              </Row>
            )}
          </>
        )}

        <Center>
          <PWForgot to="/forgotPassword">{t('forgotPassword')}</PWForgot>
        </Center>

        <Center style={{ marginTop: '1rem' }}>
          <ButtonContainer>
            <Button
              data-cy="enterButton"
              disabled={!isReadyForLogin()}
              variant={!isReadyForLogin() ? 'disabled' : 'primary'}
              onClick={loginFunction}
            >
              {t('login')}
            </Button>
          </ButtonContainer>
        </Center>
        <Center style={{ marginTop: '1rem' }}>
          <hr style={{ width: '50%' }}></hr>
          <AbsoluteText>{tOnboarding('or')}</AbsoluteText>
        </Center>
        <Center style={{ marginTop: '1rem' }}>
          <ButtonContainer>
            <Button
              data-cy="signUpButton"
              onClick={() => navigate('/signUp-type')}
            >
              {t('signUp')}
            </Button>
          </ButtonContainer>
        </Center>
      </form>
    </PageWrapper>
  );
};

Login.defaultProps = {
  isLoggedIn: false,
  setIsLoggedIn: () => {},
  setUser: () => {},
};

Login.propTypes = {
  isLoggedIn: PropTypes.bool,
  setIsLoggedIn: PropTypes.func,
  setUser: PropTypes.func,
};

export default Login;
