import { useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import styled from 'styled-components';
import TrafineoLogo from '../../static/img/<EMAIL>';
import BpLogo from '../../static/img/BPP_Rlbg.svg';
import <PERSON>l<PERSON>ogo from '../../static/img/Aral_Logo_M40_4C_transp.png';
import { ImageWrapper } from '../../Components/dashboard/DashboardTemplates';

import PageWrapper from '../../Components/PageWrapper';
import CardOverview from '../../Components/dashboard/CardOverview';
import { Box } from '@mui/system';
import { Tab, Tabs } from '@mui/material';
import UserOverview from '../../Components/dashboard/UserOverview';
import SummaryOverview from '../../Components/dashboard/SummaryOverview';
import TagManager from 'react-gtm-module';

const StyledTab = styled(Tab)`
  text-transform: none !important;
  font-size: 16px !important;
  font-weight: bold !important;
  padding: 0 2.5rem !important;
  &.Mui-selected {
    color: black !important;
  }
`;

const Dashboard = () => {
  const { t } = useTranslation('directCustomer');
  const { id } = useParams();
  const superUser = id;
  const [user, setUser] = useState({
    idp_id: null,
    provider: null,
    e_mail: null,
    function: null,
    principle: null,
  });
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();

  useEffect(() => {
    if (!searchParams.get('tab')) {
      if (!superUser) {
        navigate(`${window.location.pathname}?tab=summary`, { replace: true });
      } else {
        navigate(`${window.location.pathname}?tab=drivers`, { replace: true });
      }
    }
  }, [searchParams, setSearchParams, navigate, superUser]);

  const handleChange = (event, newValue) => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'page_view',
        page: {
          name: window.location.pathname,
          type: window.location.href,
        },
      },
    });
    setSearchParams({ tab: newValue });
  };
  function a11yProps(index) {
    return {
      id: `dashboard-tab-${index}`,
      'aria-controls': `dashboard-tab-${index}`,
    };
  }

  return (
    <PageWrapper
      backButton={superUser}
      title={superUser ? user?.e_mail : t('dashboard-header')}
      minWidth="1230px"
      icon={
        superUser &&
        user.provider && (
          <ImageWrapper>
            <img
              src={
                user.provider?.toLowerCase() === 'bp'
                  ? BpLogo
                  : user.provider?.toLowerCase() === 'aral'
                  ? AralLogo
                  : TrafineoLogo
              }
              alt="logo"
            />
          </ImageWrapper>
        )
      }
    >
      <Box mb="2.5rem" sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs
          value={searchParams.get('tab')}
          onChange={handleChange}
          aria-label="dashboard-tabs"
        >
          {!superUser && (
            <StyledTab
              disableRipple
              value="summary"
              label={t('summary')}
              {...a11yProps(0)}
            />
          )}
          <StyledTab
            disableRipple
            value="drivers"
            label={t('drivers')}
            {...a11yProps(1)}
          />
          <StyledTab
            disableRipple
            value="cards"
            label={t('cards')}
            {...a11yProps(2)}
          />
        </Tabs>
      </Box>
      {searchParams.get('tab') === 'cards' ? (
        <CardOverview setDashboardUser={setUser} />
      ) : searchParams.get('tab') === 'drivers' ? (
        <UserOverview
          setDashboardUser={setUser}
          onlyPending={searchParams.get('pending')}
        />
      ) : !superUser ? (
        <SummaryOverview />
      ) : (
        <UserOverview
          setDashboardUser={setUser}
          onlyPending={searchParams.get('pending')}
        />
      )}
    </PageWrapper>
  );
};

export default Dashboard;
