import { useState, useEffect, useContext } from 'react';
import { Grid } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import jwtDecode from 'jwt-decode';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import Center from '../../Components/helper/Center';
import { Button, Error, LegacyTariffs } from '../../Components';
import { evDriverContext, userContext } from '../../ContextProvider';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import ResponsibilityCheck from '../../Components/ResponsibilityCheck';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  InputWrapper,
  Row,
  TextArea,
} from '../../Components/evDriver/PageTemplate';
import styled from 'styled-components';

const Counter = styled.p`
  font-style: italic;
  text-align: initial;
  color: ${(props) => (props.full ? 'var(--error-color)' : 'default')};
`;

const useStyles = makeStyles({
  container: {
    width: '100%',
    maxWidth: 812,
  },
  main: {
    maxWidth: 600,
    margin: '0 auto',
  },
  viewHeader: {
    textAlign: 'left',
  },
  errorText: {
    color: 'var(--trafineo-rot-100)',
    paddingLeft: '1rem',
  },
  row: {
    display: 'flex',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bold: {
    fontWeight: 'bold',
  },
  button: {
    width: '220px',
  },
  inputWrapper: {
    padding: 4,
  },
  grid: {
    boxSizing: 'border-box',
    padding: '0.5rem',
  },
  filledBox: {
    display: 'flex',
    flexDirection: 'column',
    border: 'solid 1px var(--trafineo-grau-70)',
    backgroundColor: 'var(--trafineo-grau-20)',
  },
  filledBoxRow: {
    textAlign: 'left',
    justifyContent: 'space-between',
    display: 'flex',
    alignItems: 'center',
    padding: '1rem',
  },
  filledBoxRowOverview: {
    textAlign: 'left',
    display: 'flex',
    alignItems: 'center',
    padding: '1rem',
    '& p': {
      marginLeft: '1rem',
    },
  },
  borderBox: {
    border: 'solid 1px var(--trafineo-grau-70)',
    padding: '0.5rem',
  },
  borderBoxRow: {
    display: 'flex',
    padding: '0.5rem',
    '& p': {
      fontSize: '14px',
      fontWeight: 400,
      margin: 0,
    },
  },
  borderBoxRowSmall: {
    padding: '0.25rem 0.25rem 0.25rem 0',
    height: '16px',
  },
  modified: {
    border: 'solid 2px var(--trafineo-rot-100)',
  },
  buttonContainer: {
    padding: '1rem 0.5rem',
    marginTop: '1.5rem',
  },
  imgContainer: {
    paddingRight: '0.5rem',
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center',
    cursor: 'pointer',
  },
  fullWidth: {
    width: '100%',
    margin: 0,
  },
  margin: { margin: '0 1rem', minWidth: '74px' },
  progressWrapper: {
    display: 'flex',
    justifyContent: 'center',
    width: '100%',
  },
  tooltipWrapper: {
    marginTop: '-3px',
  },
});

const DataApproval = ({ open, onClose, driverMail, idpId }) => {
  const classes = useStyles();
  const { t } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;
  const tDirectCustomer = useTranslation('directCustomer').t;
  const { evDriverData, evDriverDispatch } = useContext(evDriverContext);
  const { user } = useContext(userContext);

  const pendingTariff = evDriverData?.pending_tariff;

  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);
  const [comment, setComment] = useState('');

  const [page, setPage] = useState(1);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingApprovalData, setIsLoadingApprovalData] = useState(false);
  const [responsibility, setResponsibility] = useState(false);

  const showError = (message) => {
    setIsErrorVisible(true);
    setErrorMessage(message);
  };

  useEffect(() => {
    if (page === 2 && comment?.length === 255) {
      showError(t('maxLengthReached'));
    } else {
      setIsErrorVisible(false);
    }
  }, [comment, t, page]);

  useEffect(() => {
    const getDriverData = async () => {
      setIsLoadingApprovalData(true);
      try {
        const { data } = await requester().get(
          '/Ev_Driver_Data_Approval/Get_Driver_Data',
          {
            params: { ev_driver_idp_id: idpId },
            headers: {
              'Content-Type': 'application/x-www-form-urlencoded',
            },
          },
        );
        evDriverDispatch({ type: 'set', value: data });
        setIsLoadingApprovalData(false);
      } catch (error) {
        console.error(error);
        showError(tActions('loadingApprovalDataError'));
        setIsLoadingApprovalData(false);
      }
    };
    getDriverData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idpId]);

  const handleSuccess = () => {
    setIsErrorVisible(false);
    setIsStatusDialogVisible(true);
  };

  const sendData = async (reject) => {
    const approvalData = {
      ev_driver_idp_id: idpId,
      approval_status: reject ? 0 : 1,
    };
    if (reject) {
      Object.assign(approvalData, {
        reject_reason: comment,
      });
    }
    try {
      setIsLoading(true);
      await requester()({
        method: 'post',
        url: '/Ev_Driver_Data_Approval/Post_Driver_Data_Approval',
        data: approvalData,
      });
      setIsLoading(false);
      handleSuccess();
    } catch (error) {
      showError(
        tActions(
          error.response && error.response.status === 406
            ? 'infectedFileError'
            : 'generalRequestError',
        ),
      );
      setIsLoading(false);
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={isStatusDialogVisible}
      successMessage={
        page === 1
          ? `${t('successTextDriverApproval', {
              driverMail,
              managerMail: jwtDecode(user.access_token).preferred_username,
            })}`
          : `${tDirectCustomer('successTextRejectData')} ${driverMail}`
      }
      headline={page === 1 ? t('dataApprovalHeadline') : t('rejectData')}
      description={page === 1 ? '' : t('rejectHeadline2')}
    >
      {isLoadingApprovalData ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : page === 1 ? (
        <>
          <Grid className={classes.main} container>
            <Grid item className={classes.grid} xs={12}>
              <LegacyTariffs
                pendingTariff={pendingTariff}
                comment={pendingTariff?.comment}
                idpId={idpId}
                files={pendingTariff?.contract_files}
                hideIcon
              />
            </Grid>
            <Grid item className={classes.grid} xs={12}>
              <ResponsibilityCheck
                responsibility={responsibility}
                setResponsibility={setResponsibility}
              />
            </Grid>
            <Grid item className={classes.grid} xs={12}>
              <Error visible={isErrorVisible} text={errorMessage} />
            </Grid>
          </Grid>
          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button
                data-cy="rejectData"
                style={{ marginRight: 'auto' }}
                disabled={!responsibility}
                onClick={() => {
                  setPage(2);
                  showError(false);
                }}
              >
                {t('rejectData')}
              </Button>
              <Button
                data-cy="approveData"
                onClick={() => {
                  sendData(false);
                }}
                disabled={!responsibility}
                variant="primary"
              >
                {t('approveData')}
              </Button>
            </ButtonWrapper>
          )}
        </>
      ) : (
        <>
          <Row>
            <InputWrapper fullWidth>
              <Description>{t('Comments')}</Description>
              <TextArea
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder={t('commentPlaceholder')}
                maxLength={255}
                rows={5}
              ></TextArea>
              <Counter full={comment.length === 255}>
                {255 - comment.length}/255
              </Counter>
            </InputWrapper>
          </Row>

          <Error visible={isErrorVisible} text={errorMessage} />

          {isLoading ? (
            <Center>
              <CircularProgress />
            </Center>
          ) : (
            <ButtonWrapper>
              <Button
                style={{ marginRight: 'auto' }}
                variant="secondary"
                onClick={() => {
                  setPage(1);
                  showError(false);
                }}
              >
                {tActions('back')}
              </Button>
              <Button
                disabled={!comment?.trim()}
                onClick={() => {
                  sendData(true);
                }}
                variant="primary"
              >
                {t('rejectData')}
              </Button>
            </ButtonWrapper>
          )}
        </>
      )}
    </DialogWrapper>
  );
};

DataApproval.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  driverMail: PropTypes.string,
  idpId: PropTypes.string,
};

export default DataApproval;
