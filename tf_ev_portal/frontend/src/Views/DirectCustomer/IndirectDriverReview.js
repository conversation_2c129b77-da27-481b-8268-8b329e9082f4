import { useTranslation, Trans } from 'react-i18next';
import { useEffect, useState } from 'react';

import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  InputWrapper,
  Row,
  TextArea,
} from '../../Components/evDriver/PageTemplate';

import styled from 'styled-components';
import { Button, Checkbox, Error } from '../../Components';
import requester, { serviceRequest } from '../../utils/requester';
import { LoadingCircleContainer } from '../../Components/dashboard/DashboardTemplates';
import { CircularProgress } from '@material-ui/core';
import Center from '../../Components/helper/Center';

const BasicInfo = styled.div`
  width: 50%;
  font-weight: ${(props) => (props.isHeadline ? 'bold' : 'normal')};
  overflow-wrap: anywhere;
  padding-right: 2px;
`;

const StyledRow = styled(Row)`
  margin-bottom: 15px;
`;

const Spacer = styled.div`
  width: 100%;
  height: 20px;
`;

const Divider = styled.hr`
  border-top: 1px solid var(--trafineo-grau-50);
`;

const ButtonRow = styled(Row)`
  margin-bottom: 0;
  justify-content: end;
`;

const Counter = styled.p`
  font-style: italic;
  text-align: initial;
  color: ${(props) => (props.full ? 'var(--error-color)' : 'default')};
`;

const IndirectDriverReview = ({ open, onClose, rowData, isCard }) => {
  const { t } = useTranslation('evDriver');
  const { t: tActions } = useTranslation('actions');
  const { t: tDirect } = useTranslation('directCustomer');
  const { t: tSuperuser } = useTranslation('superuser');
  const [showSuccess, setShowSuccess] = useState(false);
  const [confirmation, setConfirmation] = useState(false);
  const [notedDataPrivacy, setNotedDataPrivacy] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [showComment, setShowComment] = useState(false);
  const [comment, setComment] = useState('');
  const [approval, setApproval] = useState({
    data: { token_visual_number: null, expiry_date: null },
  });
  const [allLoading, setAllLoading] = useState(false);

  useEffect(() => {
    setAllLoading(true);

    const getApprovalData = async () => {
      let approvalData = await serviceRequest().get(
        `/approvals/v1/approval/`,
        {},
      );

      approvalData.data.data.forEach((approval) => {
        if (
          approval.approvalstatus === 'pending' &&
          approval.user.user_idp_id === rowData.ev_driver_idp_id
        ) {
          setApproval(approval);
        }
      });
      setAllLoading(false);
    };

    getApprovalData();
  }, [isCard, rowData.ev_driver_idp_id]);

  const ConfirmationRow = styled.div`
    display: flex;
    align-items: center;
    & p {
      padding-left: 0.5rem;
    }
  `;

  const acceptUser = async () => {
    setLoading(true);
    try {
      await requester().patch(
        `/driver_management/${rowData.ev_driver_idp_id}`,
        {
          approval_status: true,
          comment: null,
        },
      );
      setLoading(false);
      setIsErrorVisible(false);
      setShowSuccess(true);
    } catch (e) {
      console.error('Failed to accept request:' + e);
      setLoading(false);
      setIsErrorVisible(true);
    }
  };

  const rejectUser = async () => {
    try {
      setLoading(true);
      await requester().patch(
        `/driver_management/${rowData.ev_driver_idp_id}`,
        {
          approval_status: false,
          comment: comment,
        },
      );
      setLoading(false);
      setIsErrorVisible(false);
      setShowSuccess('error');
    } catch (e) {
      console.error('Failed to reject request:' + e);
      setLoading(false);
      setIsErrorVisible(true);
    }
  };

  const rejectCard = async () => {
    try {
      setLoading(true);
      await serviceRequest().patch(`/approvals/v1/approval/${approval.id}`, {
        approvalstatus: 'Rejected',
        comment: comment,
      });
      setLoading(false);
      setIsErrorVisible(false);
      setShowSuccess('error');
    } catch (e) {
      console.error('Failed to reject request:' + e);
      setLoading(false);
      setIsErrorVisible(true);
    }
  };

  const acceptCard = async () => {
    try {
      setLoading(true);
      await serviceRequest().patch(`/approvals/v1/approval/${approval.id}`, {
        approvalstatus: 'Approved',
      });

      setLoading(false);
      setIsErrorVisible(false);
      setShowSuccess(true);
    } catch (e) {
      console.error('Failed to accept request:' + e);
      setLoading(false);
      setIsErrorVisible(true);
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSuccess}
      successMessage={
        showSuccess === 'error'
          ? tDirect('successTextRejectData') +
            ' ' +
            rowData.email_address_ev_driver
          : isCard
          ? t('successApproval')
          : t('successText') + ' ' + rowData.email_address_ev_driver
      }
      headline={showComment ? t('rejectData') : ''}
      description={showComment ? t('rejectHeadline2') : ''}
    >
      {allLoading && (
        <LoadingCircleContainer>
          <CircularProgress />
        </LoadingCircleContainer>
      )}
      {!allLoading && (
        <>
          {!showComment ? (
            <>
              <StyledRow>
                <BasicInfo isHeadline>{t('basicInfo').toUpperCase()}</BasicInfo>
              </StyledRow>
              <StyledRow>
                <BasicInfo isHeadline>{t('firstname')}</BasicInfo>
                <BasicInfo isHeadline>{t('lastname')}</BasicInfo>
              </StyledRow>
              <StyledRow>
                <BasicInfo>{rowData.firstname}</BasicInfo>
                <BasicInfo>{rowData.lastname}</BasicInfo>
              </StyledRow>
              <Spacer />
              <StyledRow>
                <BasicInfo isHeadline>{t('email')}</BasicInfo>
                <BasicInfo isHeadline>{t('licencePlate')}</BasicInfo>
              </StyledRow>
              <StyledRow>
                <BasicInfo>{rowData.email_address_ev_driver}</BasicInfo>
                <BasicInfo>{rowData.vehicle.licence_plate}</BasicInfo>
              </StyledRow>
              <Divider />
              <Spacer />

              <StyledRow>
                <BasicInfo isHeadline>{t('mycards').toUpperCase()}</BasicInfo>
              </StyledRow>
              <StyledRow>
                <BasicInfo isHeadline>{t('cardNumber')}</BasicInfo>
                <BasicInfo isHeadline>{t('expiryDate')}</BasicInfo>
              </StyledRow>
              <StyledRow>
                <BasicInfo>
                  {isCard
                    ? approval.data.token_visual_number
                    : rowData.tokens[0].token_visual_number}
                </BasicInfo>
                <BasicInfo>
                  {isCard
                    ? approval.data.expiry_date?.split('-')[1] +
                      '/' +
                      approval.data.expiry_date?.split('-')[0].slice(-2)
                    : rowData.tokens[0].expiry_date.split('-')[1] +
                      '/' +
                      rowData.tokens[0].expiry_date.split('-')[0].slice(-2)}
                </BasicInfo>
              </StyledRow>
              <Divider />
              <Spacer />

              {!isCard && (
                <>
                  <StyledRow>
                    <BasicInfo isHeadline>
                      {tSuperuser('feetManager').toUpperCase()}
                    </BasicInfo>
                  </StyledRow>
                  <StyledRow>
                    <BasicInfo isHeadline>{t('fleetEmail')}</BasicInfo>
                  </StyledRow>
                  <StyledRow>
                    <BasicInfo>
                      {/* In case of app_user_only status [0] is without info */}
                      {rowData.driver_approval_status[0].actor?.email !== null
                        ? rowData.driver_approval_status[0].actor?.email
                        : rowData.driver_approval_status[1].actor?.email}
                    </BasicInfo>
                  </StyledRow>
                  <Divider />
                </>
              )}

              <Spacer />

              <ConfirmationRow>
                <Checkbox
                  id="confirmationCheckBox"
                  checked={confirmation}
                  onChange={() => setConfirmation(!confirmation)}
                />
                <p>{tActions('acceptIndirectDriverCheckbox')}</p>
              </ConfirmationRow>

              <ConfirmationRow>
                <Checkbox
                  id="notedDataPrivacyCheckBox"
                  checked={notedDataPrivacy}
                  onChange={() => setNotedDataPrivacy(!notedDataPrivacy)}
                />
                <p>
                  <Trans t={t} i18nKey="notedDataPrivacy">
                    I have noted the
                    <a href="/data-privacy-statement">Private Policy</a>
                  </Trans>
                </p>
              </ConfirmationRow>
              <Divider />
              <Spacer />

              <Row>
                <Error
                  visible={isErrorVisible}
                  text={tActions('generalRequestError')}
                />
              </Row>

              {loading && (
                <LoadingCircleContainer>
                  <CircularProgress />
                </LoadingCircleContainer>
              )}
              {!loading && (
                <ButtonRow>
                  <Button
                    variant="secondary"
                    disabled={!confirmation || !notedDataPrivacy}
                    onClick={(e) => setShowComment(true)}
                  >
                    {tSuperuser('rejectAction')}
                  </Button>
                  <Button
                    variant="primary"
                    disabled={!confirmation || !notedDataPrivacy}
                    onClick={isCard ? acceptCard : acceptUser}
                  >
                    {tSuperuser('approveAction')}
                  </Button>
                </ButtonRow>
              )}
            </>
          ) : (
            <>
              <Row>
                <InputWrapper fullWidth>
                  <Description>{t('Comments')}</Description>
                  <TextArea
                    value={comment}
                    onChange={(e) => setComment(e.target.value)}
                    placeholder={t('commentPlaceholder')}
                    maxLength={255}
                    rows={5}
                  ></TextArea>
                  <Counter full={comment.length === 255}>
                    {255 - comment.length}/255
                  </Counter>
                </InputWrapper>
              </Row>

              <Error
                visible={isErrorVisible}
                text={tActions('generalRequestError')}
              />

              {loading ? (
                <Center>
                  <CircularProgress />
                </Center>
              ) : (
                <ButtonWrapper>
                  <Button
                    style={{ marginRight: 'auto' }}
                    variant="secondary"
                    onClick={() => {
                      setShowComment(false);
                      setIsErrorVisible(false);
                    }}
                  >
                    {tActions('back')}
                  </Button>
                  <Button
                    disabled={!comment?.trim()}
                    onClick={() => {
                      isCard ? rejectCard(true) : rejectUser(true);
                    }}
                    variant="primary"
                  >
                    {t('rejectData')}
                  </Button>
                </ButtonWrapper>
              )}
            </>
          )}
        </>
      )}
    </DialogWrapper>
  );
};

export default IndirectDriverReview;
