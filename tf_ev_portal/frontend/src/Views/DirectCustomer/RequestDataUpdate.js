import { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Button, Checkbox } from '../../Components';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import Center from '../../Components/helper/Center';
import TextArea from '../../Components/TextArea';

const CheckBoxText = styled.div`
  margin: 0 0.75rem;
  align-items: center;
  display: inline-block;
  font-size: 15px;
`;

const Counter = styled.p`
  font-style: italic;
  text-align: initial;
  color: ${(props) => (props.full ? 'var(--error-color)' : 'default')};
`;

const CheckboxWrapper = styled.div`
  width: 50%;
  height: 30px;
  align-items: center;
  display: flex;
`;

const RequestDataUpdate = ({
  open,
  onClose,
  driverMail,
  idpId,
  onlyBank = false,
  isSpecialRequest = false,
}) => {
  const { t } = useTranslation('evDriver');
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [showSucess, setShowSuccess] = useState(false);
  const [comment, setComment] = useState('');
  const [wallbox, setWallbox] = useState(false);
  const [bank, setBank] = useState(onlyBank);
  const [tariff, setTariff] = useState(false);
  const [other, setOther] = useState(false);
  const tdirectCustomer = useTranslation('directCustomer').t;
  const tActions = useTranslation('actions').t;
  const [errorMessage, setErrorMessage] = useState(null);

  const showError = (message) => {
    setIsErrorVisible(true);
    setErrorMessage(message);
  };

  useEffect(() => {
    if (comment.length === 255) {
      showError(t('maxLengthReached'));
    } else {
      setIsErrorVisible(false);
    }
  }, [comment, t]);

  const sendData = async () => {
    setErrorMessage(null);
    const data = {
      ev_driver_idp_id: idpId,
      comment,
      updateData: [],
    };
    if (wallbox) {
      data.updateData.push('wallbox');
    }
    if (bank) {
      data.updateData.push('banking_data');
    }
    if (tariff) {
      data.updateData.push('electricity_contract');
    }
    if (other) {
      data.updateData.push('other');
    }
    try {
      setIsLoading(true);
      await requester().post(
        '/Ev_Driver_Data_Approval/Post_Update_Data_Request',
        data,
      );
      setIsErrorVisible(false);
      setShowSuccess(true);
      setIsLoading(false);
    } catch (err) {
      console.error(err);
      if (err.response.status === 409) {
        setErrorMessage(tActions('updatedByUser'));
      }
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSucess}
      successMessage={`${tdirectCustomer(
        'successTextInviteChangeData',
      )} ${driverMail}`}
      headline={t('changeHeadline')}
      description={t('requestHeadline2')}
    >
      <Row>
        <CheckboxWrapper>
          <Checkbox
            id="wallboxCheckbox"
            checked={wallbox}
            style={{ display: 'inline-block' }}
            onChange={() => setWallbox(!wallbox)}
            disabled={onlyBank}
          />
          <CheckBoxText>{t('updateWallbox')}</CheckBoxText>
        </CheckboxWrapper>
        <CheckboxWrapper>
          <Checkbox checked={bank} onChange={() => setBank(!bank)} />
          <CheckBoxText>{t('updateBank')}</CheckBoxText>
        </CheckboxWrapper>
      </Row>
      <Row>
        <CheckboxWrapper>
          <Checkbox
            checked={tariff}
            disabled={onlyBank || isSpecialRequest}
            onChange={() => setTariff(!tariff)}
          />
          <CheckBoxText>{t('updateTariff')}</CheckBoxText>
        </CheckboxWrapper>{' '}
        <CheckboxWrapper>
          <Checkbox
            checked={other}
            disabled={onlyBank}
            onChange={() => setOther(!other)}
          />
          <CheckBoxText>{t('updateOther')}</CheckBoxText>
        </CheckboxWrapper>
      </Row>

      <Row>
        <InputWrapper fullWidth>
          <Description>{t('Comments')}</Description>
          <TextArea
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            maxLength={255}
            rows={5}
          ></TextArea>
          <Counter full={comment.length === 255}>
            {255 - comment.length}/255
          </Counter>
        </InputWrapper>
      </Row>
      {isErrorVisible && (
        <Row>
          <Error visible={isErrorVisible}>
            {errorMessage ? errorMessage : tActions('generalRequestError')}
          </Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button
            disabled={
              (!wallbox && !bank && !tariff && !other) ||
              (other && comment.length === 0)
            }
            onClick={sendData}
            variant="primary"
          >
            {t('sendToDriver')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

RequestDataUpdate.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  driverMail: PropTypes.string,
  idpId: PropTypes.string,
};

export default RequestDataUpdate;
