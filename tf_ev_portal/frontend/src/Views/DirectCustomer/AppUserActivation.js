import { useState, useContext } from 'react';
import styled from 'styled-components';
import { languages } from '../../constants/localization';
import JwtDecode from 'jwt-decode';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Button } from '../../Components';
import Select from '../../Components/Select';
import requester from '../../utils/requester';
import { userContext } from '../../ContextProvider';
import CircularProgress from '../../Components/CircularProgress';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import { MenuItem } from '@material-ui/core';
import CountryFlag from '../../Components/evDriver/CountryFlag';
import Center from '../../Components/helper/Center';
import TagManager from 'react-gtm-module';

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const Placeholder = styled.div`
  color: grey;
`;

const StaticValue = styled.div``;

const AppUserActivation = ({
  open,
  onClose,
  driverMail,
  cardNumber,
  expiryDate,
}) => {
  const { t } = useTranslation('evDriver');
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('generalRequestError');
  const [language, setLanugage] = useState({ value: '', error: false });
  const [isLoading, setIsLoading] = useState(false);
  const [showSucess, setShowSuccess] = useState(false);
  const tLocalization = useTranslation('localization').t;
  const tserviceProvider = useTranslation('serviceProvider').t;
  const tdirectCustomer = useTranslation('directCustomer').t;
  const tActions = useTranslation('actions').t;

  const { user } = useContext(userContext);
  let userMail = null;
  try {
    userMail = JwtDecode(user.access_token).preferred_username;
  } catch (e) {
    console.error(e);
  }

  const sendData = async () => {
    if (driverMail === userMail) {
      setErrorMessage('fleetManagerAsDriverError');
      setIsErrorVisible(true);
    } else {
      TagManager.dataLayer({
        dataLayer: {
          event: 'invitation_send_appDriver',
        },
      });
      try {
        setIsLoading(true);
        await requester()({
          method: 'post',
          url: '/driver_management',
          data: {
            email: driverMail,
            language: language.value,
            card_number: cardNumber,
            expiry_date: expiryDate.substring(0, 10),
          },
        });
        setIsLoading(false);
        setIsErrorVisible(false);
        setShowSuccess(true);
      } catch (error) {
        if (error.response?.status === 409) {
          setErrorMessage('userExistsError');
        } else {
          setErrorMessage('generalRequestError');
        }
        setIsLoading(false);
        setIsErrorVisible(true);
      }
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSucess}
      successMessage={`${t('successText')} ${driverMail}`}
      headline={t('appInvitationHeadline')}
    >
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('email')}</Description>
          <StaticValue>{driverMail}</StaticValue>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description error={language.error}>
            {tserviceProvider('inviteLanguage')}
          </Description>
          <Select
            big
            autoFocus
            error={language.error}
            value={language.value}
            onFocus={() => setLanugage({ ...language, error: false })}
            onChange={(e) => {
              setLanugage({ ...language, value: e.target.value });
            }}
            displayEmpty
            renderValue={
              language.value !== ''
                ? undefined
                : () => (
                    <Placeholder>
                      {tserviceProvider('inviteLanguagePlaceholder')}
                    </Placeholder>
                  )
            }
          >
            {Object.keys(languages).map((entry) => (
              <StyledMenuItem
                key={entry}
                value={entry}
                data-cy={`${entry}Select`}
              >
                <CountryFlag country={entry} />
                {tLocalization(`${entry}Language`)}
              </StyledMenuItem>
            ))}
          </Select>
        </InputWrapper>
      </Row>
      {isErrorVisible && (
        <Row>
          <Error>{tActions(errorMessage)}</Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button
            disabled={language.value === ''}
            onClick={sendData}
            variant="primary"
          >
            {tdirectCustomer('sendInvitation')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

AppUserActivation.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  driverMail: PropTypes.string,
  cardNumber: PropTypes.string,
};

export default AppUserActivation;
