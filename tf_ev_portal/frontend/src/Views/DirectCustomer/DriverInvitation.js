import { useState, useContext } from 'react';
import styled from 'styled-components';
import { languages } from '../../constants/localization';
import JwtDecode from 'jwt-decode';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Button, TextField } from '../../Components';
import Select from '../../Components/Select';
import requester from '../../utils/requester';
import { userContext } from '../../ContextProvider';
import CircularProgress from '../../Components/CircularProgress';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import { MenuItem } from '@material-ui/core';
import CountryFlag from '../../Components/evDriver/CountryFlag';
import Center from '../../Components/helper/Center';
import { mailRegex } from '../../utils/helper';
import TagManager from 'react-gtm-module';

const StyledMenuItem = styled(MenuItem)`
  color: rgba(0, 0, 0, 0.7);
  padding: 0 0.5rem;
  font-size: 14px !important;
  height: 40px;
  display: flex;
  align-items: center;
`;

const Placeholder = styled.div`
  color: grey;
`;

const StaticValue = styled.div``;

const DriverInvitation = ({ open, onClose, cardNumber, expiryDate }) => {
  const { t } = useTranslation('evDriver');
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('generalRequestError');
  const [language, setLanugage] = useState({ value: '', error: false });
  const [isLoading, setIsLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [email, setEmail] = useState({ value: '', error: false });
  const tLocalization = useTranslation('localization').t;
  const tserviceProvider = useTranslation('serviceProvider').t;
  const tdirectCustomer = useTranslation('directCustomer').t;
  const tActions = useTranslation('actions').t;

  const { user } = useContext(userContext);
  let userMail = null;
  try {
    userMail = JwtDecode(user.access_token).preferred_username;
  } catch (e) {
    console.error(e);
  }

  const sendData = async () => {
    setIsErrorVisible(false);
    if (email.value === userMail) {
      setEmail({ ...email, error: true });
    } else {
      if (email.value.match(mailRegex)) {
        TagManager.dataLayer({
          dataLayer: {
            event: 'invitation_send',
          },
        });
        try {
          setIsLoading(true);
          await requester()({
            method: 'post',
            url: '/driver_management',
            data: {
              email: email.value,
              language: language.value,
              card_number: cardNumber,
              expiry_date: expiryDate.substring(0, 10),
            },
          });
          setIsLoading(false);
          setIsErrorVisible(false);
          setShowSuccess(true);
        } catch (error) {
          if (error.response?.status === 409) {
            setErrorMessage('userExistsError');
          } else {
            setErrorMessage('generalRequestError');
          }
          setIsLoading(false);
          setIsErrorVisible(true);
        }
      } else {
        setEmail({ ...email, error: true });
      }
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSuccess}
      successMessage={`${t('successText')} ${email.value}`}
      headline={t('invitationHeadline')}
    >
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('cardNumber')}</Description>
          <StaticValue>{cardNumber}</StaticValue>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('expiryDate')}</Description>
          <StaticValue>{`${expiryDate.substr(5, 2)}/${expiryDate.substr(
            2,
            2,
          )}`}</StaticValue>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description error={email.error} style={{ marginBottom: '0' }}>
            {t('emailNewDriver')}
          </Description>
          <TextField
            autoFocus
            newDriver
            placeholder={t('driverMailPlaceholder')}
            error={email.error}
            onFocus={() => setEmail({ ...email, error: false })}
            autocomplete="off"
            value={email.value}
            onChange={(e) => setEmail({ ...email, value: e.target.value })}
            id="emailFilter"
          />
          <>
            {email.error && (
              <Error>
                {email.value === userMail
                  ? tActions('fleetManagerAsDriverError')
                  : t('emailError')}
              </Error>
            )}
          </>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description error={language.error}>
            {tserviceProvider('inviteLanguage')}
          </Description>
          <Select
            big
            error={language.error}
            value={language.value}
            onFocus={() => setLanugage({ ...language, error: false })}
            onChange={(e) => {
              setLanugage({ ...language, value: e.target.value });
            }}
            displayEmpty
            renderValue={
              language.value !== ''
                ? undefined
                : () => (
                    <Placeholder>
                      {tserviceProvider('inviteLanguagePlaceholder')}
                    </Placeholder>
                  )
            }
          >
            {Object.keys(languages).map((entry) => (
              <StyledMenuItem
                key={entry}
                value={entry}
                data-cy={`${entry}Select`}
              >
                <CountryFlag country={entry} />
                {tLocalization(`${entry}Language`)}
              </StyledMenuItem>
            ))}
          </Select>
        </InputWrapper>
      </Row>
      {isErrorVisible && (
        <Row>
          <Error>{tActions(errorMessage)}</Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button
            disabled={email.value === '' || language.value === ''}
            onClick={sendData}
            variant="primary"
          >
            {tdirectCustomer('sendInvitation')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

DriverInvitation.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  cardNumber: PropTypes.string,
};

export default DriverInvitation;
