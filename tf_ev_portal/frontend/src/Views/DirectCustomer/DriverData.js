import { useState, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import { userContext } from '../../ContextProvider';
import { But<PERSON>, TextField } from '../../Components';
import requester from '../../utils/requester';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import jwtDecode from 'jwt-decode';
import { mailRegex } from '../../utils/helper';
import Center from '../../Components/helper/Center';
import CircularProgress from '../../Components/CircularProgress';

const DriverData = ({
  open,
  onClose,
  driverMail,
  evseId,
  cardNumber,
  licensePlate,
  departmentId,
  isModifyMode,
  expiryDate,
  canModifyMail,
}) => {
  const { t } = useTranslation('evDriver');
  const tdirectCustomer = useTranslation('directCustomer').t;
  const tActions = useTranslation('actions').t;
  const [isLoading, setIsLoading] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [showSucess, setShowSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState(
    tActions('generalRequestError'),
  );

  const [departmentIdInput, setDepartmentIdInput] = useState(
    departmentId ? String(departmentId) : '',
  );
  const [driverMailInput, setDriverMailInput] = useState(driverMail || '');
  const [mailError, setMailError] = useState(false);
  const [licensePlateInput, setLicensePlateInput] = useState(
    licensePlate || '',
  );

  const { user } = useContext(userContext);
  let userMail = null;
  try {
    userMail = jwtDecode(user.access_token).preferred_username;
  } catch (e) {
    console.error(e);
  }

  const validateInputs = () => {
    const mailValid =
      driverMailInput.length === 0 || driverMailInput.match(mailRegex);
    if (
      !mailValid ||
      (departmentIdInput && !departmentIdInput.match(/^[0-9a-zA-Z]/)) ||
      (licensePlateInput && !licensePlateInput.match(/^[0-9a-zA-Z]/))
    ) {
      setErrorMessage(tActions('validationError'));
      setMailError(true);
      return false;
    }
    if (driverMailInput === userMail) {
      setErrorMessage(tActions('fleetManagerAsDriverError'));
      setMailError(true);
      return false;
    }
    setIsErrorVisible(false);
    return true;
  };

  const saveData = async () => {
    if (validateInputs()) {
      try {
        setIsLoading(true);
        await requester()({
          method: 'post',
          url: '/Additional_Information/Post_DirectChannel',
          data: {
            card_informations: [
              {
                card_number: cardNumber,
                department_id: departmentIdInput,
                email_address_ev_driver: driverMailInput,
                licence_plate: licensePlateInput,
                expiry_date: expiryDate,
              },
            ],
          },
        });
        setErrorMessage(tActions('generalRequestError'));
        setIsErrorVisible(false);
        setIsLoading(true);
        setShowSuccess(true);
      } catch (err) {
        setIsErrorVisible(true);
        setIsLoading(false);
      }
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSucess}
      successMessage={
        isModifyMode
          ? tdirectCustomer('successTextModifyData')
          : tdirectCustomer('successTextAddData')
      }
      headline={isModifyMode ? t('modifyDataHeadline') : t('addDataHeadline')}
    >
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('cardNumber')}</Description>
          <div>{cardNumber}</div>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('evseId')}</Description>
          <div>{evseId || '-'}</div>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <TextField
            autoFocus
            newDriver
            data-cy="department"
            value={departmentIdInput}
            label={t('department')}
            name="department"
            onChange={(e) => setDepartmentIdInput(e.target.value)}
          />
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <TextField
            data-cy="driverMail"
            newDriver
            error={mailError}
            onFocus={() => setMailError(false)}
            value={driverMailInput}
            disabled={!canModifyMail}
            onChange={(e) => setDriverMailInput(e.target.value)}
            label={isModifyMode ? t('modifyMail') : t('addDataMail')}
          />
          {mailError && <Error>{errorMessage}</Error>}
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <TextField
            newDriver
            value={licensePlateInput}
            onChange={(e) => setLicensePlateInput(e.target.value)}
            label={t('licencePlate')}
          />
        </InputWrapper>
      </Row>
      {isErrorVisible && (
        <Row>
          <Error>{errorMessage}</Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button data-cy="save" onClick={saveData} variant="primary">
            {t('save')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

export default DriverData;
