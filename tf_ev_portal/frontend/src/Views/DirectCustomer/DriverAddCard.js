import { useTranslation } from 'react-i18next';
import { useState } from 'react';
import TagManager from 'react-gtm-module';

import DialogWrapper from '../../Components/DialogWrapper';
import {
  ButtonWrapper,
  Description,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';

import styled from 'styled-components';
import { <PERSON><PERSON>, Error, TextField } from '../../Components';
import { serviceRequest } from '../../utils/requester';
import Center from '../../Components/helper/Center';
import moment from 'moment';
import HelpIcon from '@mui/icons-material/Help';

import CircularProgress from '../../Components/CircularProgress';
import { RowBox } from '../../Components/dashboard/RowTemplates';

const ReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  list-style-type: circle;
  padding: 0;
  margin-top: 2px;
`;

const SubReasonText = styled.p`
  display: list-item;
  list-style-position: inside;
  list-style-type: square;
  padding: 0;
  margin-top: 2px;
  padding-left: 5%;
`;

const HintWrapper = styled.div`
  cursor: pointer;
  display: inline-flex;
  color: var(--default-text);
`;

const StyledHelp = styled(HelpIcon)`
  font-size: 16px !important;
`;

const DriverAddCard = ({ open, onClose }) => {
  const { t } = useTranslation('evDriver');
  const { t: tActions } = useTranslation('actions');
  const { t: tOnboarding } = useTranslation('onboarding');
  const { t: tDirect } = useTranslation('directCustomer');
  const { t: tDriver } = useTranslation('evDriver');
  const [showSuccess, setShowSuccess] = useState(false);
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [hintOpen, setHintOpen] = useState(false);

  const [isLoading, setIsLoading] = useState(false);
  const [checkCardError, setCheckCardError] = useState(false);

  const [formState, setFormState] = useState({
    cardNumber: {
      value: '',
      error: false,
    },
    cardNumberConfirm: {
      value: '',
      error: false,
    },
    month: {
      value: '',
      error: false,
    },
    year: {
      value: '',
      error: false,
    },
  });

  const reasons = [
    'cardErrorReason1',
    'cardErrorReason2',
    'cardErrorReason3',
    'cardErrorReason4',
  ];

  const checkCard = async () => {
    let valid = true;
    const tempFormState = JSON.parse(JSON.stringify(formState));

    if (
      tempFormState.cardNumber.value !== tempFormState.cardNumberConfirm.value
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumber: {
          ...tempFormState.cardNumber,
          error: true,
        },
      });
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumberConfirm: {
          ...tempFormState.cardNumberConfirm,
          error: true,
        },
      });
    }

    if (tempFormState.cardNumber.value.length !== 18) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        cardNumber: {
          ...tempFormState.cardNumber,
          error: true,
        },
      });
    }
    if (
      tempFormState.year.value.length !== 2 ||
      Number(tempFormState.year.value) < new Date().getYear() - 100
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        year: {
          ...tempFormState.year,
          error: true,
        },
      });
    }
    if (
      tempFormState.month.value.length !== 2 ||
      Number(tempFormState.month.value) > 12 ||
      Number(tempFormState.month.value) < 1
    ) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        month: {
          ...tempFormState.month,
          error: true,
        },
      });
    }

    if (!valid) {
      setFormState(tempFormState);
      return;
    }
    setIsLoading(true);
    setIsErrorVisible(false);
    setCheckCardError(false);

    const yearMonth = '20' + formState.year.value + '-' + formState.month.value;
    const days = moment(yearMonth, 'YYYY-MM').daysInMonth();
    const fullExpiryDate = yearMonth + '-' + days;

    try {
      await serviceRequest().post('/approvals/v1/approval/', {
        approvaltype: 'token',
        data: {
          token_visual_number: formState.cardNumber.value,
          expiry_date: fullExpiryDate,
        },
      });

      setCheckCardError(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (err) {
      if (err.response?.data?.status_code === 2000) {
        setCheckCardError(true);
        setIsLoading(false);
      } else {
        setIsErrorVisible(true);
        setIsLoading(false);
      }
    }
  };

  return (
    <>
      {hintOpen && (
        <DialogWrapper
          open={hintOpen}
          onClose={() => {
            TagManager.dataLayer({
              dataLayer: {
                event: 'popup_interaction',
                popup: {
                  name: `popup_card_error`,
                  interaction_type: 'close',
                },
              },
            });
            setHintOpen(false);
          }}
          headline={t('cardErrorPopupHeader')}
          width="800px"
        >
          <RowBox></RowBox>

          {reasons.map((reason, i) => {
            return (
              <>
                <ReasonText>{t(reason)}</ReasonText>
                <SubReasonText>{t(reason + 'Sub1')}</SubReasonText>
              </>
            );
          })}
        </DialogWrapper>
      )}
      <DialogWrapper
        open={open}
        onClose={onClose}
        showSuccess={showSuccess}
        successMessage={tDriver('successTextApproval')}
        headline={tOnboarding('fcCardInformation')}
      >
        <>
          <Row>
            <InputWrapper fullWidth>
              <TextField
                value={formState.cardNumber.value}
                error={formState.cardNumber.error}
                type="text"
                maxLength="18"
                newDriver
                autoFocus
                label={tOnboarding('fcCardnumber')}
                placeholder={tOnboarding('fcCardnumberPlaceholder')}
                onFocus={() =>
                  setFormState({
                    ...formState,
                    cardNumber: {
                      ...formState.cardNumber,
                      error: false,
                    },
                  })
                }
                onChange={(e) => {
                  const { value } = e.target;
                  if (value.match(/^\d+$/) || value === '') {
                    setFormState({
                      ...formState,
                      cardNumber: {
                        ...formState.cardNumber,
                        value: value,
                      },
                    });
                  }
                }}
              />
            </InputWrapper>
          </Row>
          <Row>
            <InputWrapper fullWidth>
              <TextField
                value={formState.cardNumberConfirm.value}
                error={formState.cardNumberConfirm.error}
                type="text"
                maxLength="18"
                newDriver
                autoFocus
                label={tOnboarding('fcCardnumberConfirm')}
                placeholder={tOnboarding('fcCardnumberPlaceholder')}
                onFocus={() =>
                  setFormState({
                    ...formState,
                    cardNumberConfirm: {
                      ...formState.cardNumberConfirm,
                      error: false,
                    },
                  })
                }
                onChange={(e) => {
                  const { value } = e.target;
                  if (value.match(/^\d+$/) || value === '') {
                    setFormState({
                      ...formState,
                      cardNumberConfirm: {
                        ...formState.cardNumberConfirm,
                        value: value,
                      },
                    });
                  }
                }}
              />
            </InputWrapper>
          </Row>

          {formState.cardNumber.error && (
            <Row>
              <Error>
                {t('cardError')}{' '}
                <HintWrapper
                  onClick={() => {
                    TagManager.dataLayer({
                      dataLayer: {
                        event: 'popup_interaction',
                        popup: {
                          name: `popup_card_error`,
                          interaction_type: 'open',
                        },
                      },
                    });
                    setHintOpen(true);
                  }}
                >
                  <StyledHelp />
                </HintWrapper>
              </Error>
            </Row>
          )}
          <div>
            <Description
              horizontal
              error={formState.month.error || formState.month.year}
            >
              {t('expiryDate')}
            </Description>
          </div>
          <Row>
            <InputWrapper style={{ width: 40, marginRight: '0.5rem' }}>
              <TextField
                value={formState.month.value}
                error={formState.month.error}
                disableMinWidth
                style={{ textAlign: 'center' }}
                type="text"
                maxLength="2"
                newDriver
                placeholder={t('MM')}
                onFocus={() =>
                  setFormState({
                    ...formState,
                    month: {
                      ...formState.month,
                      error: false,
                    },
                  })
                }
                onChange={(e) => {
                  const { value } = e.target;
                  if (value.match(/^\d+$/) || value === '') {
                    setFormState({
                      ...formState,
                      month: {
                        ...formState.month,
                        value: value,
                      },
                    });
                  }
                }}
              />
            </InputWrapper>
            <InputWrapper style={{ width: 40 }}>
              <TextField
                value={formState.year.value}
                error={formState.year.error}
                disableMinWidth
                style={{ textAlign: 'center' }}
                type="text"
                maxLength="2"
                placeholder={t('YY')}
                newDriver
                onFocus={() =>
                  setFormState({
                    ...formState,
                    year: {
                      ...formState.year,
                      error: false,
                    },
                  })
                }
                onChange={(e) => {
                  const { value } = e.target;
                  if (value.match(/^\d+$/) || value === '') {
                    setFormState({
                      ...formState,
                      year: {
                        ...formState.year,
                        value: value,
                      },
                    });
                  }
                }}
              />
            </InputWrapper>
          </Row>
        </>

        <Error
          visible={isErrorVisible}
          text={tActions('generalRequestError')}
        />
        <Error visible={checkCardError} text={tOnboarding('checkCardError')} />

        {isLoading ? (
          <Center>
            <CircularProgress />
          </Center>
        ) : (
          <ButtonWrapper>
            <Button variant="primary" onClick={checkCard}>
              {tDirect('addCard')}
            </Button>
          </ButtonWrapper>
        )}
      </DialogWrapper>
    </>
  );
};

export default DriverAddCard;
