import { useCallback, useContext, useEffect, useState } from 'react';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import BoltIcon from '@mui/icons-material/Bolt';
import PageWrapper from '../../Components/PageWrapper';
import logger from '../../utils/logger';
import requester from '../../utils/requester';
import CenteredCircularProgress from '../../Components/helper/CenteredCircularProgress';
import ButtonSwitch from '../../Components/dashboard/ButtonSwitch';
import DialogRouter from '../../Components/dashboard/DialogRouter';
import PersonIcon from '@mui/icons-material/Person';
import CreditCardIcon from '@mui/icons-material/CreditCard';
import EvStationIcon from '@mui/icons-material/EvStation';
import EuroIcon from '@mui/icons-material/Euro';
import { userContext } from '../../ContextProvider';
import styled from 'styled-components';
import Info from '../../Components/userDetails/Info';
import Tariffs from '../../Components/userDetails/Tariffs';
import Cards from '../../Components/userDetails/Cards';
import { Tab, Box, Grid, Tabs } from '@mui/material';

const MainMenuTab = styled(Tab)`
  text-transform: none !important;
  justify-content: flex-start !important;
  align-items: center !important;
  font-size: 14px !important;
  min-height: 48px !important;
  color: black !important;
  height: 70px !important;
  padding: 0 0 0 1rem !important;
  svg {
    color: var(--trafineo-rot-100) !important;
  }
  &.Mui-selected {
    color: black !important;
    background: #ebf2eb !important;
  }
`;
function a11yProps(index) {
  return {
    id: `main-tab-${index}`,
    'aria-controls': `main-tabpanel-${index}`,
  };
}

const UserDetails = () => {
  const { t } = useTranslation('directCustomer');
  const { ev_driver_idp_id, fleetManagerId, from } = useParams();
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(undefined);
  const [searchParams, setSearchParams] = useSearchParams();
  const { REACT_APP_KEYCLOAK_SUPERUSER_ROLE } = process.env;
  const { role } = useContext(userContext);
  const isSuperUser = role.toLowerCase() === REACT_APP_KEYCLOAK_SUPERUSER_ROLE;
  const navigate = useNavigate();

  useEffect(() => {
    if (!searchParams.get('nav')) {
      navigate(`${window.location.pathname}?nav=information`, {
        replace: true,
      });
    }
  }, [searchParams, setSearchParams, navigate]);

  const getUserCards = () => {
    const userCards = [];
    user.tokens.forEach((token) => {
      userCards.push({
        card_number: token.token_visual_number,
        expiry_date:
          token.expiry_date.split('-')[1] +
          '/' +
          token.expiry_date.split('-')[0].slice(-2),
      });
    });
    return JSON.stringify(userCards);
  };

  const TabMapping = ['information', 'tariffs', 'cards'];
  const mainMenuTabs = [
    [
      'information',
      (user) => <Info user={user} />,
      <PersonIcon fontSize="small" />,
    ],
    [
      'tariffs',
      (user) => <Tariffs user={user} />,
      <BoltIcon fontSize="small" />,
    ],
    [
      'cards',
      (user) => <Cards tokens={user.tokens || []} />,
      <CreditCardIcon fontSize="small" />,
    ],
    [
      'reimbursements',
      (user) =>
        navigate(
          `/reimbursements/${user.ev_driver_idp_id}?ev_driver_idp_id=${ev_driver_idp_id}&email=${user.email_address_ev_driver}`,
        ),
      <EuroIcon fontSize="small" />,
    ],
    [
      'chargingSessions',
      (user) =>
        navigate(
          `/charging_sessions/${
            user.ev_driver_idp_id
          }?ev_driver_idp_id=${ev_driver_idp_id}&email=${
            user.email_address_ev_driver
          }&cards=${getUserCards()}`,
        ),
      <EvStationIcon fontSize="small" />,
    ],
  ];

  const reFetchData = async () => {
    const queryParams = [['ev_driver_idp_id', ev_driver_idp_id]];
    if (isSuperUser) {
      queryParams.push(['fleetmanager_id', fleetManagerId]);
    }
    const rspData = await getData(new URLSearchParams(queryParams));
    if (
      rspData.information &&
      rspData.information[0] &&
      rspData.information.length === 1
    ) {
      setUser(rspData.information[0]);
    } else {
      navigate('/error', { replace: true });
    }
  };

  const getData = useCallback(async (params) => {
    setIsLoading(true);
    try {
      const rsp = await requester().get(
        '/Information_Overview/card_overview_by_driver',
        {
          params: new URLSearchParams(params),
        },
      );
      setIsLoading(false);
      return rsp.data;
    } catch (err) {
      logger().error(`Couldn't get overview data from api.\n${err.message}`);
      setIsLoading(false);
      return {};
    }
  }, []);

  useEffect(() => {
    const fetchData = async () => {
      const queryParams = [['ev_driver_idp_id', ev_driver_idp_id]];
      if (isSuperUser) {
        queryParams.push(['fleetmanager_id', fleetManagerId]);
      }
      const rspData = await getData(new URLSearchParams(queryParams));
      if (
        rspData.information &&
        rspData.information[0] &&
        rspData.information.length === 1
      ) {
        setUser(rspData.information[0]);
      } else {
        navigate('/error');
      }
    };
    fetchData();
  }, [ev_driver_idp_id, getData, navigate, fleetManagerId, isSuperUser]);

  if (isLoading || !user) {
    return <CenteredCircularProgress />;
  }
  const driverStatus = user.driver_approval_status
    ? user.driver_approval_status.map(
        ({ driver_approval_status_name }) => driver_approval_status_name,
      )
    : [null];
  const handleChange = (newValue) => {
    setSearchParams({ nav: newValue });
  };

  return (
    <PageWrapper
      backButton
      backFuction={() =>
        navigate(
          `${isSuperUser ? `/cardoverview/${fleetManagerId}` : '/'}${
            from === 'c'
              ? '?tab=cards'
              : from === 'u'
              ? '?tab=drivers'
              : '?tab=summary'
          }`,
        )
      }
      title={user.email_address_ev_driver || ''}
      icon={
        <Box px="1rem" width="200px">
          <ButtonSwitch status={driverStatus} inUserDetails data={user} />
        </Box>
      }
      minWidth="1230px"
    >
      <DialogRouter onDialogClose={reFetchData} />
      <Grid mt="2rem" container>
        <Grid item xs={3}>
          <Box pr="4rem">
            <Tabs
              orientation="vertical"
              value={searchParams.get('nav') || TabMapping[0]}
              indicatorColor="transparent"
              onChange={(event, newValue) => {
                if (TabMapping.indexOf(newValue) < 3) handleChange(newValue);
              }}
              aria-label="informationTabs"
            >
              {mainMenuTabs.map((arr, i) => {
                const e = arr[0];
                const icon = arr[2];
                return (
                  <MainMenuTab
                    key={e}
                    icon={icon}
                    iconPosition="start"
                    disableRipple
                    {...(i > 2 && { onClick: () => mainMenuTabs[i][1](user) })}
                    value={e}
                    label={t(e)}
                    {...a11yProps(i)}
                  />
                );
              })}
            </Tabs>
          </Box>
        </Grid>
        <Grid item xs={9}>
          <Box pl="1rem">
            {mainMenuTabs[
              TabMapping.indexOf(searchParams.get('nav')) === -1
                ? 0
                : TabMapping.indexOf(searchParams.get('nav'))
            ][1](user)}
          </Box>
        </Grid>
      </Grid>
    </PageWrapper>
  );
};

export default UserDetails;
