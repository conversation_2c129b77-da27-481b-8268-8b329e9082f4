import { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';

import { Button } from '../../Components';
import DialogWrapper from '../../Components/DialogWrapper';
import { Error, Row } from '../../Components/evDriver/PageTemplate';

import logger from '../../utils/logger';
import requester from '../../utils/requester';

import CircularProgress from '../../Components/CircularProgress';
import { LoadingCircleContainer } from '../../Components/dashboard/DashboardTemplates';

import styled from 'styled-components';
import PropTypes from 'prop-types';
import TagManager from 'react-gtm-module';

const Headline = styled.div`
  color: black;
  margin: auto;
  font-size: 24px;
  font-weight: 700;
  margin-top: 1rem;
`;

const GifDisplay = styled.img`
  width: 100%;
`;

const StaticValue = styled.div``;

const ButtonWrapper = styled.div`
  margin: auto;
`;

const TrainingView = ({ open, onClose, training, gif, isDriver = false }) => {
  const [isLoading, setIsLoading] = useState(false);
  const [description, setDescription] = useState('');
  const { t } = useTranslation('directCustomer');
  const { t: tEvDriver } = useTranslation('evDriver');
  const tActions = useTranslation('actions').t;

  const completeTraining = async (id) => {
    setIsLoading(true);
    TagManager.dataLayer({
      dataLayer: {
        event: 'training_interaction',
        training: {
          names: training.training_name,
          interaction_type: 'complete',
        },
      },
    });
    try {
      await requester().post(`/trainings/${id}/user`);
      setIsLoading(false);
      onClose();
    } catch (err) {
      logger().error(
        `Couldn't get overview training data from api.\n${err.message}`,
      );
      setIsLoading(false);
      onClose();
      return {};
    }
  };

  useEffect(() => {
    const descriptionContent = training.content.find(
      (element) => element.key === 'description',
    );
    setDescription(descriptionContent.value);
  }, [training]);

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      successMessage={t('successTrainingComplete')}
      width={'65vw'}
    >
      {!training.content ||
      !training.content.find((element) => element.key === 'description') ? (
        <>
          <Row>
            <Headline>
              <>
                {isDriver &&
                  tEvDriver(training.training_name || 'generalRequestError')}
                {!isDriver &&
                  t(training.training_name || 'generalRequestError')}
              </>
            </Headline>
          </Row>
          <Row>
            <Error>{tActions('generalRequestError')}</Error>
          </Row>
        </>
      ) : (
        <>
          <GifDisplay src={gif} alt={training.training_name}></GifDisplay>
          <Row>
            {isDriver && (
              <Headline>{tEvDriver(training.training_name)}</Headline>
            )}

            {!isDriver && <Headline>{t(training.training_name)}</Headline>}
          </Row>
          <Row>
            {isDriver && <StaticValue>{tEvDriver(description)}</StaticValue>}

            {!isDriver && <StaticValue>{t(description)}</StaticValue>}
          </Row>

          <Row>
            <ButtonWrapper>
              {!isLoading && (
                <Button onClick={() => completeTraining(training.id)}>
                  {tActions('completeTraining')}
                </Button>
              )}
              {isLoading && (
                <LoadingCircleContainer style={{ padding: '2rem' }}>
                  <CircularProgress />
                </LoadingCircleContainer>
              )}
            </ButtonWrapper>
          </Row>
        </>
      )}
    </DialogWrapper>
  );
};

TrainingView.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  cardNumber: PropTypes.string,
};

export default TrainingView;
