import { useState, useEffect } from 'react';
import { Grid, Box } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useNavigate, useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import qs from 'querystring';
import Content from '../../Components/helper/Content';
import { Card, Button, Error, BackButton } from '../../Components';
import TextArea from '../../Components/TextArea';
import Checkbox from '../../Components/Checkbox';
import CircularProgress from '../../Components/CircularProgress';
import requester from '../../utils/requester';
import Select from '../../Components/Select';
import { languages } from '../../constants/localization';
import SuccessDialog from '../../Components/SuccessDialog';

const useStyles = makeStyles({
  viewHeader: {
    textAlign: 'left',
  },
  errorText: {
    color: 'var(--trafineo-rot-100)',
    paddingLeft: '1rem',
  },
  row: {
    display: 'flex',
    width: '100%',
    justifyContent: 'center',
    alignItems: 'center',
  },
  bold: {
    fontWeight: 'bold',
  },
  button: {
    marginTop: '3rem',
    marginBottom: '1rem',
    width: '220px',
  },
  grid: {
    boxSizing: 'border-box',
    padding: '1rem',
  },
  staticInfo: {
    display: 'flex',
    flexDirection: 'column',
    border: 'solid 1px var(--trafineo-grau-70)',
    backgroundColor: 'var(--trafineo-grau-20)',
    padding: '0.5rem 1rem',
    marginLeft: '1rem',
  },
  staticInfoRow: {
    textAlign: 'left',
    display: 'flex',
    flexDirection: 'column',
    wordBreak: 'break-word',
  },
  main: {
    maxWidth: 1000,
    margin: '0 auto',
    '& a': {
      color: 'var(--trafineo-rot-100)',
    },
    '& p': {
      fontSize: '14px',
    },
  },
  headline: {
    fontWeight: 'bold',
    marginBottom: '1.5rem',
  },
  mainText: {
    whiteSpace: 'pre-line',
  },
  checkLabel: {
    margin: '0 2rem 0 1rem',
    fontWeight: 'bold',
  },
  link: {
    fontSize: '12px',
    color: 'var(--default-text)',
    fontWeight: 'bold',
  },
  checkWrapper: {
    marginTop: '1rem',
  },
  languageSelect: {
    fontSize: 12,
    marginLeft: '1rem',
  },
  languageSelectHeadline: {
    margin: '1.25rem 0',
  },
  margin: {
    marginBottom: '4rem',
  },
});

const DriverMail = () => {
  /**
   * cardNumber, driverMail and evseId are supplied by query-string
   */
  const classes = useStyles();
  const { t } = useTranslation('evDriver');
  const tLocalization = useTranslation('localization').t;
  const tActions = useTranslation('actions').t;
  const tDirectCustomer = useTranslation('directCustomer').t;
  const navigate = useNavigate();
  const location = useLocation();
  const rawQueryString = location.search.slice(1);
  const parsedQueryString = qs.parse(rawQueryString);
  const { cardNumber, evseId, driverMail, mode } = parsedQueryString;

  const areParamsValid = Boolean(!!cardNumber && !!evseId && !!driverMail);
  const [comment, setComment] = useState('');
  const [wallbox, setWallbox] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [bank, setBank] = useState(false);
  const [language, setLanguage] = useState('de');
  const [isErrorVisible, setIsErrorVisible] = useState(false);

  const viewMode = mode && mode.length > 0 ? 'reject' : '';

  // visibility of dialog
  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);
  useEffect(() => {
    // route to mainpage if params are not valid
    if (!areParamsValid) {
      navigate('/');
    }
  }, [navigate, areParamsValid]);

  const handleSuccess = async (url, data) => {
    try {
      setIsLoading(true);
      await requester().post(url, data);
      setIsErrorVisible(false);
      setIsStatusDialogVisible(true);
      setIsLoading(false);
    } catch (err) {
      setIsErrorVisible(true);
      setIsLoading(false);
    }
  };

  const handleRejectSuccess = async () => {
    handleSuccess('/Ev_Driver_Data_Approval/Post_Driver_Data_Approval', {
      card_number: cardNumber,
      approval_status: 0,
      reject_reason: comment,
      language,
    });
  };

  const handleUpdateSuccess = () => {
    const data = {
      card_number: cardNumber,
      comment,
      language,
      updateData: [],
    };
    if (wallbox) {
      data.updateData.push('wallbox');
    }
    if (bank) {
      data.updateData.push('banking_data');
    }
    handleSuccess('/Ev_Driver_Data_Approval/Post_Update_Data_Request', data);
  };

  return (
    <div className={classes.margin}>
      <Content>
        <BackButton />
        <h1 className={classes.viewHeader}>
          {viewMode === 'reject' ? t('rejectHeadline') : t('changeHeadline')}
        </h1>
        <SuccessDialog
          isVisible={isStatusDialogVisible}
          onClose={() => navigate('/')}
          text={`${
            viewMode === 'reject'
              ? tDirectCustomer('successTextRejectData')
              : tDirectCustomer('successTextInviteChangeData')
          } ${driverMail}`}
        />
        <Card>
          <Grid spacing={1} className={classes.main} container>
            <Grid item className={classes.grid} xs={7}>
              <p className={classes.headline}>
                {viewMode === 'reject'
                  ? t('rejectHeadline2')
                  : t('requestHeadline2')}
              </p>
              <p className={classes.mainText}>
                {viewMode === 'reject'
                  ? t('rejectMainText')
                  : t('requestMainText')}
              </p>
            </Grid>
            <Grid item className={classes.grid} xs={5}>
              <Box data-cy="infoBox" className={classes.staticInfo}>
                <Box className={classes.staticInfoRow}>
                  <p>
                    {t('driverMail')}{' '}
                    <span className={classes.bold}>{driverMail}</span>
                  </p>
                </Box>
                <Box className={classes.staticInfoRow}>
                  <p>
                    {t('cardNumber')}{' '}
                    <span className={classes.bold}>{cardNumber}</span>
                  </p>
                  <p>
                    {t('evseId')} <span className={classes.bold}>{evseId}</span>
                  </p>
                </Box>
              </Box>
            </Grid>
          </Grid>
          <Grid spacing={1} className={classes.main} container>
            <Grid item className={classes.grid} xs={7}>
              <p className={classes.bold}>{t('Comments')}</p>
              <TextArea
                data-cy="commentField"
                autoFocus
                value={comment}
                onChange={(e) => setComment(e.target.value)}
              />
              {viewMode !== 'reject' && (
                <Box
                  className={classes.checkWrapper}
                  display="flex"
                  alignItems="center"
                >
                  <Checkbox
                    id="wallboxCheckbox"
                    checked={wallbox}
                    onChange={() => setWallbox(!wallbox)}
                    boxSize="large"
                  />
                  <p className={classes.checkLabel}>{t('updateWallbox')}</p>
                  <Checkbox
                    checked={bank}
                    onChange={() => setBank(!bank)}
                    boxSize="large"
                  />
                  <p className={classes.checkLabel}>{t('updateBank')}</p>
                </Box>
              )}
            </Grid>
            <Grid item className={classes.grid} xs={5}>
              <Box className={classes.languageSelect}>
                <Box className={classes.languageSelectHeadline}>
                  {tDirectCustomer('headlineSelectEmailLanguage')}
                </Box>
                <Select
                  value={language}
                  onChange={(e) => setLanguage(e.target.value)}
                >
                  {Object.values(languages).map(({ key: entry }) => (
                    <option key={entry} value={entry}>
                      {tLocalization(`${entry}Language`)}
                    </option>
                  ))}
                </Select>
              </Box>
            </Grid>
            <Grid container justifyContent="center">
              <Error
                visible={isErrorVisible}
                text={tActions('generalRequestError')}
              />
            </Grid>
            <Grid container justifyContent="center">
              <Box className={`${classes.row} ${classes.button}`}>
                {(isLoading && <CircularProgress />) || (
                  <Button
                    data-cy="sendButton"
                    disabled={viewMode !== 'reject' && !wallbox && !bank}
                    onClick={
                      viewMode === 'reject'
                        ? handleRejectSuccess
                        : handleUpdateSuccess
                    }
                    variant="primary"
                  >
                    {t('sendToDriver')}
                  </Button>
                )}
              </Box>
            </Grid>
          </Grid>
        </Card>
      </Content>
    </div>
  );
};

export default DriverMail;
