import { useContext } from 'react';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import DialogWrapper from '../../Components/DialogWrapper';
import {
  TariffTypeSelectionContainer,
  TariffTypeSelectionOption,
} from '../../Components/evDriver/PageTemplate';
import { dialogContext } from '../../ContextProvider';

import CreditCardIcon from '@mui/icons-material/CreditCard';
import PersonOutlineIcon from '@mui/icons-material/PersonOutline';

const InviteOrAssign = ({ open, onClose, idpId, cardNumber, expiryDate }) => {
  const { t } = useTranslation('evDriver');

  const { dialogData, setDialogData } = useContext(dialogContext);

  const inviteDriver = () => {
    setDialogData({
      ...dialogData,
      type: 'invitation',
      cardNumber: cardNumber,
      expiryDate,
      open: true,
    });
  };

  const assignDriver = () => {
    setDialogData({
      ...dialogData,
      type: 'assign',
      cardNumber: cardNumber,
      expiryDate,
      idpId: idpId,
      open: true,
    });
  };

  const TypeSelection = () => {
    return (
      <TariffTypeSelectionContainer>
        <TariffTypeSelectionOption
          style={{ width: '100%' }}
          onClick={() => {
            inviteDriver();
          }}
        >
          <PersonOutlineIcon style={{ marginRight: '0.5rem' }} />
          {t('driverInvite')}
        </TariffTypeSelectionOption>
        <TariffTypeSelectionOption
          style={{ width: '100%' }}
          onClick={() => {
            assignDriver();
          }}
        >
          <CreditCardIcon style={{ marginRight: '0.5rem' }} />
          {t('assignDriver')}
        </TariffTypeSelectionOption>
      </TariffTypeSelectionContainer>
    );
  };

  return (
    <DialogWrapper open={open} onClose={onClose} headline={t('assignCard')}>
      <TypeSelection />
    </DialogWrapper>
  );
};

InviteOrAssign.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  cardNumber: PropTypes.string,
};

export default InviteOrAssign;
