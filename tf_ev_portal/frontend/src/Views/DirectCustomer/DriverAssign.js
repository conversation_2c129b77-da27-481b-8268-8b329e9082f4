import { useState, useEffect, useRef, useMemo } from 'react';
import debounce from 'lodash.debounce';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import PropTypes from 'prop-types';
import { Button, TextField } from '../../Components';
import requester from '../../utils/requester';
import CircularProgress from '../../Components/CircularProgress';
import DialogWrapper from '../../Components/DialogWrapper';
import { Box } from '@mui/system';
import {
  ButtonWrapper,
  Description,
  Error,
  InputWrapper,
  Row,
} from '../../Components/evDriver/PageTemplate';
import { MenuItem, Grow, Paper, Popper, MenuList } from '@material-ui/core';
import { IconButton } from '@material-ui/core';
import Center from '../../Components/helper/Center';
import ClickAwayListener from '@mui/material/ClickAwayListener';
import Close from '@material-ui/icons/Close';

const PopperStaticText = styled.div`
  padding: 0.5rem;
  width: 320px;
  font-size: 14px;
  box-sizing: border-box;
  text-align: left;
  line-height: 28px;
`;

const ResultPopper = styled(Popper)`
  z-index: 10;
  width: 89.2%;
  padding-left: 2.3rem;
  padding-right: 1.8rem;
  top: ${(props) => (props.noResults ? 0 : '-2px !important')};
`;

const StaticValue = styled.div``;

const HintText = styled.p`
  margin-bottom: 20px;
`;

const DriverAssign = ({ open, onClose, cardNumber, expiryDate }) => {
  const { t } = useTranslation('evDriver');
  const [isErrorVisible, setIsErrorVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState('generalRequestError');
  const [isLoading, setIsLoading] = useState(false);
  const [isSendLoading, setIsSendLoading] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isSearchFocused, setIsSearchFocused] = useState(false);
  const [driverMailInput, setDriverMailInput] = useState();
  const [emailFilter, setEmailFilter] = useState('');
  const [noEmailsFound, setNoEmailsFound] = useState(false);
  const [allEmails, setAllEmails] = useState([]);
  const [idpId, setIdpId] = useState([]);
  const [query, setQuery] = useState('');
  const tdirectCustomer = useTranslation('directCustomer').t;
  const tActions = useTranslation('actions').t;

  const sendData = async () => {
    try {
      setIsSendLoading(true);
      await requester()({
        method: 'post',
        url: `/ev_driver/${idpId}/token`,
        data: {
          token_visual_number: cardNumber,
          expiry_date: expiryDate,
        },
      });
      setIsSendLoading(false);
      setIsErrorVisible(false);
      setShowSuccess(true);
    } catch (error) {
      if (error.response?.status === 409) {
        setErrorMessage('userExistsError');
      } else {
        setErrorMessage('generalRequestError');
      }
      setIsSendLoading(false);
      setIsErrorVisible(true);
    }
  };

  //Menu
  const [openMenu, setOpenMenu] = useState(false);
  const anchorRef = useRef(null);

  const handleToggle = () => {
    setOpenMenu((prevOpen) => !prevOpen);
  };

  const handleClose = () => {
    setEmailFilter('');
    setAllEmails([]);
  };
  const handleSelect = (selectedEmail) => {
    setDriverMailInput(selectedEmail.email);
    setIdpId(selectedEmail.idpId);
    setEmailFilter('');
    setAllEmails([]);
  };

  function handleListKeyDown(event) {
    if (event.key === 'Tab') {
      event.preventDefault();
      setOpenMenu(false);
    } else if (event.key === 'Escape') {
      setOpenMenu(false);
    }
  }

  const prevOpen = useRef(openMenu);
  useEffect(() => {
    if (prevOpen.current === true && openMenu === false) {
      anchorRef.current.focus();
    }

    prevOpen.current = openMenu;
  }, [openMenu]);

  const getEmails = async (searchString) => {
    setIsLoading(true);
    try {
      const rsp = await requester().get(
        '/Information_Overview/card_overview_by_driver',
        {
          params: {
            email: searchString,
          },
        },
      );
      setIsLoading(false);
      return rsp.data.information;
    } catch (err) {
      console.error(err);
      setIsLoading(false);
      return null;
    }
  };

  useEffect(() => {
    const fetchData = async (param) => {
      const rspData = await getEmails(param);
      const emailArray = [];
      rspData.forEach((element) => {
        if (
          element.email_address_ev_driver &&
          emailArray.indexOf(element) === -1
        ) {
          emailArray.push({
            email: element.email_address_ev_driver,
            idpId: element.ev_driver_idp_id,
          });
        }
      });
      if (rspData && emailArray.length > 0) {
        emailArray.sort((a, b) => a.email.localeCompare(b.email));
        setNoEmailsFound(false);
        setAllEmails(emailArray);
      } else {
        setNoEmailsFound(true);
      }
    };

    if (query !== '' && query.length > 1) {
      fetchData(query);
    }
  }, [query]);

  const debouncedSearch = useMemo(() => {
    return debounce(setQuery, 750);
  }, []);

  const changeEmailFilter = (value) => {
    setEmailFilter(value);
    if (value.length > 1) {
      setNoEmailsFound(false);
    } else {
      setAllEmails([]);
    }
  };

  return (
    <DialogWrapper
      open={open}
      onClose={onClose}
      showSuccess={showSuccess}
      successMessage={t('successAssign')}
      headline={t('assignHeadline')}
    >
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('cardNumber')}</Description>
          <StaticValue>{cardNumber}</StaticValue>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description>{t('expiryDate')}</Description>
          <StaticValue>{`${expiryDate.substr(5, 2)}/${expiryDate.substr(
            2,
            2,
          )}`}</StaticValue>
        </InputWrapper>
      </Row>
      <Row>
        <InputWrapper fullWidth>
          <Description style={{ marginBottom: '0' }}>{t('email')}</Description>
          {driverMailInput ? (
            <MenuItem
              disableRipple
              style={{
                padding: '0.5rem',
                marginBottom: '0.25rem',
                marginTop: '0.25rem',
                height: '46px',
                backgroundColor: '#F9F9F9',
              }}
            >
              <div style={{ fontSize: '14px', lineHeight: '46px' }}>
                {driverMailInput}
              </div>
              <IconButton
                style={{ position: 'absolute', right: '10px' }}
                size="small"
                data-cy="rejectAction"
                onClick={() => {
                  setDriverMailInput('');
                }}
              >
                <Close fontSize="small" />
              </IconButton>
            </MenuItem>
          ) : (
            <>
              <TextField
                placeholder={t('driverMailPlaceholder')}
                newDriver
                onFocus={() => {
                  setIsSearchFocused(true);
                }}
                onBlur={() => {
                  setIsSearchFocused(false);
                }}
                autocomplete="off"
                value={emailFilter}
                onChange={(e) => {
                  changeEmailFilter(e.target.value.trim());
                  debouncedSearch(e.target.value.trim());
                }}
                id="emailFilter"
                aria-controls={open ? 'composition-menu' : undefined}
                aria-expanded={open ? 'true' : undefined}
                aria-haspopup="true"
                ref={anchorRef}
                onClick={handleToggle}
              />
              {isLoading && (
                <div
                  style={{
                    position: 'relative',
                  }}
                >
                  <div
                    style={{
                      position: 'absolute',
                      right: '10px',
                      top: '-35px',
                    }}
                  >
                    <CircularProgress small />
                  </div>
                </div>
              )}
              <ResultPopper
                noResults={emailFilter.length < 2 || noEmailsFound}
                open={isSearchFocused || emailFilter.length > 1}
                anchorEl={anchorRef.current}
                role={undefined}
                placement="bottom-start"
                transition
                disablePortal
              >
                {({ TransitionProps, placement }) => (
                  <Grow
                    {...TransitionProps}
                    style={{
                      transformOrigin:
                        placement === 'bottom-start' ? 'top' : 'bottom',
                    }}
                  >
                    <Paper>
                      <ClickAwayListener onClickAway={handleClose}>
                        {emailFilter.length < 2 ? (
                          <PopperStaticText>
                            {t('enter2Letters')}
                          </PopperStaticText>
                        ) : noEmailsFound ? (
                          <PopperStaticText>
                            {t('noEmailsFound')}
                          </PopperStaticText>
                        ) : (
                          <MenuList
                            autoFocusItem={open}
                            id="composition-menu"
                            style={{
                              maxHeight: '270px',
                              width: '100%',
                              overflowY: 'scroll',
                              padding: 0,
                            }}
                            aria-labelledby="emailFilter"
                            onKeyDown={handleListKeyDown}
                          >
                            {allEmails?.map((e) => {
                              return (
                                <Box>
                                  <MenuItem
                                    style={{
                                      display: 'flex',
                                      flexDirection: 'column',
                                      alignItems: 'flex-start',
                                      padding: '0.5rem',
                                      marginBottom: '0.25rem',
                                    }}
                                    onMouseEnter={(e) =>
                                      (e.target.style.opacity = 0.8)
                                    }
                                    onMouseLeave={(e) =>
                                      (e.target.style.opacity = 1)
                                    }
                                    onClick={() => handleSelect(e)}
                                  >
                                    <div
                                      style={{
                                        fontSize: '14px',
                                        width: '100%',
                                      }}
                                    >{`${e.email}`}</div>
                                  </MenuItem>
                                </Box>
                              );
                            })}
                          </MenuList>
                        )}
                      </ClickAwayListener>
                    </Paper>
                  </Grow>
                )}
              </ResultPopper>
            </>
          )}
        </InputWrapper>
      </Row>
      <HintText>{t('assignCardHint')}</HintText>
      {isErrorVisible && (
        <Row>
          <Error>{tActions(errorMessage)}</Error>
        </Row>
      )}
      {isSendLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper>
          <Button
            disabled={!driverMailInput || driverMailInput === ''}
            onClick={sendData}
            variant="primary"
          >
            {tdirectCustomer('sendInvitation')}
          </Button>
        </ButtonWrapper>
      )}
    </DialogWrapper>
  );
};

DriverAssign.propTypes = {
  open: PropTypes.bool,
  onClose: PropTypes.func,
  cardNumber: PropTypes.string,
};

export default DriverAssign;
