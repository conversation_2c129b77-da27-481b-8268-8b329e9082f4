import { useState, useContext, useEffect } from 'react';
import { Grid } from '@material-ui/core';
import { useLocation } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import qs from 'querystring';
import JwtDecode from 'jwt-decode';

import { Card } from '../Components';
import CircularProgress from '../Components/CircularProgress';
import { userContext } from '../ContextProvider';

import { idpRequest } from '../utils/requester';
import logger from '../utils/logger';

import styled from 'styled-components';

const StyledGrid = styled(Grid)`
  padding: 2rem 0;
  margin-bottom: 5rem;
  justify-content: center;
`;

const ChangeEmail = () => {
  const { logOut } = useContext(userContext);
  const { t } = useTranslation('profile');
  const location = useLocation();
  const [isTokenExpired, setIsTokenExpired] = useState(false);
  const [loading, setLoading] = useState(true);

  const checkIfTokenIsExpired = (keyToCheck) => {
    const isExpired = Number(keyToCheck.exp) * 1000 < Date.parse(new Date());
    setIsTokenExpired(isExpired);
    return isExpired;
  };

  useEffect(() => {
    const changeMailRequest = async (key) => {
      try {
        await idpRequest(false).get(`/login-actions/action-token?key=${key}`);
        setLoading(false);
      } catch (error) {
        setIsTokenExpired(true);
        setLoading(false);
        console.error(error);
      }
    };
    try {
      const rawQueryString = location.search.slice(1);
      const parsedQueryString = qs.parse(rawQueryString);
      const decodedKey = JwtDecode(parsedQueryString.key);
      if (!checkIfTokenIsExpired(decodedKey)) {
        changeMailRequest(parsedQueryString.key);
      }
    } catch (err) {
      setIsTokenExpired(true);
      setLoading(false);
      logger(true).error(`JWT Token is not valid`);
    }
  }, [location]);

  useEffect(() => {
    if (!loading && !isTokenExpired) {
      setTimeout(() => {
        logOut();
      }, 3000);
    }
  }, [loading, isTokenExpired, location, logOut]);

  return (
    <Grid justifyContent="center" container>
      <StyledGrid item xs={10} lg={8}>
        <Card>
          {(loading && <CircularProgress />) ||
            (isTokenExpired && t('changeEmailExpired')) ||
            t('changeEmailSuccess')}
        </Card>
      </StyledGrid>
    </Grid>
  );
};

export default ChangeEmail;
