import { useEffect, useState, useMemo } from 'react';
import PageWrapper from '../Components/PageWrapper';
import { Headline, Hint, Row } from '../Components/evDriver/PageTemplate';
import { Button, Checkbox, Error, TextField } from '../Components';
import { useTranslation } from 'react-i18next';
import LanguageSelection from '../Components/LanguageSelection';
import requester from '../utils/requester';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { CircularProgress } from '@material-ui/core';
import { mailRegex } from '../utils/helper';
import SuccessMessage from '../Components/SuccessMessage';

const Spacer = styled.div`
  width: 100%;
  height: 20px;
`;

const LanguageTitle = styled.p`
  margin-top: 0;
  padding-top: 0;
  margin-bottom: 8px;
`;

const TextCheckbox = styled.p`
  line-height: 24px;
  margin: 0;
  padding-left: 15px;
  cursor: pointer;
`;

const DataHeadline = styled.div`
  font-size: 16px;
  text-align: left;
  color: black;
  margin: 2rem 0 1rem 0;
  line-height: 20px;
  font-weight: bold;
  white-space: pre-line;
`;

const IndirectDriverOnboarding = () => {
  const { t } = useTranslation('onboarding');
  const { t: tEvDriver } = useTranslation('evDriver');
  const { t: tActions } = useTranslation('actions');

  const urlSearchString = window.location.search;

  const params = useMemo(
    () => new URLSearchParams(urlSearchString),
    [urlSearchString],
  );

  const [isLoading, setIsLoading] = useState(false);
  const [submitError, setSubmitError] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [isAppUser, setIsAppUser] = useState(false);
  // RE-3918, Request sometimges runs into gateway timeout but still successfully creates user.
  const [showPossibleSuccess, setShowPossibleSuccess] = useState(false);
  const [formState, setFormState] = useState({
    firstName: {
      value: '',
      error: false,
    },
    lastName: {
      value: '',
      error: false,
    },
    language: {
      value: '',
      error: false,
    },
    emailAdress: {
      value: '',
      error: false,
    },
    licensePlate: {
      value: '',
      error: false,
    },
    fleetManagerEmail: {
      value: '',
      error: false,
    },
    cardNumber: {
      value: params.get('cardNumber'),
      error: false,
    },
    dataConfirmation: false,
  });
  const navigate = useNavigate();

  useEffect(() => {
    //make sure the given card is valid and prevent faulty onboardings
    const checkCardNumber = async () => {
      try {
        const rsp = await requester().get(
          `/cards/${params.get(
            'cardNumber',
          )}/availability/?expiry_date=${params.get('expiryDate')}`,
        );
        const { state, type, email } = rsp.data;
        if (!state || state === 'error') {
          navigate('/Error');
          return;
        }
        if (type !== 'indirect') {
          navigate('/Error');
        }
        if (email) {
          setIsAppUser(true);
        }
      } catch (err) {
        console.error(err);
        navigate('/Error');
      }
    };
    if (
      params.get('cardNumber') &&
      params.get('cardNumber').length === 18 &&
      params.get('expiryDate')
    ) {
      checkCardNumber();
    } else {
      navigate('/Error');
    }
  }, [params, navigate]);

  const changeLanguage = (lang) => {
    setFormState({
      ...formState,
      language: {
        ...formState.language,
        value: lang,
      },
    });
  };

  const register = async () => {
    setSubmitError(false);
    let valid = true;
    const tempFormState = JSON.parse(JSON.stringify(formState));
    if (!tempFormState.emailAdress.value.match(mailRegex)) {
      valid = false;
      Object.assign(tempFormState, {
        ...tempFormState,
        emailAdress: {
          ...tempFormState.emailAdress,
          error: true,
        },
      });
    }

    if (!valid) {
      setFormState(tempFormState);
      return;
    }
    try {
      setIsLoading(true);
      await requester()({
        method: 'put',
        url: '/driver_management',
        data: {
          first_name: formState.firstName.value,
          last_name: formState.lastName.value,
          email: formState.emailAdress.value.trim(),
          card_number: params.get('cardNumber'),
          expiry_date: params.get('expiryDate'),
          language: formState.language.value,
          fleetmanager_email: formState.fleetManagerEmail.value.trim(),
          data_correctness_confirmed: true,
          licence_plate: formState.licensePlate.value,
        },
      });
      setSubmitError(false);
      setIsLoading(false);
      setShowSuccess(true);
    } catch (error) {
      if (error.response.status === 504) {
        setSubmitError(false);
        setIsLoading(false);
        setShowPossibleSuccess(true);
      } else {
        setSubmitError(true);
        setIsLoading(false);
      }
    }
  };

  return (
    <>
      {showSuccess ? (
        <SuccessMessage message={t('driverOnboardingSucess')} />
      ) : showPossibleSuccess ? (
        <SuccessMessage
          alternateIcon={true}
          message={t('driverOnboardingTimeoutSuccess')}
        />
      ) : (
        <PageWrapper centered title={t('signUp')}>
          <Headline>{t('indirectDriverHeadline')}</Headline>

          <TextField
            value={formState.firstName.value}
            error={formState.firstName.error}
            type="text"
            fullWidth
            newDriver
            autoFocus
            label={t('firstName')}
            placeholder={t('firstNamePlaceholder')}
            onFocus={() =>
              setFormState({
                ...formState,
                firstName: {
                  ...formState.firstName,
                  error: false,
                },
              })
            }
            onChange={(e) => {
              setFormState({
                ...formState,
                firstName: {
                  ...formState.firstName,
                  value: e.target.value,
                },
              });
            }}
          />
          <Spacer />
          <TextField
            value={formState.lastName.value}
            error={formState.lastName.error}
            type="text"
            fullWidth
            newDriver
            autoFocus
            label={t('lastName')}
            placeholder={t('lastNamePlaceholder')}
            onFocus={() =>
              setFormState({
                ...formState,
                lastName: {
                  ...formState.lastName,
                  error: false,
                },
              })
            }
            onChange={(e) => {
              setFormState({
                ...formState,
                lastName: {
                  ...formState.lastName,
                  value: e.target.value,
                },
              });
            }}
          />
          <Spacer />
          <TextField
            value={formState.emailAdress.value}
            error={formState.emailAdress.error}
            type="text"
            fullWidth
            newDriver
            autoFocus
            label={t('mail')}
            placeholder={t('mailPlaceholder')}
            onFocus={() =>
              setFormState({
                ...formState,
                emailAdress: {
                  ...formState.emailAdress,
                  error: false,
                },
              })
            }
            onChange={(e) => {
              setFormState({
                ...formState,
                emailAdress: {
                  ...formState.emailAdress,
                  value: e.target.value,
                },
              });
            }}
          />
          {isAppUser && <Hint>{t('emailHint')}</Hint>}

          <Spacer />

          <TextField
            value={formState.licensePlate.value}
            error={formState.licensePlate.error}
            type="text"
            fullWidth
            newDriver
            autoFocus
            label={tEvDriver('licencePlate') + ' ' + t('optional')}
            placeholder={tEvDriver('licencePlatePlaceholder')}
            onFocus={() =>
              setFormState({
                ...formState,
                licensePlate: {
                  ...formState.licensePlate,
                  error: false,
                },
              })
            }
            onChange={(e) => {
              setFormState({
                ...formState,
                licensePlate: {
                  ...formState.licensePlate,
                  value: e.target.value,
                },
              });
            }}
          />
          <Spacer />
          <LanguageTitle>{t('language')}</LanguageTitle>
          <LanguageSelection
            language={formState.language.value}
            changeLanguage={changeLanguage}
          />
          <Spacer />
          <TextField
            value={formState.fleetManagerEmail.value}
            error={formState.fleetManagerEmail.error}
            type="text"
            fullWidth
            newDriver
            autoFocus
            tooltip={{
              content: t('fleetmanagerTooltip'),
              placement: 'top',
              type: 'indirectDriverOnboardingFleetManager',
            }}
            label={t('fleetManagerEmail')}
            placeholder={t('fleetManagerEmailPlaceholder')}
            onFocus={() =>
              setFormState({
                ...formState,
                fleetManagerEmail: {
                  ...formState.fleetManagerEmail,
                  error: false,
                },
              })
            }
            onChange={(e) =>
              setFormState({
                ...formState,
                fleetManagerEmail: {
                  ...formState.fleetManagerEmail,
                  value: e.target.value,
                },
              })
            }
          />
          <DataHeadline>{t('dataProtectionNotice')}</DataHeadline>
          <Row>
            <Checkbox
              id="dataCheckBox"
              checked={formState.dataConfirmation}
              onChange={() =>
                setFormState({
                  ...formState,

                  dataConfirmation: !formState.dataConfirmation,
                })
              }
            />
            <TextCheckbox
              onClick={() =>
                setFormState({
                  ...formState,
                  dataConfirmation: !formState.dataConfirmation,
                })
              }
            >
              {t('dataCheckbox')}
            </TextCheckbox>
          </Row>

          <Error visible={submitError} text={tActions('generalRequestError')} />

          {(isLoading && <CircularProgress />) || (
            <Button
              variant="primary"
              disabled={
                !formState.firstName.value.length > 0 ||
                !formState.lastName.value.length > 0 ||
                !formState.emailAdress.value.length > 0 ||
                !formState.language.value.length > 0 ||
                !formState.fleetManagerEmail.value.length > 0 ||
                !formState.dataConfirmation
              }
              onClick={register}
            >
              {t('submit')}
            </Button>
          )}
        </PageWrapper>
      )}
    </>
  );
};

export default IndirectDriverOnboarding;
