import { useEffect, useState, useContext } from 'react';
import { makeStyles } from '@material-ui/styles';
import requester from '../utils/requester';
import logger from '../utils/logger';
import { userContext } from '../ContextProvider';
import { Button } from '../Components';
import PdfRender from '../Components/PdfRenderer';
import { getBranding } from '../utils/helper';
import { Description, InputWrapper } from '../Components/evDriver/PageTemplate';
import Select from '../Components/Select';
import MenuItem from '../Components/MenuItem';
import CountryFlag from '../Components/evDriver/CountryFlag';
import { useTranslation } from 'react-i18next';
import i18next from '../i18n';
import PageWrapper from '../Components/PageWrapper';

const DataPrivacyStatement = () => {
  const useStyles = makeStyles({
    dataPrivacy: {
      '& p': {
        fontSize: '12px',
      },
      '& ul': {
        color: 'var(--default-text)',
        fontSize: '12px',
      },
      '& a': {
        color: 'var(--trafineo-rot-100)',
        fontSize: '12px',
      },
      '& h3': {
        fontWeight: 'bold',
      },
    },
    langSelect: {
      marginTop: '15px',
    },
    downloadWrapper: {
      position: 'absolute',
      textAlign: 'center',
      width: '175px',
      top: '15px',
      right: 0,
      '& a': {
        color: 'var(--default-text)',
        fontSize: '12px',
        textDecoration: 'none',
      },
    },
  });
  const classes = useStyles();
  const tncMode =
    window.location.pathname.indexOf('/terms-and-conditions') === 0;

  const { lang } = useContext(userContext || i18next.language);
  const { t } = useTranslation('footer');
  const [content, setContent] = useState('');
  const tLocalization = useTranslation('localization').t;
  const [country, setCountry] = useState(null);

  useEffect(() => {
    const getData = async () => {
      try {
        const { data } = await requester().get('/Policies/Get_Policies', {
          params: {
            type: tncMode
              ? 'TC'
              : getBranding() === 'trafineo'
              ? 'DPS_Indirect'
              : 'DPS',
            language: lang,
            country_code: tncMode && getBranding() === 'bp' ? country : 'de',
          },
          headers: {
            role: tncMode ? 'FleetManager_direct' : 'Driver',
          },
        });
        setContent(data.message);
      } catch (err) {
        logger(true).error(err.message);
      }
    };
    getData();
  }, [lang, tncMode, country]);

  return (
    <PageWrapper
      backButton
      title={tncMode ? t('terms-and-conditions') : t('data-privacy-statement')}
    >
      {tncMode && getBranding() === 'bp' && (
        <div className={classes.langSelect}>
          <InputWrapper>
            <Description>{tLocalization('chooseCountry')}</Description>
            <Select
              autoFocus
              big
              value={country}
              onChange={(e) => {
                setCountry(e.target.value);
              }}
            >
              {Object.values(['nl', 'at']).map((entry) => (
                <MenuItem key={entry} value={entry} data-cy={`${entry}Select`}>
                  <CountryFlag country={entry} />
                  {tLocalization(`${entry}CountryName`)}
                </MenuItem>
              ))}
            </Select>
          </InputWrapper>
        </div>
      )}
      {((tncMode && getBranding() === 'bp' && country !== null) ||
        (tncMode && getBranding() !== 'bp') ||
        !tncMode) && (
        <div>
          <PdfRender src={tncMode ? content : `data-privacy-${lang}.pdf`} />
        </div>
      )}

      {content !== '' && (
        <div className={classes.wrapper}>
          <div className={classes.downloadWrapper}>
            <a href={`./${content}`} download>
              <Button>Download Pdf</Button>
            </a>
          </div>
        </div>
      )}
    </PageWrapper>
  );
};

export default DataPrivacyStatement;
