import { useEffect, useState, useMemo } from 'react';

import { useTranslation } from 'react-i18next';
import PageWrapper from '../Components/PageWrapper';
import { Row } from '../Components/evDriver/PageTemplate';
import { Switch } from '@material-ui/core';
import styled from 'styled-components';
import { Button } from '../Components';

import Cookies from 'universal-cookie';
import TagManager from 'react-gtm-module';
import { getCookieConsentValue } from 'react-cookie-consent';

import { useNavigate } from 'react-router-dom';
import { useLocation } from 'react-router-dom';

export const MainHeadline = styled.h2`
  font-weight: bold;
`;

export const Headline = styled.h3`
  font-weight: bold;
  margin-bottom: 15px;
  width: 80%;
`;

export const StyledSwitch = styled(Switch)`
  .MuiSwitch-colorSecondary.Mui-checked {
    color: var(--primary) !important;
  }
  .MuiSwitch-colorSecondary.Mui-checked + .MuiSwitch-track {
    background-color: var(--secoundary) !important;
  }
`;

export const SwitchWrapper = styled.div`
  position: absolute;
  right: 2rem;
`;

export const Spacer = styled.div`
  height: 20px;
`;

export const ButtonWrapper = styled.div`
  position: absolute;
  right: 2rem;
  width: 120px;
  height: 40px;
`;

const CookieSettings = () => {
  const { t } = useTranslation('cookie');
  const { t: tDriver } = useTranslation('evDriver');

  const [checked, setChecked] = useState(true);

  const cookies = useMemo(() => new Cookies(), []);
  const navigate = useNavigate();
  const location = useLocation();

  const handleChange = () => {
    setChecked(!checked);
  };

  const handleAcceptCookie = () => {
    TagManager.dataLayer({
      dataLayer: {
        event: 'consent_save',
        consent: {
          interactionPhase:
            getCookieConsentValue('reimbursementCookie') !== undefined
              ? 'update'
              : 'initial',
          interactionType: 'accept all',
          status: {
            essential: true,
            performance: true,
          },
        },
      },
    });
    cookies.set('reimbursementCookie', true);
  };

  useEffect(() => {
    if (cookies.get('reimbursementCookie') === undefined) {
      setChecked(true);
    } else {
      setChecked(cookies.get('reimbursementCookie'));
    }
  }, [cookies]);

  const savePreferences = () => {
    if (checked) {
      handleAcceptCookie();
    } else {
      TagManager.dataLayer({
        dataLayer: {
          event: 'consent_save',
          consent: {
            interactionPhase:
              getCookieConsentValue('reimbursementCookie') !== undefined
                ? 'update'
                : 'initial',
            interactionType: 'decline all',
            status: {
              essential: true,
              performance: false,
            },
          },
        },
      });
      cookies.set('reimbursementCookie', false);
    }
    if (location.state?.prevPathname === '/home-charging-austria') {
      navigate('/home-charging-austria');
    } else if (location.state?.prevPathname === '/changePassword') {
      navigate(`/changePassword${location.state.key}`);
    } else {
      navigate('/login');
    }
  };

  return (
    <PageWrapper>
      <p>{t('intro')}</p>
      <MainHeadline>{t('headline')}</MainHeadline>
      <Row>
        <Headline>{t('functionalCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <Switch
            checked={true}
            disabled
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{t('functionalCookiesText')}</p>
      <Spacer />
      <Row>
        <Headline>{t('performanceCookiesHeadline')}</Headline>
        <SwitchWrapper>
          <StyledSwitch
            checked={checked}
            onChange={handleChange}
            inputProps={{ 'aria-label': 'controlled' }}
          />
        </SwitchWrapper>
      </Row>
      <p>{t('performanceCookiesText')}</p>
      <Spacer />
      <ButtonWrapper>
        <Button variant="primary" onClick={savePreferences}>
          {tDriver('save')}
        </Button>
      </ButtonWrapper>
      <Spacer />
      <Spacer />
    </PageWrapper>
  );
};

export default CookieSettings;
