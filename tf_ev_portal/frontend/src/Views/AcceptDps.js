import { useState } from 'react';
import { Grid, Box } from '@material-ui/core';
import { makeStyles } from '@material-ui/styles';
import { useTranslation, Trans } from 'react-i18next';
import { Card, Button, Checkbox } from '../Components';
import StaticDialog from '../Components/changePassword/StaticDialog';
import requester from '../utils/requester';

const useStyles = makeStyles({
  link: {
    border: 'none',
    background: 'transparent',
    padding: 0,
    color: 'var(--trafineo-rot-100)',
    fontSize: 12,
    '&:hover': {
      textDecoration: 'underline',
      cursor: 'pointer',
    },
    outline: 'none !important',
  },
  main: {
    padding: '2rem 0',
    marginBottom: '5rem',
  },
  viewHeader: {
    textAlign: 'left',
  },
  buttonGrid: {
    marginTop: '2rem',
  },
  errorText: {
    color: 'var(--trafineo-rot-100)',
    paddingLeft: '1rem',
  },
  termsContainer: {
    display: 'flex',
    flexDirection: 'column',
    border: 'solid 1px var(--trafineo-grau-70)',
    backgroundColor: 'var(--trafineo-grau-20)',
    padding: '1rem',
    marginTop: '2rem',
  },
  termsRow: {
    textAlign: 'left',
    display: 'flex',
    alignItems: 'center',
    '& p': {
      paddingLeft: '0.5rem',
    },
    '& a': {
      color: 'var(--trafineo-rot-100)',
    },
  },
  selectWrapper: {
    alignItems: 'center',
    display: 'flex',
    justifyContent: 'flex-start',
  },
  selectLabel: {
    fontSize: '19px',
    lineHeight: '22px',
    fontWeight: 'bold',
    marginRight: '1rem',
  },
});

const ChangePassword = () => {
  const classes = useStyles();
  const { t } = useTranslation('managePassword');
  const tActions = useTranslation('actions').t;

  // checkboxes for Terms and Private Policy
  const [policy, setPolicy] = useState(false);

  const [isPolicyDialogVisible, setIsPolicyDialogVisible] = useState(false);
  const [isInfoDialogVisible, setIsInfoDialogVisible] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const { REACT_APP_KEYCLOAK_DRIVER_ROLE } = process.env;

  const saveAcceptenceToBackend = async () => {
    if (policy) {
      try {
        await requester().post(`/Policies/Post_PoliciesAccepted`, {});
        setErrorMessage(null);
        window.location.reload();
      } catch (e) {
        setErrorMessage(tActions('generalRequestError'));
      }
    } else {
      setErrorMessage(t('checkTCandPolicy'));
    }
  };

  return (
    <Grid container>
      <>
        <StaticDialog
          open={isPolicyDialogVisible}
          role={REACT_APP_KEYCLOAK_DRIVER_ROLE}
          onClose={() => setIsPolicyDialogVisible(false)}
        />
        <StaticDialog
          open={isInfoDialogVisible}
          type="DPH"
          role={REACT_APP_KEYCLOAK_DRIVER_ROLE}
          onClose={() => setIsInfoDialogVisible(false)}
        />
      </>
      <Grid item xs={1} lg={2} />
      <Grid className={classes.main} item xs={10} lg={8}>
        <h1 data-cy="changeText" className={classes.viewHeader}>
          {t('acceptDpsHeadline')}
        </h1>
        <Card>
          <Grid container spacing={1}>
            <GridRow>
              <Box className={classes.termsContainer}>
                <Box className={classes.termsRow}>
                  <Checkbox
                    checked={policy}
                    onChange={() => setPolicy(!policy)}
                  />
                  <p>
                    <Trans t={t} i18nKey="privacyPolicy">
                      I hereby confirm the acceptance of the
                      <button
                        className={classes.link}
                        type="button"
                        onClick={() => setIsPolicyDialogVisible(true)}
                      >
                        Private Policy
                      </button>
                      <button
                        className={classes.link}
                        type="button"
                        onClick={() => setIsInfoDialogVisible(true)}
                      >
                        Information
                      </button>
                      in accordance with GDPR
                    </Trans>
                  </p>
                </Box>
              </Box>
            </GridRow>
            <Grid item xs={4} />
            <Grid item xs={4} className={classes.buttonGrid}>
              <Button variant="primary" onClick={saveAcceptenceToBackend}>
                {t('enter')}
              </Button>
              {errorMessage && (
                <p className={classes.errorText}>{errorMessage}</p>
              )}
            </Grid>
            <Grid item xs={4} />
          </Grid>
        </Card>
      </Grid>
      <Grid item xs={1} lg={2} />
    </Grid>
  );
};

const GridRow = ({ children }) => {
  return (
    <>
      <Grid item xs={2} xl={3} />
      <Grid item xs={8} xl={6}>
        {children}
      </Grid>
      <Grid item xs={2} xl={3} />
    </>
  );
};

export default ChangePassword;
