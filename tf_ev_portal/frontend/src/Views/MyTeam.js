import { useCallback, useContext, useEffect, useState } from 'react';
import styled from 'styled-components';
import { useTranslation } from 'react-i18next';
import { Button } from '../Components';
import AddFleetManagerDialog from '../Components/myTeam/AddFleetManagerDialog';
import AddSalesAdminDialog from '../Components/myTeam/AddSalesAdminDialog';
import requester, { serviceRequest } from '../utils/requester';
import CircularProgress from '../Components/CircularProgress';
import { userContext } from '../ContextProvider';
import jwtDecode from 'jwt-decode';
import FleetManagerActionsMenu from '../Components/myTeam/FleetmanagerActionsMenu';
import PageWrapper from '../Components/PageWrapper';
import AddSupportAdminDialog from '../Components/myTeam/AddSupportAdminDialog';

export const ButtonWrapper = styled.div`
  width: 200px;
  margin-bottom: 1rem;
`;
export const Status = styled.div`
  color: #7a7a7a;
  margin-top: 0.5rem;
`;
export const RoleHeadline = styled.div`
  color: black;
  font-weight: bold;
  margin-top: 2rem;
`;
export const FleetManagersList = styled.div`
  width: 600px;
  display: flex;
  flex-direction: column;
  padding: 1rem 0;
`;
export const FleetManagerWrapper = styled.div`
  opacity: ${(props) => (props.greydOut === true ? '0.4' : '1')};
  display: flex;
  position: relative;
  flex-direction: column;
  background-color: #f2f2f2;
  font-size: 14px;
  :nth-child(even) {
    background-color: #fbfafa;
  }
  padding: 1rem;
`;

export const LoadingWrapper = styled.div`
  display: flex;
  width: 600px;
  margin-top: 6rem;
  justify-content: center;
`;

export const PageTitle = styled.h1`
  font-size: 33px;
  color: black;
  font-weight: bold;
`;

const MenuWrapper = styled.div`
  display: flex;
  position: absolute;
  right: 12px;
  top: 12px;
`;

const MyTeam = () => {
  const { t } = useTranslation('serviceProvider');
  const { user } = useContext(userContext);
  const [isFleetManagerDialogVisible, setIsFleetManagerDialogVisible] =
    useState(false);
  const [isSalesAdminDialogVisible, setIsSalesAdminDialogVisible] =
    useState(false);
  const [isSupportAdminDialogVisible, setIsSupportAdminDialogVisible] =
    useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [fleetManagers, setFleetmanagers] = useState([]);
  const [userFunction, setUserFunction] = useState(null);
  const [userMail, setUserMail] = useState(null);

  const FleetManagerItem = ({
    fmFunction,
    status,
    email,
    disableActions,
    idpId,
  }) => {
    return (
      <FleetManagerWrapper greydOut={status === 'inactive'}>
        <div>{email}</div>
        <Status>{t(`${status}Status`)}</Status>
        {!disableActions && userFunction === 'admin' && (
          <MenuWrapper>
            <FleetManagerActionsMenu
              idpId={idpId}
              email={email}
              fmFunction={fmFunction}
              status={status}
              onClose={reFetchData}
              salesAdmin={
                jwtDecode(user.access_token).role.toLowerCase() === 'sales'
              }
              support={
                jwtDecode(user.access_token).role.toLowerCase() === 'support'
              }
            />
          </MenuWrapper>
        )}
      </FleetManagerWrapper>
    );
  };

  const getData = useCallback(async () => {
    setIsLoading(true);
    let rsp = null;
    try {
      rsp = await (await requester().get(`/fm_management`)).data.fleetmanagers;
      setIsLoading(false);
      return rsp;
    } catch (err) {
      setIsLoading(false);
      return undefined;
    }
  }, []);

  const getSupportData = async () => {
    setIsLoading(true);
    try {
      const rsp = await serviceRequest().get('users/v1/users/', {});
      setIsLoading(false);
      return rsp.data;
    } catch (err) {
      setIsLoading(false);
      return undefined;
    }
  };

  const reFetchData = async () => {
    const rspData =
      jwtDecode(user.access_token).role.toLowerCase() === 'sales' ||
      jwtDecode(user.access_token).role.toLowerCase() === 'support'
        ? await getSupportData().data
        : await getData();
    if (rspData) {
      setFleetmanagers(rspData);
    }
  };

  useEffect(() => {
    const fetchData = async (role) => {
      let rspData;
      if (role === 'sales' || role === 'support') {
        rspData = await getSupportData();
      } else {
        rspData = await getData();
      }
      if (rspData) {
        setFleetmanagers(
          role === 'sales' || role === 'support' ? rspData.data : rspData,
        );
      }
    };
    try {
      const decodedUser = jwtDecode(user.access_token);
      setUserMail(decodedUser.preferred_username);
      setUserFunction(decodedUser.function);
      fetchData(decodedUser.role.toLowerCase());
    } catch (e) {
      console.error(e);
    }
  }, [getData, user.access_token]);

  const getCurrentSalesStatus = (userstatus) => {
    if (!userstatus || userstatus?.length === 0) {
      return 'inactive';
    }
    const now = new Date();
    let active = 'unknown';
    userstatus.forEach((status) => {
      let start = new Date(status.valid_from);
      let end = new Date(status.valid_to);
      if (start.getTime() < now.getTime() && now.getTime() < end.getTime()) {
        active = status.description;
      }
    });

    return active;
  };

  const statusSortHelper = {
    active: 1,
    invited: 2,
    inactive: 3,
  };

  return (
    <PageWrapper backButton title={t('myTeam')}>
      <AddSupportAdminDialog
        open={isSupportAdminDialogVisible}
        onClose={() => {
          setIsSupportAdminDialogVisible(false);
          reFetchData();
        }}
      ></AddSupportAdminDialog>
      <AddFleetManagerDialog
        open={isFleetManagerDialogVisible}
        onClose={() => {
          setIsFleetManagerDialogVisible(false);
          reFetchData();
        }}
      />
      <AddSalesAdminDialog
        open={isSalesAdminDialogVisible}
        onClose={() => {
          setIsSalesAdminDialogVisible(false);
          reFetchData();
        }}
      />
      {userFunction === 'admin' && (
        <ButtonWrapper>
          <Button
            onClick={() =>
              jwtDecode(user.access_token).role.toLowerCase() === 'sales'
                ? setIsSalesAdminDialogVisible(true)
                : jwtDecode(user.access_token).role.toLowerCase() === 'support'
                ? setIsSupportAdminDialogVisible(true)
                : setIsFleetManagerDialogVisible(true)
            }
            variant="primary"
          >
            {t('sendInvitation')}
          </Button>
        </ButtonWrapper>
      )}

      <RoleHeadline>{t('adminFleetmanagers')}</RoleHeadline>
      <FleetManagersList>
        {isLoading ? (
          <LoadingWrapper>
            <CircularProgress />
          </LoadingWrapper>
        ) : (
          <>
            {userFunction === 'admin' && (
              <FleetManagerItem
                email={`${userMail} ${t('you')}`}
                fmFunction="admin"
                status="active"
                key={userMail}
                disableActions
              ></FleetManagerItem>
            )}
            {fleetManagers
              .filter((e) =>
                e.role
                  ? e.role[0].name.toLowerCase() === 'sales' ||
                    e.role[0].name.toLowerCase() === 'support'
                  : e.function_description === 'admin',
              )
              .sort(
                (a, b) =>
                  statusSortHelper[a.status_description] -
                  statusSortHelper[b.status_description],
              )
              .map((e) => {
                if (e.email !== userMail && e.e_mail !== userMail) {
                  return (
                    <FleetManagerItem
                      email={e.e_mail || e.email}
                      fmFunction={e.email ? 'admin' : e.function_description}
                      status={
                        e.status_description ||
                        getCurrentSalesStatus(e.role[0].userstatus)
                      }
                      key={e.e_mail}
                      idpId={e.user_idp_id}
                    ></FleetManagerItem>
                  );
                } else {
                  return false;
                }
              })}
            {fleetManagers.filter((e) => e.function_description === 'admin')
              .length === 0 &&
              userFunction !== 'admin' && (
                <FleetManagerWrapper>
                  {t('noFleetManagersFound')}
                </FleetManagerWrapper>
              )}
          </>
        )}
      </FleetManagersList>
      {jwtDecode(user.access_token).role.toLowerCase() !== 'sales' &&
        jwtDecode(user.access_token).role.toLowerCase() !== 'support' && (
          <>
            <RoleHeadline>{t('operativeFleetmanagers')}</RoleHeadline>
            <FleetManagersList>
              {isLoading ? (
                <LoadingWrapper>
                  <CircularProgress />
                </LoadingWrapper>
              ) : (
                <>
                  {userFunction === 'operative' && (
                    <FleetManagerItem
                      email={`${userMail} ${t('you')}`}
                      fmFunction="operative"
                      status="active"
                      key={userMail}
                      disableActions
                    ></FleetManagerItem>
                  )}
                  {fleetManagers
                    .filter((e) => e.function_description === 'operative')
                    .sort(
                      (a, b) =>
                        statusSortHelper[a.status_description] -
                        statusSortHelper[b.status_description],
                    )
                    .map((e) => (
                      <FleetManagerItem
                        email={e.e_mail}
                        fmFunction={e.function_description}
                        status={e.status_description}
                        key={e.e_mail}
                      ></FleetManagerItem>
                    ))}
                  {fleetManagers.filter(
                    (e) => e.function_description === 'operative',
                  ).length === 0 &&
                    userFunction !== 'operative' && (
                      <FleetManagerWrapper>
                        {t('noFleetManagersFound')}
                      </FleetManagerWrapper>
                    )}
                </>
              )}
            </FleetManagersList>
          </>
        )}
    </PageWrapper>
  );
};

export default MyTeam;
