import { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { TextField, Button } from '../Components';
import { idpRequest } from '../utils/requester';
import logger from '../utils/logger';
import Center from '../Components/helper/Center';
import CircularProgress from '../Components/CircularProgress';
import SuccessDialog from '../Components/SuccessDialog';
import { isMobile } from 'react-device-detect';
import PageWrapper from '../Components/PageWrapper';
import {
  ButtonWrapper,
  Error,
  InputWrapper,
  Row,
} from '../Components/evDriver/PageTemplate';
import { mailRegex } from '../utils/helper';

const ForgotPassword = () => {
  const { t } = useTranslation('managePassword');
  const tActions = useTranslation('actions').t;
  const navigate = useNavigate();
  const [email, setEmail] = useState('');
  const [isNotCorrect, setIsNotCorrect] = useState(false);
  const [error, setError] = useState(false);
  const [isLoading, setILoading] = useState(false);
  const [isStatusDialogVisible, setIsStatusDialogVisible] = useState(false);

  const validateMail = () => {
    if (email.match(mailRegex)) {
      return true;
    }
    return false;
  };

  const onPwReset = async () => {
    try {
      if (validateMail()) {
        setError(false);
        setIsNotCorrect(false);
        setILoading(true);
        await idpRequest(true).get(`/login-actions/reset-credentials`, {
          headers: {
            email,
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        });
        setILoading(false);
        setIsStatusDialogVisible(true);
      } else {
        setError(false);
        setIsNotCorrect(true);
      }
    } catch (err) {
      setILoading(false);
      logger().error(err.message);
      setError(true);
    }
  };

  const loginOnEnter = (e) => {
    // login on Enter press
    if (e.keyCode === 13) {
      onPwReset();
    }
  };

  return (
    <PageWrapper
      isMobile={isMobile}
      card
      title={t('setNewPassword')}
      description={t('forgotDesc')}
    >
      <SuccessDialog
        isVisible={isStatusDialogVisible}
        onClose={() => navigate('/login')}
        text={t('passwordResetSuccess')}
      />
      <Row>
        <InputWrapper fullWidth>
          <TextField
            data-cy="driverMail"
            newDriver
            autoFocus
            onChange={(e) => {
              setEmail(e.target.value);
            }}
            value={email}
            error={isNotCorrect}
            type="text"
            label={t('email')}
            onKeyDown={loginOnEnter}
          />
          {isNotCorrect && <Error>{t('emailValidationError')}</Error>}
        </InputWrapper>
      </Row>
      {error && (
        <Row>
          <Error>{tActions('generalRequestError')}</Error>
        </Row>
      )}
      {isLoading ? (
        <Center>
          <CircularProgress />
        </Center>
      ) : (
        <ButtonWrapper isMobile={isMobile}>
          <Button disabled={!email} variant="primary" onClick={onPwReset}>
            {t('send')}
          </Button>
        </ButtonWrapper>
      )}
    </PageWrapper>
  );
};

export default ForgotPassword;
