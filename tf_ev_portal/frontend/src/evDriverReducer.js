import { db } from './db';

const evDriverReducer = (state, action) => {
  switch (action.type) {
    case 'clear':
      return null;
    case 'set':
      // if last_modified is undefined or ev driver data response isn't modified the data won't be updated
      if (
        state &&
        action.value.last_modified &&
        state.lastModified === action.value.last_modified
      ) {
        return state;
      }
      db.delete();
      return {
        idpId: action.value.ev_driver_idp_id,
        cards: action.value.cards,
        dpsAccepted: action.value.DPS_accepted,
        principle: action.value.principle,
        language: {
          new: action.value.language,
          old: action.value.language,
        },
        firstname: action.value.firstname,
        lastname: action.value.lastname,
        lastModified: action.value.last_modified,
        reimbursementStatus: action.value.reimbursement_status_id,
        approvalStatus: [
          ...(action.value.driver_approval_status?.map(
            ({ driver_approval_status_name: status }) => status,
          ) || []),
        ],
        availableTariffSchemes: action.value.available_tariff_schemes,
        vehicle:
          action.value.vehicles && action.value.vehicles.length > 0
            ? {
                id: action.value.vehicles[0].id,
                Vehicle_Make: action.value.vehicles[0].Vehicle_Make,
                Vehicle_Model: action.value.vehicles[0].Vehicle_Model,
                licencePlate: action.value.vehicles[0].licence_plate,
                Vehicle_Model_Version:
                  action.value.vehicles[0].Vehicle_Model_Version,
                Battery_Capacity_Full:
                  action.value.vehicles[0].Battery_Capacity_Full,
                Drivetrain_Type: action.value.vehicles[0].Drivetrain_Type,
                Availability_Date_From: action.value.vehicles[0].Availability_Date_From,
                Availability_Date_To: action.value.vehicles[0].Availability_Date_To,
              }
            : {
                id: null,
                Vehicle_Make: null,
                Vehicle_Model: null,
                licencePlate: null,
                Vehicle_Model_Version: null,
                Battery_Capacity_Full: null,
                Drivetrain_Type: null,
              },
        wallbox:
          action.value.wallboxes && action.value.wallboxes.length > 0
            ? {
                evseId: action.value.wallboxes[0].evse_id,
                locationOfWallbox:
                  action.value.wallboxes[0].location_of_wallbox,
                ownerOfWallbox: action.value.wallboxes[0].owner_of_wallbox,
                address: {
                  street: action.value.wallboxes[0].address?.street,
                  number: action.value.wallboxes[0].address?.number,
                  city: action.value.wallboxes[0].address?.city,
                  postcode: action.value.wallboxes[0].address?.postcode,
                  state: action.value.wallboxes[0].address?.state,
                  country: action.value.wallboxes[0].address?.country,
                  additional_information:
                    action.value.wallboxes[0].address?.additional_information,
                },
              }
            : {
                evseId: null,
                locationOfWallbox: null,
                ownerOfWallbox: null,
                address: {
                  street: null,
                  number: null,
                  city: null,
                  postcode: null,
                  state: null,
                  country: null,
                  additional_information: null,
                },
              },
        country: action.value.wallbox?.country_code?.toLowerCase(),
        evseId: action.value.wallbox?.evse_id,
        tariffs: action.value.tariffs,
        pending_tariff: action.value.pending_tariff,
        bankData: {
          iban: {
            new: action.value.bank_data?.last_four_iban_digits,
            old: action.value.bank_data?.last_four_iban_digits,
          },
          validFrom: {
            new: action.value.bank_data?.valid_from,
            old: action.value.bank_data?.valid_from,
          },
        },
        trainings: action.value.trainings,
      };
    case 'approvalStatus':
      return {
        ...state,
        approvalStatus: [action.value.approvalStatus],
      };
    case 'tariffType':
      return {
        ...state,
        tariffType: action.value.tariffType,
      };
    case 'wallbox':
      return {
        ...state,

        firstname: action.value.firstname,
        lastname: action.value.lastname,
        wallbox: {
          evseId: action.value.evseId,
          address: {
            ...action.value.address,
          },
          country: action.value.country,
        },
        tariffType: action.value.tariffType
          ? action.value.tariffType
          : state.tariffType,
      };
    case 'pending_tariff':
      return {
        ...state,
        reminder: action.value.reminder,
        pending_tariff: action.value.pending_tariff,
        pending_tariff_completed: action.value.pending_tariff_completed,
      };
    case 'bankData':
      return {
        ...state,
        bankData: {
          iban: {
            ...state.bankData.iban,
            new: action.value.iban,
          },
          validFrom: {
            ...state.bankData.validFrom,
            new: action.value.validFrom,
          },
        },
      };
    case 'language':
      return {
        ...state,
        language: {
          ...state.bankData.iban,
          new: action.value,
        },
      };
    case 'revert':
      return {
        ...state,
        pending_tariff: undefined,
        pending_tariff_completed: undefined,
        tariffType: action.value.tariffType,
      };
    case 'vehicle':
      return {
        ...state,
        licencePlate: action.value.licencePlate,
        vehicle: action.value.vehicle,
      };
    case 'reminder':
      return {
        ...state,
        reminder: action.value.reminder,
      };
    default:
      throw new Error();
  }
};

export default evDriverReducer;
