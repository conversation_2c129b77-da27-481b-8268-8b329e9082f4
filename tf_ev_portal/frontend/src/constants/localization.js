import deLocale from 'date-fns/locale/de';
import enLocale from 'date-fns/locale/en-US';
import nlLocale from 'date-fns/locale/nl';

const countries = {
  de: 'de',
  nl: 'nl',
  at: 'at',
};

const languages = {
  de: {
    key: 'de',
    locale: deLocale,
    placeholder: 'tt.mm.jjjj',
  },
  en: {
    key: 'en',
    locale: enLocale,
    placeholder: 'dd.mm.yyyy',
  },
  nl: {
    key: 'nl',
    locale: nlLocale,
    placeholder: 'dd.mm.jjjj',
  },
};

export { countries, languages };
