body {
  display: none;
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  overflow-y: scroll;
  padding: 0 !important;
}

@keyframes FadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-moz-keyframes FadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-webkit-keyframes FadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-o-keyframes FadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

@-ms-keyframes FadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}
.MuiFormGroup-root {
  text-align: left;
}

:root.trafineo {
  --primary: #c60018;
  --secoundary: #e27e8a;
  --default-text: #414042;
  --trafineo-rot-100: #c60018;
  --trafineo-grau-20: #e7e7e7;
  --trafineo-grau-50: #c4c4c5;
  --trafineo-grau-100: #5a5b5e;
  --trafineo-grau-70: #acadae;
  --trafineo-rot-70: #d64b5c;
  --trafineo-rot-20: #f3cbd0;
  --trafineo-rot-50: #e27e8a;
  --box-shadow: rgba(0, 0, 0, 0.2);
  --font-weight: normal;
  --font-family: Arial, Helvetica, sans-serif;
  --background: white;
  --button-font-weight: normal;
  --button-border-radius: 19px;
  --button-box-shadow: 0 0 16px 0 rgba(198, 0, 24, 0.3);
  --button-height: 45px;
  --header-background: white;
  --header-height: 78px;
  --header-img-height: 60px;
  --header-img-width: 80px;
  --table-height: calc(100vh - 668px);
  --app-height: calc(100vh - 40px);
  --app-height-driver: calc(100vh - 108px);
  --header-img-align: baseline;
  --navbar-background: var(--default-text);
  --headline-color: var(--default-text);
  --error-color: var(--trafineo-rot-100);
  --light-background-text-color: var(--default-text);
  --card-status: var(--trafineo-rot-20);
  --navbar-color: white;
  --fadein-duration: 0.5s;
  --backgroud-gradient: linear-gradient(
    90deg,
    var(--trafineo-rot-100) 51%,
    rgba(255, 255, 255, 1) 52%
  );
  --menu-background: var(--trafineo-rot-100);
}

:root.bp {
  --primary: #009900;
  --secoundary: #9acc00;
  --default-text: #414042;
  --trafineo-rot-100: #9acc00;
  --trafineo-grau-20: #e7e7e7;
  --trafineo-grau-50: #c4c4c5;
  --trafineo-grau-100: #5a5b5e;
  --trafineo-grau-70: #acadae;
  --trafineo-rot-70: #d64b5c;
  --trafineo-rot-20: #99cc00;
  --trafineo-rot-50: #99cc00;
  --box-shadow: rgba(0, 0, 0, 0.2);
  --font-family: 'Roboto', Helvetica, sans-serif;
  --font-weight: 400;
  --background: white;
  --button-font-weight: 700;
  --button-border-radius: 4px;
  --button-box-shadow: 0;
  --button-height: 45px;
  --header-background: white;
  --header-height: 68px;
  --header-img-height: 63px;
  --header-img-width: 43px;
  --table-height: calc(100vh - 701px);
  --app-height: calc(100vh - 40px);
  --app-height-driver: calc(100vh - 108px);
  --header-img-align: center;
  --navbar-background: var(--trafineo-rot-100);
  --headline-color: #99cc00;
  --error-color: #ff6600;
  --light-background-text-color: white;
  --card-status: rgb(255, 102, 0, 0.5);
  --navbar-color: white;
  --backgroud-gradient: linear-gradient(
    90deg,
    rgba(191, 239, 46, 1) 0%,
    rgba(0, 153, 0, 1) 35%,
    rgba(0, 153, 0, 1) 50%,
    rgba(255, 255, 255, 1) 51%
  );
  --menu-background: linear-gradient(
    90deg,
    rgba(191, 239, 46, 1) 0%,
    rgba(0, 153, 0, 1) 100%
  );
  --fadein-duration: 0.5s;
}

:root.aral {
  --default-text: rgb(37, 37, 48);
  --primary: #0064cc;
  --secoundary: #31a2d8;
  --trafineo-rot-100: #0064cc;
  --trafineo-grau-20: #e7e7e7;
  --trafineo-grau-50: #c4c4c5;
  --trafineo-grau-100: #5a5b5e;
  --trafineo-grau-70: #acadae;
  --trafineo-rot-70: #d64b5c;
  --trafineo-rot-20: rgba(0, 100, 204, 0.8);
  --trafineo-rot-50: rgba(0, 100, 204, 0.8);
  --box-shadow: rgba(0, 0, 0, 0.2);
  --font-weight: normal;
  --font-family: Arial, Helvetica, sans-serif;
  --background: white;
  --button-font-weight: 300;
  --button-border-radius: 19px;
  --button-box-shadow: 0;
  --button-height: 45px;
  --header-background: white;
  --header-height: 68px;
  --header-img-height: 64px;
  --header-img-width: 64px;
  --table-height: calc(100vh - 701px);
  --app-height: calc(100vh - 40px);
  --app-height-driver: calc(100vh - 108px);
  --header-img-align: center;
  --navbar-background: #0064cc;
  --headline-color: rgb(37, 37, 48);
  --error-color: #ff7e00;
  --light-background-text-color: white;
  --card-status: rgb(255, 102, 0, 0.5);
  --navbar-color: white;
  --backgroud-gradient: linear-gradient(
    90deg,
    var(--trafineo-rot-100) 51%,
    rgba(255, 255, 255, 1) 52%
  );
  --menu-background: var(--trafineo-rot-100);
  --fadein-duration: 0.5s;
}
