import axios from 'axios';
import createAuthRefreshInterceptor from 'axios-auth-refresh';
import qs from 'querystring';

const getAccessToken = () => {
  try {
    const { access_token } = JSON.parse(
      window.localStorage.getItem('user-token'),
    );
    return access_token;
  } catch (e) {
    return '';
  }
};

const refreshAuthLogic = async (failedRequest) => {
  try {
    const paramsForEnv = {};
    if (process.env.NODE_ENV !== 'production') {
      paramsForEnv.client_id = process.env.REACT_APP_KEYCLOAK_ID;
      paramsForEnv.client_secret = process.env.REACT_APP_KEYCLOAK_SECRET;
    }

    const { refresh_token } = JSON.parse(
      window.localStorage.getItem('user-token'),
    );

    const tokenRefreshResponse = (
      await axios.post(
        `${
          process.env.NODE_ENV !== 'production'
            ? process.env.REACT_APP_KEYCLOAK_DEV_PROXY_URL
            : process.env.REACT_APP_KEYCLOAK_URL
        }${process.env.REACT_APP_KEYCLOAK_TOKEN_URI}`,
        qs.stringify({
          grant_type: 'refresh_token',
          refresh_token,
          ...paramsForEnv,
        }),
        {
          skipAuthRefresh: true,
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        },
      )
    ).data;
    window.localStorage.setItem(
      'user-token',
      JSON.stringify(tokenRefreshResponse),
    );
    failedRequest.response.config.headers.Authorization = `Bearer ' + ${getAccessToken()}`;
    axios.interceptors.request.use((request) => {
      request.headers.Authorization = `Bearer ${getAccessToken()}`;
      return request;
    });
    return Promise.resolve();
  } catch (err) {
    window.localStorage.setItem('user-token', '');
    window.localStorage.setItem('user-is-logged-in', false);
    window.localStorage.setItem('user-token', null);
    window.location.reload();
    return Promise.reject();
  }
};

const devHeaders =
  process.env.NODE_ENV !== 'production'
    ? {
        user_id: 'test-idp-id212387',
        role: 'FleetManager_direct',
      }
    : {};

const createClient = (baseURL, isIdpRequest) => {
  const headers = isIdpRequest ? {} : devHeaders;

  let req = axios.create({
    baseURL,
    headers,
  });
  try {
    // Get from local storage by key

    if (getAccessToken() !== '') {
      req = axios.create({
        baseURL,
        headers: {
          Authorization: `Bearer ${getAccessToken()}`,
          ...headers,
        },
      });
    }
    // eslint-disable-next-line no-empty
  } catch (error) {} // false error
  createAuthRefreshInterceptor(req, refreshAuthLogic);
  // Use interceptor to inject the token to requests
  req.interceptors.request.use((request) => {
    request.headers.Authorization = `Bearer ${getAccessToken()}`;
    return request;
  });

  return req;
};

export const idpRequest = (useDevProxy) => {
  const baseUrl =
    useDevProxy && process.env.NODE_ENV !== 'production'
      ? process.env.REACT_APP_KEYCLOAK_DEV_PROXY_URL
      : process.env.REACT_APP_KEYCLOAK_URL;
  return createClient(baseUrl, true);
};

export const serviceRequest = () => {
  const baseUrl = process.env.REACT_APP_SERVICE_BASEURL;
  return createClient(baseUrl);
};

// eslint-disable-next-line import/no-anonymous-default-export
export default () => createClient(process.env.REACT_APP_API_BASEURL);
