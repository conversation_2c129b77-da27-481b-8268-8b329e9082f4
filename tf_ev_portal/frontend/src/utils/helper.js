/* eslint-disable no-bitwise */
/* eslint-disable one-var */
/* eslint-disable no-plusplus */
/* eslint-disable vars-on-top */
/* eslint-disable object-shorthand */
/* eslint-disable no-var */
import XLSX from 'xlsx';
import approvalStatus from '../constants/approvalStatus';

const atob = (b64) => {
  var chars = {
    ascii: function () {
      return 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
    },
    indices: function () {
      if (!this.cache) {
        this.cache = {};
        var ascii = chars.ascii();

        for (var c = 0; c < ascii.length; c++) {
          var chr = ascii[c];
          this.cache[chr] = c;
        }
      }
      return this.cache;
    },
  };
  var indices = chars.indices(),
    pos = b64.indexOf('='),
    padded = pos > -1,
    len = padded ? pos : b64.length,
    i = -1,
    data = '';

  while (i < len) {
    var code =
      (indices[b64[++i]] << 18) |
      (indices[b64[++i]] << 12) |
      (indices[b64[++i]] << 6) |
      indices[b64[++i]];
    if (code !== 0) {
      data += String.fromCharCode(
        (code >>> 16) & 255,
        (code >>> 8) & 255,
        code & 255,
      );
    }
  }

  if (padded) {
    data = data.slice(0, pos - b64.length);
  }

  return data;
};

const b64toBlob = (dataURI, type) => {
  const byteString = atob(dataURI.split(',')[1]);
  const ab = new ArrayBuffer(byteString.length);
  const ia = new Uint8Array(ab);

  for (let i = 0; i < byteString.length; i += 1) {
    ia[i] = byteString.charCodeAt(i);
  }
  return new Blob([ab], { type });
};

const downloadFile = (fileName, urlData) => {
  if (window.navigator.msSaveOrOpenBlob) {
    const blobObject = b64toBlob(urlData, urlData.split(';')[0].split(':')[1]);
    window.navigator.msSaveOrOpenBlob(blobObject, `${fileName}.pdf`);
  } else {
    const a = document.createElement('a');
    a.href = urlData;
    a.setAttribute('download', fileName);
    a.click();
  }
};

const downloadExcel = (filename, data) => {
  // transform data
  const ws = XLSX.utils.json_to_sheet(data);
  var wb = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(wb, ws, filename);
  XLSX.writeFile(wb, filename);
};

const getInputEventHandlersFromProps = (props) => {
  const inputEventHandlerPropKeys = Object.keys(props).filter(
    (propKey) => propKey.slice(0, 2) === 'on',
  );
  const inputEventProps = {};
  inputEventHandlerPropKeys.forEach((eventKey) => {
    inputEventProps[eventKey] = props[eventKey];
  });
  if (props.value !== undefined) {
    inputEventProps.value = props.value;
  }
  return inputEventProps;
};

const setBranding = () => {
  if (window.location.host.indexOf('bp-reimbursement') === 0) {
    window.localStorage.setItem('branding', 'bp');
  } else if (window.location.host.indexOf('aral-reimbursement') === 0) {
    window.localStorage.setItem('branding', 'aral');
  } else {
    window.localStorage.setItem('branding', 'trafineo');
  }
};

const getBranding = () => {
  return window.localStorage.getItem('branding');
};

const isInDemoMode = () => {
  return process.env.REACT_APP_ENVIRONMENT === 'demo';
};

const isDateValid = (date) => !Number.isNaN(date.getTime());

const isReimbursementActive = (reimbursementStatusId) =>
  reimbursementStatusId === 2;

const getCookieValue = (a) => {
  const b = document.cookie.match(`(^|;)\\s*${a}\\s*=\\s*([^;]+)`);
  return b ? b.pop() : '';
};
const mailRegex =
  /^(([^<>()\]\\.,;:\s@"]+(\.[^<>()\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;

const formatDateTime = (datetime) => {
  if (!datetime) {
    return '-';
  }
  let utcDate = datetime;
  if (!datetime.includes('Z')) {
    utcDate = datetime.concat('Z');
  }

  try {
    return new Intl.DateTimeFormat('de-DE', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
    }).format(new Date(utcDate));
  } catch {
    return '-';
  }
};

const formatDate = (date) => {
  return isDateValid(new Date(date))
    ? new Intl.DateTimeFormat('de-DE', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
      }).format(new Date(date))
    : '-';
};

const getTariffChronology = (startDate, endDate) => {
  if (new Date(startDate) > new Date()) return 'upcomingTariff';
  if (new Date(startDate) <= new Date() && new Date(endDate) > new Date())
    return 'currentTariff';
  return 'expiredTariff';
};

const getUpdateRequested = (evDriverData) => {
  const response = {
    updateRequested: false,
    wallboxUpdateRequested: false,
    bankingUpdateRequested: false,
    tariffUpdateRequested: false,
  };
  if (
    evDriverData.approvalStatus.filter(
      (status) => status.indexOf('update_requested') !== -1,
    ).length > 0
  ) {
    evDriverData.approvalStatus.forEach((status) => {
      if (status === approvalStatus.wallboxUpdateRequested) {
        response.wallboxUpdateRequested = true;
      } else if (status === approvalStatus.bankingUpdateRequested) {
        response.bankingUpdateRequested = true;
      } else if (status === approvalStatus.tariffUpdateRequested) {
        response.tariffUpdateRequested = true;
      }
    });
    response.updateRequested = true;
    return response;
  }
  return response;
};

function secondsToHms(d) {
  d = Number(d);
  var h = Math.floor(d / 3600);
  var m = Math.floor((d % 3600) / 60);
  var s = Math.floor((d % 3600) % 60);

  return `${h !== 0 ? `${h}h` : ''} ${m !== 0 ? `${m}m` : ''} ${s}s`;
}

const prohibitedDomains = [
  'gmx',
  'web',
  'gmail',
  'outlook',
  't-online',
  'magenta',
  'aol',
  'freenet',
  'yahoo',
  'hotmail',
  'icloud',
];

export {
  downloadFile,
  downloadExcel,
  getInputEventHandlersFromProps,
  isDateValid,
  isReimbursementActive,
  setBranding,
  getBranding,
  getCookieValue,
  isInDemoMode,
  getTariffChronology,
  formatDateTime,
  formatDate,
  getUpdateRequested,
  secondsToHms,
  mailRegex,
  prohibitedDomains,
};
