import { useState } from 'react';
import logger from './logger';

export function useLocalStorage(key, initialValue) {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      // Get from local storage by key
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      logger(true).error(error.message);
      return initialValue;
    }
  });

  const setValue = (value) => {
    try {
      const valueToStore =
        value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      // Save to local storage
      window.localStorage.setItem(key, JSON.stringify(valueToStore));
    } catch (error) {
      logger(true).error(error.message);
    }
  };

  return [storedValue, setValue];
}

export function useReducerLocalStorage(
  key,
  reducer,
  initializerArg,
  initializer,
) {
  const [state, setState] = useLocalStorage(
    key,
    initializer?.(initializerArg) || initializerArg,
  );

  const dispatch = (action) => {
    const stateToStore = reducer(state, action);
    setState(stateToStore);
  };

  return [state, dispatch];
}
