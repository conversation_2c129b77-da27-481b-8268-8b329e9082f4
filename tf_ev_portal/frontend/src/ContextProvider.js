import { createContext } from 'react';

const userContext = createContext({});
const dialogContext = createContext(null);
const evDriverContext = createContext(null);
const reminderContext = createContext(null);
const defaultDialogContext = {
  type: null,
  cardNumber: null,
  driverMail: null,
  evseId: null,
  open: false,
  expiryDate: null,
  isSpecialRequest: null,
};

const ContextProvider = ({
  user,
  logOut,
  lang,
  setLang,
  evDriverData,
  evDriverDispatch,
  children,
  dialogData,
  setDialogData,
  reminder,
  setReminder,
  role,
}) => {
  const UserProvider = userContext.Provider;
  const EvDriverDataProvider = evDriverContext.Provider;
  const DialogContextProvider = dialogContext.Provider;
  const ReminderContextProvider = reminderContext.Provider;

  return (
    <ReminderContextProvider value={{ reminder, setReminder }}>
      <UserProvider value={{ user, lang, setLang, logOut, role }}>
        <EvDriverDataProvider value={{ evDriverData, evDriverDispatch }}>
          <DialogContextProvider value={{ dialogData, setDialogData }}>
            {children}
          </DialogContextProvider>
        </EvDriverDataProvider>
      </UserProvider>
    </ReminderContextProvider>
  );
};

export default ContextProvider;
export {
  userContext,
  evDriverContext,
  dialogContext,
  defaultDialogContext,
  reminderContext,
};
