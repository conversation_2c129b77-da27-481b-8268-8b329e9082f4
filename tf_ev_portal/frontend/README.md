# Reimbursement Frontend

This project was bootstrapped with [Create React App](https://github.com/facebook/create-react-app).

## Available Scripts

In the project directory, you can run:

### `yarn start`

Runs the app in the development mode.<br />
The page will reload if you make edits.<br />
You will also see any lint errors in the console.

### `yarn start:mock`

Runs the app in the development mode with the mock server running and answering all requests.<br />
The page will reload if you make edits.<br />
You will also see any lint errors in the console.

### `yarn test`

Launches the test runner in the interactive watch mode.<br />
RUN `yarn test:ci` IN CI ENVIRONMENTS (Pipelines)

### `yarn lint`

Runs eslint to run linting on the code.

### `yarn build`

Builds the app for production to the `build` folder.<br />
It correctly bundles React in production mode and optimizes the build for the best performance.

The build is minified and the filenames include the hashes.<br />
Your app is ready to be deployed!

### yarn sync:lang

Downloads newest versions of translations from POEditor and overwrites the content in the `/lang` folder automatically.

## Development informntion

Test user credentials to log in are:

Fleetmanager (indirect):
Username: <EMAIL>
Password: test

Fleetmanager (direct):
Username: <EMAIL>
Password: test

Leasingmanager:
Username: <EMAIL>
Password: test

Driver:
Username: <EMAIL>
Password: test

## Example Response Object of IDP:

```
{
   "access_token":"eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJHbUJiOFNDTFNKbm50OTVBbHVKeFBIYy1GZUlXZ25RVngweUpWNFlEZk00In0.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Cj7kadscxFiCMmVN-0nxyl9KTRwrmLmusyQxIusdToHxpiA0_T7G0flkgUVHNdOPmhphB4BQxy0QXqLliTSmBAcqV_5MQ41inmHq7DPzlEI_uhJ3W_UgheGakb6w6mwo-LbfSa5MmgKDATg_PlC1JVPrRVLkl4BfRG34UdSqraFLBGfxlisU8_3j6SI47lboqazMtU2SDFlrKQICIGz5ySGQxY-HG_yQ2xb4mvrvGN07O47nT0esnpWRYonIbl4G4m9flYs-\\_9mrQTjXKuwQX7xFxaTFwGaGavIRuQATn46Mw4ibKjKMTfhV0iOqSJk5tse_6MRXu3ZDQH5yBMSviA",
   "expires_in":300,
   "refresh_expires_in":1800,
   "refresh_token":"eyJhbGciOiJIUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICI5MGMxYWU0ZS0xZGE2LTQ5NTktOWQ2MS0xNjdlODJjZWFmOWYifQ.******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.WYEg41fmO6w-wiXvVZZv8MSwlpQUj7cjnQutTJyCFWw",
   "token_type":"bearer",
   "not-before-policy":0,
   "session_state":"43388429-3996-4a89-be45-1ff09e1a92f9",
   "scope":"profile email"
}
```

## SSH for IDP dev

Add the following snippet to your `~/.ssh/config`. This will setup the needed portforwardings.

```
Host reimbursement-dev.trafineo.com
    Port 2222
    User x-integrate
    IdentityFile ~/.ssh/id_rsa
    LocalForward 16060 trafazlx23:8080
    LocalForward 19001 trafazlx22:9001
    LocalForward 19002 trafazlx22:8040
```

Then you can open the tunnel with `ssh reimbursement-dev.trafineo.com -N`. This will open the tunnel and the IDP will be accessable under http://localhost:16060
