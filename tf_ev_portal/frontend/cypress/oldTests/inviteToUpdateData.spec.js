/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Invite to update data', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager_Direct.jwt,
        refresh_token: users.Fleetmanager_Direct.jwt,
      }),
    );
    cy.visit('localhost:3000/driverMail?evseId=12345&cardNumber=23456&driverMail=<EMAIL>');
  });

  it('Given user on page when user looks at the grey information section then he can find the drivers Email address, the card number and the evse-id of the wall box.', () => {
    cy.get('[data-cy=infoBox]').should(
      'contain.text',
      '12345',
    );
    cy.get('[data-cy=infoBox]').should(
      'contain.text',
      '23456',
    );
    cy.get('[data-cy=infoBox]').should(
      'contain.text',
      '<EMAIL>',
    );
  });

  it('Given user opens up the page when user is on page then cursor is focused on the comment field and the send to EV driver button is disabled', () => {
    cy.get('[data-cy=commentField]').should('be.focused');
    cy.get('[data-cy=sendButton]').should('be.disabled');
  });

  it('Given user checked at least one of the checkboxes when send EV Driver button is active then on click the request for change is sent to ESB API', () => {
    cy.get('#wallboxCheckbox').check();
    cy.get('[data-cy=sendButton]').should('be.enabled').click();
    cy.get('[data-cy=successText]').should('be.visible')
  });
});
