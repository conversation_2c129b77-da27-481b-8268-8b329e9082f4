/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('CardTable', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager_Direct.jwt,
        refresh_token: users.Fleetmanager_Direct.jwt,
      }),
    );
    cy.visit('localhost:3000/');
  });

  it('Given initial card overview (no status set) when user looks specific row then the right button has the label "Add F&C card data" if no additional data has been set for the card yet, otherwise if at least one of the values has been changed, the button has the label "Modify F&C card data"', () => {
    cy.get('[data-cy=tableRow]').eq(2).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=sendInvitation]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=sendInvitation]')
      .should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(1).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=addFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(0)
      .find('[data-cy=addFCData]')
      .should('not.be.disabled');
  });

  it('Given the Email has been set in additional card data (no status set) when user was not invited yet then the "Send invitation" button is enabled, otherwise it\'s disabled', () => {
    cy.get('[data-cy=tableRow]')
      .eq(3)
      .find('[data-cy=sendInvitation]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(3)
      .find('[data-cy=sendInvitation]')
      .should('not.be.disabled');
    cy.get('[data-cy=tableRow]').eq(3).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(3)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(3)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver invitation was send (status = invited) when "Waiting for EV driver (hasn´t logged in)" is shown then invitation can be canceled and "Modify F&C card data" is active', () => {
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=waitingDriverNotLoggedIn]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=waitingDriverNotLoggedIn]')
      .should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(4).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=cancelInvitation]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=cancelInvitation]')
      .should('not.be.disabled');
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(4)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver has already logged in the first time (status = logged in once) when "Waiting for EV driver (accepted invitation)" is shown then invitation cannot be canceled and "Modify F&C card data" is active', () => {
    cy.get('[data-cy=tableRow]')
      .eq(5)
      .find('[data-cy=waitingDriverAccepted]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(5)
      .find('[data-cy=waitingDriverAccepted]')
      .should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(5).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(5)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(5)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver has filled in his data (status = initial_data_entered) when user clicks on "Review and approve EV driver data" then data approval page opens up.', () => {
    cy.get('[data-cy=tableRow]')
      .eq(6)
      .find('[data-cy=reviewAndApproveData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(6)
      .find('[data-cy=reviewAndApproveData]')
      .should('not.be.disabled');
    cy.get('[data-cy=tableRow]').eq(6).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(6)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(6)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver data was rejected (status = initial_data_rejected) when "Pending corrections by EV" is shown then only "Modify F&C card data" is active', () => {
    cy.get('[data-cy=tableRow]')
      .eq(7)
      .find('[data-cy=pendingCorrectionsDriver]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(7)
      .find('[data-cy=pendingCorrectionsDriver]')
      .should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(7).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(7)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(7)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver was requested to change his tariff information while(status = tariff_update_requested) when "Request changes from EV" is shown then only "Modify F&C card data" is active', () => {
    cy.get('[data-cy=nextButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=requestedChangesDriver]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=requestedChangesDriver]')
      .should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(1).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given driver has modified his data (status = tariff_modified) when user clicks on "Review and approve data changes" then data approval page opens up.', () => {
    cy.get('[data-cy=nextButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=reviewAndApproveDataChanges]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=reviewAndApproveDataChanges]')
      .should('not.be.disabled');
    cy.get('[data-cy=tableRow]').eq(2).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });

  it('Given fleet manager on card overview page when EV driver approval status is "approved and reimbursement status is active then the "Send data update request" button is active', () => {
    cy.get('[data-cy=nextButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(0)
      .find('[data-cy=updataDataRequest]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(0)
      .find('[data-cy=updataDataRequest]')
      .should('not.be.disabled');
    cy.get('[data-cy=tableRow]').eq(0).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(0)
      .find('[data-cy=modifyFCData]')
      .should('exist');
    cy.get('[data-cy=tableRow]')
      .eq(0)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });
});
