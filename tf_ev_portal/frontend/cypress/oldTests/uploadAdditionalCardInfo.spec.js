/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Upload Additional Card Info', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager_Direct.jwt,
        refresh_token: users.Fleetmanager_Direct.jwt,
      }),
    );
    cy.visit('localhost:3000/');
  });

  it('Given file selected in dialog when user clicks on upload then .csv is uploaded and checked structurally', () => {
    cy.get('[data-cy=uploadDialogButton]').click();
    cy.get('[data-cy=fileInput]').attachFile('invalidCardInfo.xlsx');
    cy.get('[data-cy=fileButton]').click();
    cy.get('[data-cy=formatError]').should('be.visible');
    cy.get('[data-cy=requestError]').should('not.be.visible');
  });

  it('Given .csv is uploaded when structural check succeded then service call to update data is initiated to ESB (Interface RE-92)', () => {
    cy.get('[data-cy=uploadDialogButton]').click();
    cy.get('[data-cy=fileInput]').attachFile('cardInfo.xlsx');
    cy.get('[data-cy=fileButton]').click();
    cy.get('[data-cy=formatError]').should('not.be.visible');
    cy.get('[data-cy=requestError]').should('not.be.visible');
    cy.get('[data-cy=uploading]').should('exist');
  });
});
