/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Location', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify.jwt,
        refresh_token: users.Driver_Modify.jwt,
      }),
    );
    cy.visit(
      'localhost:3000/location',
    );
  });

  it('should render in modify Mode', () => {
    cy.get('[data-cy=modify]').should('exist');
  });

  it('es driver data country should be selected', () => {
    cy.get('.flag-icon-nl').should('exist');
  });

  it('country select should offer all the countries', () => {
    cy.get('[data-cy=countryInput]').click();
    cy.get('[data-cy=deSelect]').should('exist');
    cy.get('[data-cy=nlSelect]').should('exist');
  });
});
