/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('EV Driver Invitation', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager.jwt,
        refresh_token: users.Fleetmanager.jwt,
      }),
    );
  });

  it('Given user on card overview page when no E-Mail address is given then the Send Invitation button is deactivated', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(1).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]')
      .eq(1)
      .find('[data-cy=sendInvitation]')
      .should('be.disabled');
  });

  it('Given user has selected the language when user clicks on send invitation then send request is sent to Send invitation API', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(3).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]').eq(3).find('[data-cy=sendInvitation]').click();
    cy.get('[data-cy=sendData]').click();
    cy.get('[data-cy=successText]').should('be.visible');
  });
});
