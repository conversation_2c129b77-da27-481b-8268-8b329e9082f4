/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Welcome', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Welcome.jwt,
        refresh_token: users.Driver_Welcome.jwt,
      }),
    );
    cy.visit(
      'localhost:3000/',
    );
  });

  it('should render in initial Mode', () => {
    cy.get('[data-cy=initial]').should('exist');
  });

  it('default country should be selected', () => {
    cy.get('.flag-icon-de').should('exist');
  });

  it('country select should offer all the countries', () => {
    cy.get('[data-cy=countryInput]').click();
    cy.get('[data-cy=deSelect]').should('exist');
    cy.get('[data-cy=nlSelect]').should('exist');
  });
});
