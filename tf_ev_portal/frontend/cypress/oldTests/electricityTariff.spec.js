/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Electricity Tariff', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Welcome.jwt,
        refresh_token: users.Driver_Welcome.jwt,
      }),
    );
    cy.visit('localhost:3000/electricityTariff');
    //  Wait for Context to load
    cy.wait(500);
  });

  it('should render in initial Mode', () => {
    cy.get('[data-cy=initial]').should('exist');
  });

  it('work price should have up to 4 digits', () => {
    cy.get('[data-cy=currencyInput]').type(0.12456789);
    cy.get('[data-cy=currencyInput]').invoke('val').should('eq', '0.1245');
  });

  it('confirmButton should be disabled when work price not entered', () => {
    cy.get('#datePicker').click();
    cy.get('.MuiButton-textPrimary').last().should('have.text', 'OK').click();
    cy.get('[data-cy=fileInput]').attachFile({
      fileContent: 'foo',
      fileName: 'bar.pdf',
      mimeType: 'application/pdf',
    });
    cy.get('[data-cy=fileButton]').click();
    cy.get('#responsibiliyCheckBox').check();

    cy.get('[data-cy=confirmButton]').should('be.disabled');
    cy.get('[data-cy=currencyInput]').type(0.12);
    cy.get('[data-cy=confirmButton]').should('not.be.disabled');
  });

  it('confirmButton should be disabled when date not picked', () => {
    cy.get('[data-cy=currencyInput]').type(0.12);
    cy.get('[data-cy=fileInput]').attachFile({
      fileContent: 'foo',
      fileName: 'bar.pdf',
      mimeType: 'application/pdf',
    });
    cy.get('[data-cy=fileButton]').click();
    cy.get('#responsibiliyCheckBox').check();

    cy.get('[data-cy=confirmButton]').should('be.disabled');
    cy.get('#datePicker').click();
    cy.get('.MuiButton-textPrimary').last().should('have.text', 'OK').click();
    cy.get('[data-cy=confirmButton]').should('not.be.disabled');
  });

  it('confirmButton should be disabled when responsibility checkbox not checked', () => {
    cy.get('[data-cy=currencyInput]').type(0.12);
    cy.get('#datePicker').click();
    cy.get('.MuiButton-textPrimary').last().should('have.text', 'OK').click();
    cy.get('[data-cy=fileInput]').attachFile({
      fileContent: 'foo',
      fileName: 'bar.pdf',
      mimeType: 'application/pdf',
    });
    cy.get('[data-cy=fileButton]').click();

    cy.get('[data-cy=confirmButton]').should('be.disabled');
    cy.get('#responsibiliyCheckBox').check();
    cy.get('[data-cy=confirmButton]').should('not.be.disabled');
  });

  it('confirmButton should be disabled when data not uploaded', () => {
    cy.get('[data-cy=currencyInput]').type(0.12);
    cy.get('#datePicker').click();
    cy.get('.MuiButton-textPrimary').last().should('have.text', 'OK').click();
    cy.get('#responsibiliyCheckBox').check();
    cy.get('[data-cy=fileInput]').attachFile({
      fileContent: 'foo',
      fileName: 'bar.pdf',
      mimeType: 'application/pdf',
    });

    cy.get('[data-cy=confirmButton]').should('be.disabled');
    cy.get('[data-cy=fileButton]').click();
    cy.get('[data-cy=confirmButton]').should('not.be.disabled');
  });

  it('should be routed to bank data view when confirm button is clicked', () => {
    cy.get('[data-cy=currencyInput]').type(0.12);
    cy.get('#datePicker').click();
    cy.get('.MuiButton-textPrimary').last().should('have.text', 'OK').click();
    cy.get('#responsibiliyCheckBox').check();
    cy.get('[data-cy=fileInput]').attachFile({
      fileContent: 'foo',
      fileName: 'bar.pdf',
      mimeType: 'application/pdf',
    });
    cy.get('[data-cy=fileButton]').click();

    cy.get('[data-cy=confirmButton]').click();
    cy.location('pathname').should('eq', '/bankData');
  });
});
