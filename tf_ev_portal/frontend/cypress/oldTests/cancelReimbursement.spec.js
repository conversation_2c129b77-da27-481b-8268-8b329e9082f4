/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Cancel Reimbursement', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager.jwt,
        refresh_token: users.Fleetmanager.jwt,
      }),
    );
  });

  it('Given reimbursement status on card is active when user selects the card row then "Cancel Reimbursement" button is activated', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(0).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=cancelReimbursement]').should('be.disabled');
    cy.get('[data-cy=tableRow]').eq(6).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=cancelReimbursement]').should('be.enabled');
  });

  it('Given check overlay is presented when user clicks on "Yes, cancel reimbursement" then request to change status is sent to ESB', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(6).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=cancelReimbursement]').click();
    cy.get('[data-cy=dialog]').should('exist');
    cy.get('[data-cy=confirm]').click();
    cy.get('[data-cy=dialog]').should('not.exist');
  });
});
