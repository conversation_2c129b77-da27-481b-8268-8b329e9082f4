/// <reference types="cypress" />
import { users } from '../../src/mock/data';

context('Data Approval', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager_Direct.jwt,
        refresh_token: users.Fleetmanager_Direct.jwt,
      }),
    );
  });

  it('Given user is on approval page when user looks at the header section then he can find the card number', () => {
    cy.visit('localhost:3000/dataApproval?cardNumber=12345');
    cy.get('[data-cy=approval]').should('contain.text', '12345');
  });

  it('Given user is on approval page when user looks at the bank section he can find the last four digits of the drivers IBAN and the data entry date', () => {
    cy.visit('localhost:3000/dataApproval?cardNumber=12345');
    cy.get('[data-cy=approval]').should('exist');
    cy.get('[data-cy=bankDate]').should('contain.text', '2019-07-30');
    cy.get('[data-cy=iban]').should(
      'contain.text',
      '**** **** **** **** **80 15',
    );
  });

  it('Given selected card number corresponds to a wall box in germany when user looks at the electricity tariff section then he can find the work price and the valid from date and a button to download the electricity tariff', () => {
    cy.visit('localhost:3000/dataApproval?cardNumber=12345_DE');
    cy.get('[data-cy=approval]').should('exist');
    cy.get('[data-cy=price]').should('contain.text', '0.31');
    cy.get('[data-cy=tariffDate]').should('contain.text', '2020-01-20');
    cy.get('[data-cy=currentTariffDownload]').should('exist');
  });

  it('Given wall box is located in the netherlands then no electricity tariff section is presented to the user', () => {
    cy.visit('localhost:3000/dataApproval?cardNumber=12345_NL');
    cy.get('[data-cy=approval]').should('exist');
    cy.get('[data-cy=electricityTariff]').should('not.exist');
  });
});
