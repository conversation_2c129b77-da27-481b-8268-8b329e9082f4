/// <reference types="cypress" />

context('Login', () => {
  beforeEach(() => {
    cy.visit('localhost:3000/login');
  });

  it('enterButton should be disabled when user name not entered', () => {
    cy.get('[data-cy=passwordInput]').type('password');
    cy.get('[data-cy=enterButton]').should('be.disabled');

    cy.get('[data-cy=usernameInput]').type('username');
    cy.get('[data-cy=enterButton]').should('not.be.disabled');
  });

  it('enterButton should be disabled when password not entered', () => {
    cy.get('[data-cy=usernameInput]').type('username');
    cy.get('[data-cy=enterButton]').should('be.disabled');

    cy.get('[data-cy=passwordInput]').type('password');
    cy.get('[data-cy=enterButton]').should('not.be.disabled');
  });

  it('should be routed to home view when login is successful', () => {
    cy.get('[data-cy=usernameInput]').type('Driver_Welcome');
    cy.get('[data-cy=passwordInput]').type('password');

    cy.get('[data-cy=enterButton]').click();
    cy.location('pathname').should('eq', '/');
  });

  it('should be rejected and show invalid credentials hint when 401 is returned', () => {
    cy.get('[data-cy=usernameInput]').type('username');
    cy.get('[data-cy=passwordInput]').type('password');

    cy.get('[data-cy=enterButton]').click();
    cy.get('[data-cy=credentialsErrorMessage]').should('exist');
    cy.location('pathname').should('eq', '/login');
  });

  it('should be rejected and general login error hint when 500 is returned', () => {
    cy.get('[data-cy=usernameInput]').type('500');
    cy.get('[data-cy=passwordInput]').type('password');

    cy.get('[data-cy=enterButton]').click();
    cy.get('[data-cy=defaultErrorMessage]').should('exist');
    cy.location('pathname').should('eq', '/login');
  });

  it('should show error general error message when query param exists', () => {
    cy.visit('localhost:3000/login?error=true');
    cy.get('[data-cy=defaultErrorMessage]').should('exist');
  });
});
