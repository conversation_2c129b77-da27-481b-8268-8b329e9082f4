/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Single item update', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Fleetmanager.jwt,
        refresh_token: users.Fleetmanager.jwt,
      }),
    );
  });

  it('Given has added additional card information when the user clicks on save button then data set is stored and user is redirected to card overview', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(1).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]').eq(1).find('[data-cy=addFCData]').click();
    cy.get('[data-cy=driverMail]').type('<EMAIL>');
    cy.get('[data-cy=save]').click();
    cy.wait(5000);
    cy.location('pathname').should('eq', '/');
  });
  it('Given card overview is presented when the user data was already added then "Modify F&C card data" is active', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]')
      .eq(2)
      .find('[data-cy=modifyFCData]')
      .should('not.be.disabled');
  });
  it('Given user on Modify card page when user is not  invited then E-Mail address can be changed.', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(4).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]').eq(4).find('[data-cy=modifyFCData]').click();
    cy.get('[data-cy=driverMail]').should('be.not.disabled');
  });
  it('Given user on Modify card page when user is already invited then E-Mail address can not be changed anymore.', () => {
    cy.visit('localhost:3000/');
    cy.get('[data-cy=tableRow]').eq(4).find('[data-cy=expandButton]').click();
    cy.get('[data-cy=tableRow]').eq(4).find('[data-cy=modifyFCData]').click();
    cy.get('[data-cy=driverMail]').should('be.disabled');
  });
});
