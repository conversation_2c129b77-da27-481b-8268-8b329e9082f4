/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Summary', () => {
  it('Given user on summary page when driver looks at the wallbox section then he can find the entered wallbox location and the evse-id', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary_Nl.jwt,
        refresh_token: users.Driver_Summary_Nl.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('[data-cy=summary]').should('exist');
    cy.get('[data-cy=country]').should('have.text', 'NL');
    cy.get('[data-cy=evseId]').should('have.text', 'AT*EVN*E4109*4');
  });

  it(
    'Given user has a german wall box when user looks at the electricity tariff section then he can find the work ' +
      'price and the start date of the tariff and a button for the download of the electricity tariff',
    () => {
      localStorage.setItem('user-is-logged-in', true);
      localStorage.setItem(
        'user-token',
        JSON.stringify({
          access_token: users.Driver_Summary.jwt,
          refresh_token: users.Driver_Summary.jwt,
        }),
      );
      cy.visit('localhost:3000/summary');
      cy.get('[data-cy=summary]').should('exist');
      cy.get('[data-cy=price]').should('contain.text', '0.31');
      cy.get('[data-cy=tariffDate]').should('contain.text', '2020-01-20');
    },
  );

  it('Given user has a wall box in the netherlands when user looks at the electricity tariff section then this section is not shown', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary_Nl.jwt,
        refresh_token: users.Driver_Summary_Nl.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('[data-cy=summary]').should('exist');
    cy.get('[data-cy=electricityTariff]').should('not.exist');
  });

  it('Given user on summary page when user looks at the bank section then he can find the date of data entry and the last four digits of it´s IBAN number', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary.jwt,
        refresh_token: users.Driver_Summary.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('[data-cy=summary]').should('exist');
    cy.get('[data-cy=bankDate]').should('contain.text', '2019-07-30');
    cy.get('[data-cy=iban]').should(
      'contain.text',
      '**** **** **** **** **80 15',
    );
  });

  it('Given user openend up summary page when he looks at the action section then "Modify data" is initially activated and "Send data for approval" is deactivated', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary.jwt,
        refresh_token: users.Driver_Summary.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('[data-cy=summary]').should('exist');
    cy.get('[data-cy=modifyButton]').should('not.be.disabled');
    cy.get('[data-cy=sendButton]').should('be.disabled');
  });

  it('Given in confirmation section when user marks the confirmation checkbox then the "Send data for approval" button gets activated', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary.jwt,
        refresh_token: users.Driver_Summary.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('#confirmData').check();
    cy.get('[data-cy=modifyButton]').should('not.be.disabled');
    cy.get('[data-cy=sendButton]').should('not.be.disabled');
  });

  it(
    'Given in confirmation section when user clicks on "Send data for approval then the request to store the data ' +
      'is sent to the ESB and EV driver approval status is set to "initial_data_entered"',
    () => {
      localStorage.setItem('user-is-logged-in', true);
      localStorage.setItem(
        'user-token',
        JSON.stringify({
          access_token: users.Driver_Summary.jwt,
          refresh_token: users.Driver_Summary.jwt,
        }),
      );
      cy.visit('localhost:3000/summary');
      cy.get('[data-cy=summary]').should('exist');
      cy.get('#confirmData').check();
      cy.get('[data-cy=sendButton]').click();
      cy.wait(5000);
      cy.get('[data-cy=waitingPage]').should('exist');
    },
  );

  it('Given user has sent data for approval when data request was successful then a success popup is shown to the user', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Summary.jwt,
        refresh_token: users.Driver_Summary.jwt,
      }),
    );
    cy.visit('localhost:3000/summary');
    cy.get('[data-cy=summary]').should('exist');
    cy.get('#confirmData').check();
    cy.get('[data-cy=sendButton]').click();
    cy.get('[data-cy=successText]').should('be.visible');
  });
});
