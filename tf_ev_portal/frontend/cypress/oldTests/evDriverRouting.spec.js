/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('EV Driver Routing', () => {
  it('Given driver status returned when status is "invited", "initial_data_rejected" or "logged_in_once" then driver is routed to the welcome page', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Welcome.jwt,
        refresh_token: users.Driver_Welcome.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=initial]').should('exist');
  });

  it('Given driver status returned when status is either "initial_data_entered", "tariff_modified", "banking_modified" or "wallbox_modified" then driver is routed to Wait for fleet manager page', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Waiting.jwt,
        refresh_token: users.Driver_Waiting.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=waitingPage]').should('exist');
  });

  it('Given driver status returned when status is either "wallbox_update_requested" or "banking_update_requested" or "approved" then driver is routed to modify pag', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify.jwt,
        refresh_token: users.Driver_Modify.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyPage]').should('exist');
  });
});
