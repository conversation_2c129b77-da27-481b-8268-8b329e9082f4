/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Evse Id', () => {
  beforeEach(() => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Welcome.jwt,
        refresh_token: users.Driver_Welcome.jwt,
      }),
    );
  });

  it('should render in initial Mode', () => {
    cy.visit('localhost:3000/evseId');
    //  Wait for Context to load
    cy.wait(500);
    cy.get('[data-cy=initial]').should('exist');
  });

  it('confirmButton should be disabled when Evse Id not entered', () => {
    cy.visit('localhost:3000/evseId');
    //  Wait for Context to load
    cy.wait(500);
    cy.get('[data-cy=confirmButton]').should('be.disabled');
    cy.get('[data-cy=evseIdInput]').type('DE*TRA*EEVB**********13');
    cy.get('[data-cy=confirmButton]').should('not.be.disabled');
  });

  it('should be routed to electricity tariff view when Evse Id is entered and location is Germany', () => {
    cy.visit('localhost:3000/');
    //  Wait for Context to load
    cy.wait(500);
    cy.get('[data-cy=confirmButton]').click();
    cy.get('[data-cy=evseIdInput]').type('DE*TRA*EEVB**********13');

    cy.get('[data-cy=confirmButton]').click();
    cy.location('pathname').should('eq', '/electricityTariff');
  });

  it('should be routed to bank data view when Evse Id is entered and location is Netherlands', () => {
    cy.visit('localhost:3000/');
    //  Wait for Context to load
    cy.wait(500);
    cy.get('[data-cy=countryInput]').click();
    cy.get('[data-cy=nlSelect]').click();
    cy.get('[data-cy=confirmButton]').click();
    cy.get('[data-cy=evseIdInput]').type('DE*TRA*EEVB**********13');

    cy.get('[data-cy=confirmButton]').click();
    cy.location('pathname').should('eq', '/bankData');
  });
});
