/// <reference types="cypress" />
import { users } from '../../../src/mock/data';

context('Modify', () => {
  it('Given driver on modify page when driver looks at the different sections then modification is only possible for the type of data where an update was requested.', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify_Update_Requested.jwt,
        refresh_token: users.Driver_Modify_Update_Requested.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyTariff]').should('be.disabled');
    cy.get('[data-cy=revertTariff]').should('be.disabled');
  });

  it('Given driver in banking data section when user clicks on modify then a request is sent to Rapyd and the user is redirected to the provided URL on success', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify_De.jwt,
        refresh_token: users.Driver_Modify_De.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyBank]').click();
    cy.get('[data-cy=redirectToRapyd]').click();
    cy.get('[data-cy=iban]').should(
      'have.text',
      'IBAN: **** **** **** **** **99 99',
    );
  });

  it('Given user has modified his data when user checks the data approval checkbox then the "Send data for approval" button gets active', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify_De.jwt,
        refresh_token: users.Driver_Modify_De.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyBank]').click();
    cy.get('[data-cy=redirectToRapyd]').click();
    cy.get('[data-cy=sendButton]').should('be.disabled');
    cy.get('#confirmData').check();
    cy.get('[data-cy=sendButton]').should('not.be.disabled');
  });

  it('Given user has modified it´s data and checked the data correctness checkbox when user clicks on "Sand data for approval" then the request to update data is sent to ESB', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify_De.jwt,
        refresh_token: users.Driver_Modify_De.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyBank]').click();
    cy.get('[data-cy=redirectToRapyd]').click();
    cy.get('#confirmData').check();
    cy.get('[data-cy=sendButton]').click();
    cy.get('[data-cy=successText]').should('be.visible');
  });

  it('Given driver with wall box in the netherlands when user is on modify page then no electricity tariff section is shown to the driver', () => {
    localStorage.setItem('user-is-logged-in', true);
    localStorage.setItem(
      'user-token',
      JSON.stringify({
        access_token: users.Driver_Modify.jwt,
        refresh_token: users.Driver_Modify.jwt,
      }),
    );
    cy.visit('localhost:3000/');
    cy.get('[data-cy=modifyPage]').should('exist');
    cy.get('[data-cy=currentTariff]').should('not.exist');
    cy.get('[data-cy=additionalTariff]').should('not.exist');
  });
});
