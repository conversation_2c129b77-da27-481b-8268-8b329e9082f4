import users from '../../src/mock/data/users';
import Ev_Driver_Data from '../../src/mock/data/Ev_Driver_Data';
export async function initUser(name) {
  localStorage.setItem('user-is-logged-in', true);
  localStorage.setItem(
    'user-token',
    JSON.stringify({
      access_token: users[name].jwt,
      refresh_token: users[name].jwt,
    }),
  );
  await cy
    .intercept('GET', '/api/esb/Ev_Driver_Data/Get_Ev_Driver_Data', (req) => {
      req.reply({
        statusCode: 200,
        body: Ev_Driver_Data[name.toLowerCase()],
      });
    })
    .as('driverData');
  await cy
    .intercept(
      'POST',
      '/auth/realms/trafineo/protocol/openid-connect/token',
      (req) => {
        req.reply({
          statusCode: 200,
          body: {
            access_token: users[name].jwt,
            refresh_token: users[name].jwt,
          },
        });
      },
    )
    .as('token');

  await cy
    .intercept('GET', '/api/esb/reminder', (req) => {
      req.reply({
        statusCode: 200,
        body: {
          reminders: [
            {
              reminder_type: 'electricity_contract_update_reminder',
              last_reminder_sent: '2022-06-06',
              interval: 6,
            },
          ],
        },
      });
    })
    .as('reminder');
}
