{"name": "reimbursement-portal", "version": "0.1.0", "private": true, "resolutions": {"ini": "^1.3.6", "ssri": "^6.0.2"}, "dependencies": {"@babel/core": "^7.16.0", "@babel/eslint-parser": "^7.17.0", "@date-io/date-fns": "2.13.2", "@emotion/react": "^11.9.0", "@emotion/styled": "^11.8.1", "@material-ui/core": "^4.12.3", "@material-ui/icons": "^4.9.1", "@material-ui/styles": "^4.11.4", "@mui/icons-material": "^5.10.6", "@mui/material": "^5.2.3", "@mui/system": "^5.2.3", "@mui/x-date-pickers": "^5.0.10", "@pmmmwh/react-refresh-webpack-plugin": "^0.5.5", "@react-hook/window-size": "^3.1.1", "@react-pdf/renderer": "^3.1.3", "@svgr/webpack": "^6.2.1", "assert": "^2.0.0", "axios": "^0.27.2", "axios-auth-refresh": "^3.0.0", "babel-jest": "^27.4.2", "babel-loader": "^8.2.3", "babel-plugin-named-asset-import": "^0.3.8", "babel-preset-react-app": "^10.0.1", "browserify-zlib": "^0.2.0", "buffer": "^6.0.3", "camelcase": "^6.3.0", "case-sensitive-paths-webpack-plugin": "2.3.0", "classnames": "^2.2.6", "csp-html-webpack-plugin": "^5.1.0", "css-loader": "6.7.1", "css-minimizer-webpack-plugin": "^3.4.1", "date-fns": "^2.15.0", "dexie": "^3.2.1", "dexie-react-hooks": "^1.1.1", "dotenv": "8.2.0", "dotenv-expand": "5.1.0", "eslint": "^8.3.0", "eslint-config-react-app": "^7.0.1", "eslint-plugin-cypress": "^2.12.1", "eslint-webpack-plugin": "^3.1.1", "exceljs": "^4.4.0", "file-loader": "6.2.0", "file-saver": "^2.0.5", "form-data": "^4.0.0", "fs-extra": "^10.1.0", "history": "^5.3.0", "html-webpack-plugin": "^5.5.0", "i18next": "^21.6.16", "jest": "^28.0.3", "jest-environment-jsdom-fourteen": "^1.0.1", "jest-watch-typeahead": "1.1.0", "jwt-decode": "^3.1.2", "mini-css-extract-plugin": "2.6.0", "miragejs": "^0.1.47", "moment": "^2.29.3", "pdf-dist": "^1.0.0", "postcss-flexbugs-fixes": "4.1.0", "postcss-loader": "^6.2.1", "postcss-normalize": "8.0.1", "postcss-preset-env": "6.7.0", "postcss-safe-parser": "4.0.1", "process": "^0.11.10", "prop-types": "^15.7.2", "querystring": "^0.2.0", "react": "^18.1.0", "react-app-polyfill": "^3.0.0", "react-cookie-consent": "^8.0.1", "react-dev-utils": "^12.0.1", "react-device-detect": "^2.1.2", "react-dom": "^18.1.0", "react-ga4": "^2.1.0", "react-gtm-module": "^2.0.11", "react-i18next": "^11.7.0", "react-obfuscate": "^3.6.8", "react-pdf": "^6.2.2", "react-refresh": "^0.13.0", "react-router-dom": "6.3.0", "react-swipeable-views": "^0.14.0", "recharts": "^2.5.0", "resolve": "1.15.0", "resolve-url-loader": "^5.0.0", "sass-loader": "^12.6.0", "semver": "7.3.7", "source-map-loader": "^3.0.1", "stream-browserify": "^3.0.0", "style-loader": "3.3.1", "styled-components": "^5.1.1", "terser-webpack-plugin": "5.3.1", "ts-md5": "^1.3.1", "ts-pnp": "1.1.6", "typescript": "^4.9.4", "universal-cookie": "^6.0.1", "url": "^0.11.0", "util": "^0.12.4", "webpack": "^5.72.0", "webpack-dev-server": "^4.6.0", "webpack-manifest-plugin": "5.0.0", "workbox-webpack-plugin": "6.5.3", "xlsx": "^0.17.0"}, "devDependencies": {"@testing-library/jest-dom": "^5.16.4", "cypress": "^9.6.0", "cypress-file-upload": "^5.0.8", "dead-exports": "^1.0.13", "eslint-config-airbnb": "^19.0.4", "eslint-config-prettier": "^8.5.0", "eslint-plugin-react": "^7.32.0", "eslint-plugin-react-hooks": "^5.0.0-canary-59831c98c-20240218", "prettier": "^2.0.5", "start-server-and-test": "^1.11.7"}, "scripts": {"prestart": "yarn sync:lang", "start": "node scripts/start.js", "prestart:mock": "yarn sync:lang", "start:mock": "REACT_APP_USE_MOCK=true node scripts/start.js", "start:mockWin": "set REACT_APP_USE_MOCK=true&& node scripts/start.js", "start:demo": "REACT_APP_ENVIRONMENT=demo node scripts/start.js", "start:demoWin": "set REACT_APP_ENVIRONMENT=demo&& node scripts/start.js", "prebuild": "yarn sync:lang", "build": "GENERATE_SOURCEMAP=false NODE_ENV=production node scripts/build.js", "test": "REACT_APP_USE_MOCK=true start-server-and-test http-get://localhost:3000 cypress:run", "prelint": "yarn sync:lang", "lint": "eslint --fix ./src", "eject": "react-scripts eject", "sync:lang": "node download-translations.js", "cypress:run": "npx cypress run --spec cypress/integration/tests/*.spec.js", "cypress:open": "cypress open"}, "engines": {"node": "16"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": [">0.2%", "not dead", "not op_mini all"]}, "prettier": {"$schema": "http://json.schemastore.org/prettierrc", "printWidth": 80, "tabWidth": 2, "useTabs": false, "semi": true, "singleQuote": true, "trailingComma": "all", "endOfLine": "lf"}, "jest": {"roots": ["<rootDir>/src"], "collectCoverageFrom": ["src/**/*.{js,jsx,ts,tsx}", "!src/**/*.d.ts"], "setupFiles": ["react-app-polyfill/jsdom"], "setupFilesAfterEnv": ["<rootDir>/src/setupTests.js"], "testMatch": ["<rootDir>/src/**/__tests__/**/*.{js,jsx,ts,tsx}", "<rootDir>/src/**/*.{spec,test}.{js,jsx,ts,tsx}"], "testEnvironment": "jest-environment-jsdom-fourteen", "transform": {"^.+\\.(js|jsx|ts|tsx)$": "<rootDir>/node_modules/babel-jest", "^.+\\.css$": "<rootDir>/config/jest/cssTransform.js", "^(?!.*\\.(js|jsx|ts|tsx|css|json)$)": "<rootDir>/config/jest/fileTransform.js"}, "transformIgnorePatterns": ["[/\\\\]node_modules[/\\\\].+\\.(js|jsx|ts|tsx)$", "^.+\\.module\\.(css|sass|scss)$"], "modulePaths": [], "moduleNameMapper": {"^react-native$": "react-native-web", "^.+\\.module\\.(css|sass|scss)$": "identity-obj-proxy"}, "moduleFileExtensions": ["web.js", "js", "web.ts", "ts", "web.tsx", "tsx", "json", "web.jsx", "jsx", "node"], "watchPlugins": ["jest-watch-typeahead/filename", "jest-watch-typeahead/testname"]}, "eslintConfig": {"extends": "react-app"}}