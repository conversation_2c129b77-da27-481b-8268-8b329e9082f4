# Trafineo Reimbursement Portal

This is a monorepo of multiple components.

`/frontend` holds the webapp (React), all information needed is in its own readme in this folder

`/auth` is a helper nodeJs app that provides logic for the nginx that serves the static files from the frontend app

`/nginx` includes the configs needed for the nginx

`/dev-proxy` is a small proxy needed for local development

## Prequisites

The following Tools need to be installed for:

Frontend Development:

- NodeJs version 12
- Yarn Package Manager

## Usage

### Frontend development

- `yarn install` will install all needed dependencies
- `yarn dev` will start the frontend development server as well as the development proxy to talk to all the apis

### Run NGINX locally
```bash
docker build -t nginx:local .
docker run --rm -v $(pwd)/nginx/certs:/opt/nginx/certs -v $(pwd)/nginx/nginx.conf:/opt/nginx/nginx.conf -v $(pwd)/nginx/server.conf:/opt/nginx/server.conf -v $(pwd)/nginx/auth.js:/opt/nginx/auth.js --env-file develop.env -p 8443:8443 --network-alias --name nginx nginx:local
```


